/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/[category]/[slug]/page";
exports.ids = ["app/[category]/[slug]/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("fs/promises");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Bcategory%5D%2F%5Bslug%5D%2Fpage&page=%2F%5Bcategory%5D%2F%5Bslug%5D%2Fpage&appPaths=%2F%5Bcategory%5D%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bcategory%5D%2F%5Bslug%5D%2Fpage.tsx&appDir=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Bcategory%5D%2F%5Bslug%5D%2Fpage&page=%2F%5Bcategory%5D%2F%5Bslug%5D%2Fpage&appPaths=%2F%5Bcategory%5D%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bcategory%5D%2F%5Bslug%5D%2Fpage.tsx&appDir=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '[category]',\n        {\n        children: [\n        '[slug]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[category]/[slug]/page.tsx */ \"(rsc)/./app/[category]/[slug]/page.tsx\")), \"/Users/<USER>/Documents/augment-projects/Personal_website/app/[category]/[slug]/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/Users/<USER>/Documents/augment-projects/Personal_website/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/augment-projects/Personal_website/app/[category]/[slug]/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/[category]/[slug]/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/[category]/[slug]/page\",\n        pathname: \"/[category]/[slug]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkYlNUJjYXRlZ29yeSU1RCUyRiU1QnNsdWclNUQlMkZwYWdlJnBhZ2U9JTJGJTVCY2F0ZWdvcnklNUQlMkYlNUJzbHVnJTVEJTJGcGFnZSZhcHBQYXRocz0lMkYlNUJjYXRlZ29yeSU1RCUyRiU1QnNsdWclNUQlMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGJTVCY2F0ZWdvcnklNUQlMkYlNUJzbHVnJTVEJTJGcGFnZS50c3gmYXBwRGlyPSUyRlVzZXJzJTJGY2hhdW1pbmhraGFpJTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRlBlcnNvbmFsX3dlYnNpdGUlMkZhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPSUyRlVzZXJzJTJGY2hhdW1pbmhraGFpJTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRlBlcnNvbmFsX3dlYnNpdGUmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBb0c7QUFDckM7QUFDL0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDLHVCQUF1Qiw0S0FBbUk7QUFDMUo7QUFDQSxTQUFTO0FBQ1QsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBLHlCQUF5Qiw0SUFBbUg7QUFDNUksb0JBQW9CLDBOQUFnRjtBQUNwRztBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDdUI7QUFDNkQ7QUFDcEYsNkJBQTZCLG1CQUFtQjtBQUNoRDtBQUNPO0FBQ0E7QUFDUDtBQUNBO0FBQ0E7QUFDdUQ7QUFDdkQ7QUFDTyx3QkFBd0IsOEdBQWtCO0FBQ2pEO0FBQ0EsY0FBYyx5RUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsQ0FBQzs7QUFFRCIsInNvdXJjZXMiOlsid2VicGFjazovL3BlcnNvbmFsLXdlYnNpdGUvP2QyZWUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUGFnZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvYXBwLXBhZ2UvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUta2luZFwiO1xuLy8gV2UgaW5qZWN0IHRoZSB0cmVlIGFuZCBwYWdlcyBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgdHJlZSA9IHtcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgJycsXG4gICAgICAgIHtcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgJ1tjYXRlZ29yeV0nLFxuICAgICAgICB7XG4gICAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgICdbc2x1Z10nLFxuICAgICAgICB7XG4gICAgICAgIGNoaWxkcmVuOiBbJ19fUEFHRV9fJywge30sIHtcbiAgICAgICAgICBwYWdlOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvY2hhdW1pbmhraGFpL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL1BlcnNvbmFsX3dlYnNpdGUvYXBwL1tjYXRlZ29yeV0vW3NsdWddL3BhZ2UudHN4XCIpLCBcIi9Vc2Vycy9jaGF1bWluaGtoYWkvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvUGVyc29uYWxfd2Vic2l0ZS9hcHAvW2NhdGVnb3J5XS9bc2x1Z10vcGFnZS50c3hcIl0sXG4gICAgICAgICAgXG4gICAgICAgIH1dXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgIFxuICAgICAgICBcbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgXG4gICAgICAgIFxuICAgICAgfVxuICAgICAgXVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAnbGF5b3V0JzogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2NoYXVtaW5oa2hhaS9Eb2N1bWVudHMvYXVnbWVudC1wcm9qZWN0cy9QZXJzb25hbF93ZWJzaXRlL2FwcC9sYXlvdXQudHN4XCIpLCBcIi9Vc2Vycy9jaGF1bWluaGtoYWkvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvUGVyc29uYWxfd2Vic2l0ZS9hcHAvbGF5b3V0LnRzeFwiXSxcbidub3QtZm91bmQnOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCIpLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIl0sXG4gICAgICAgIFxuICAgICAgfVxuICAgICAgXVxuICAgICAgfS5jaGlsZHJlbjtcbmNvbnN0IHBhZ2VzID0gW1wiL1VzZXJzL2NoYXVtaW5oa2hhaS9Eb2N1bWVudHMvYXVnbWVudC1wcm9qZWN0cy9QZXJzb25hbF93ZWJzaXRlL2FwcC9bY2F0ZWdvcnldL1tzbHVnXS9wYWdlLnRzeFwiXTtcbmV4cG9ydCB7IHRyZWUsIHBhZ2VzIH07XG5leHBvcnQgeyBkZWZhdWx0IGFzIEdsb2JhbEVycm9yIH0gZnJvbSBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9lcnJvci1ib3VuZGFyeVwiO1xuY29uc3QgX19uZXh0X2FwcF9yZXF1aXJlX18gPSBfX3dlYnBhY2tfcmVxdWlyZV9fXG5jb25zdCBfX25leHRfYXBwX2xvYWRfY2h1bmtfXyA9ICgpID0+IFByb21pc2UucmVzb2x2ZSgpXG5leHBvcnQgY29uc3Qgb3JpZ2luYWxQYXRobmFtZSA9IFwiL1tjYXRlZ29yeV0vW3NsdWddL3BhZ2VcIjtcbmV4cG9ydCBjb25zdCBfX25leHRfYXBwX18gPSB7XG4gICAgcmVxdWlyZTogX19uZXh0X2FwcF9yZXF1aXJlX18sXG4gICAgbG9hZENodW5rOiBfX25leHRfYXBwX2xvYWRfY2h1bmtfX1xufTtcbmV4cG9ydCAqIGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvZW50cnktYmFzZVwiO1xuLy8gQ3JlYXRlIGFuZCBleHBvcnQgdGhlIHJvdXRlIG1vZHVsZSB0aGF0IHdpbGwgYmUgY29uc3VtZWQuXG5leHBvcnQgY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUGFnZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUEFHRSxcbiAgICAgICAgcGFnZTogXCIvW2NhdGVnb3J5XS9bc2x1Z10vcGFnZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvW2NhdGVnb3J5XS9bc2x1Z11cIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiBcIlwiLFxuICAgICAgICBmaWxlbmFtZTogXCJcIixcbiAgICAgICAgYXBwUGF0aHM6IFtdXG4gICAgfSxcbiAgICB1c2VybGFuZDoge1xuICAgICAgICBsb2FkZXJUcmVlOiB0cmVlXG4gICAgfVxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1wYWdlLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Bcategory%5D%2F%5Bslug%5D%2Fpage&page=%2F%5Bcategory%5D%2F%5Bslug%5D%2Fpage&appPaths=%2F%5Bcategory%5D%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bcategory%5D%2F%5Bslug%5D%2Fpage.tsx&appDir=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website%2Fapp%2Fglobals.css&modules=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website%2Fapp%2Fglobals.css&modules=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZjaGF1bWluaGtoYWklMkZEb2N1bWVudHMlMkZhdWdtZW50LXByb2plY3RzJTJGUGVyc29uYWxfd2Vic2l0ZSUyRmFwcCUyRmdsb2JhbHMuY3NzJm1vZHVsZXM9JTJGVXNlcnMlMkZjaGF1bWluaGtoYWklMkZEb2N1bWVudHMlMkZhdWdtZW50LXByb2plY3RzJTJGUGVyc29uYWxfd2Vic2l0ZSUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGbGluay5qcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wZXJzb25hbC13ZWJzaXRlLz8wNjQxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2NoYXVtaW5oa2hhaS9Eb2N1bWVudHMvYXVnbWVudC1wcm9qZWN0cy9QZXJzb25hbF93ZWJzaXRlL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2xpbmsuanNcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website%2Fapp%2Fglobals.css&modules=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fimage-component.js&modules=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website%2Fnode_modules%2Fnext%2Fdist%2Fshared%2Flib%2Flazy-dynamic%2Fdynamic-no-ssr.js&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fimage-component.js&modules=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website%2Fnode_modules%2Fnext%2Fdist%2Fshared%2Flib%2Flazy-dynamic%2Fdynamic-no-ssr.js&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(ssr)/./node_modules/next/dist/client/image-component.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-no-ssr.js */ \"(ssr)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-no-ssr.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZjaGF1bWluaGtoYWklMkZEb2N1bWVudHMlMkZhdWdtZW50LXByb2plY3RzJTJGUGVyc29uYWxfd2Vic2l0ZSUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGaW1hZ2UtY29tcG9uZW50LmpzJm1vZHVsZXM9JTJGVXNlcnMlMkZjaGF1bWluaGtoYWklMkZEb2N1bWVudHMlMkZhdWdtZW50LXByb2plY3RzJTJGUGVyc29uYWxfd2Vic2l0ZSUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGc2hhcmVkJTJGbGliJTJGbGF6eS1keW5hbWljJTJGZHluYW1pYy1uby1zc3IuanMmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHNOQUFxSjtBQUNySiIsInNvdXJjZXMiOlsid2VicGFjazovL3BlcnNvbmFsLXdlYnNpdGUvP2VlMzEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvY2hhdW1pbmhraGFpL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL1BlcnNvbmFsX3dlYnNpdGUvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvaW1hZ2UtY29tcG9uZW50LmpzXCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvY2hhdW1pbmhraGFpL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL1BlcnNvbmFsX3dlYnNpdGUvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9zaGFyZWQvbGliL2xhenktZHluYW1pYy9keW5hbWljLW5vLXNzci5qc1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fimage-component.js&modules=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website%2Fnode_modules%2Fnext%2Fdist%2Fshared%2Flib%2Flazy-dynamic%2Fdynamic-no-ssr.js&server=true!\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8db76b42b655\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wZXJzb25hbC13ZWJzaXRlLy4vYXBwL2dsb2JhbHMuY3NzP2MxZTAiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4ZGI3NmI0MmI2NTVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/[category]/[slug]/page.tsx":
/*!****************************************!*\
  !*** ./app/[category]/[slug]/page.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ArticlePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(rsc)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/storage */ \"(rsc)/./lib/storage.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./lib/utils.ts\");\n\n\n\n\n\nasync function getArticle(category, slug) {\n    if (!(0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.isValidCategory)(category)) {\n        return null;\n    }\n    try {\n        const article = await _lib_storage__WEBPACK_IMPORTED_MODULE_3__.articleStorage.getBySlug(category, slug);\n        return article;\n    } catch (error) {\n        console.error(\"Error fetching article:\", error);\n        return null;\n    }\n}\nasync function ArticlePage({ params }) {\n    const article = await getArticle(params.category, params.slug);\n    if (!article) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.notFound)();\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto px-4 py-12\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-amber-700 uppercase\",\n                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.getCategoryDisplayName)(article.category)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/app/[category]/[slug]/page.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-serif mt-2\",\n                        children: article.title\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/app/[category]/[slug]/page.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500 mt-2\",\n                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatDate)(article.publishedAt)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/app/[category]/[slug]/page.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4 mt-4 text-sm text-gray-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"By \",\n                                    article.author.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/app/[category]/[slug]/page.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 11\n                            }, this),\n                            article.readingTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    article.readingTime,\n                                    \" min read\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/app/[category]/[slug]/page.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 35\n                            }, this),\n                            article.viewCount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    article.viewCount,\n                                    \" views\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/app/[category]/[slug]/page.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 33\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/app/[category]/[slug]/page.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/app/[category]/[slug]/page.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            article.featuredImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                src: article.featuredImage,\n                alt: article.title,\n                width: 1000,\n                height: 600,\n                className: \"w-full h-auto rounded-lg mb-8\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/app/[category]/[slug]/page.tsx\",\n                lineNumber: 46,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"prose max-w-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-gray-50 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-700 italic\",\n                            children: article.excerpt\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/app/[category]/[slug]/page.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/app/[category]/[slug]/page.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"whitespace-pre-wrap\",\n                        children: article.content\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/app/[category]/[slug]/page.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this),\n                    article.tags && article.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 pt-8 border-t border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-4\",\n                                children: \"Tags\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/app/[category]/[slug]/page.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-2\",\n                                children: article.tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-3 py-1 bg-amber-100 text-amber-800 rounded-full text-sm\",\n                                        children: tag\n                                    }, index, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/app/[category]/[slug]/page.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/app/[category]/[slug]/page.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/app/[category]/[slug]/page.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/app/[category]/[slug]/page.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/app/[category]/[slug]/page.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/[category]/[slug]/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Navbar */ \"(rsc)/./components/Navbar.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Footer */ \"(rsc)/./components/Footer.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"Your Personal Website\",\n    description: \"A personal website with various topic categories\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"bg-stone-100\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/app/layout.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/app/layout.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/app/layout.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/app/layout.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/app/layout.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUNzQjtBQUNrQjtBQUNBO0FBRWpDLE1BQU1FLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVU7OzhCQUNkLDhEQUFDViwwREFBTUE7Ozs7OzhCQUNQLDhEQUFDVzs4QkFBTUw7Ozs7Ozs4QkFDUCw4REFBQ0wsMERBQU1BOzs7Ozs7Ozs7Ozs7Ozs7O0FBSWYiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wZXJzb25hbC13ZWJzaXRlLy4vYXBwL2xheW91dC50c3g/OTk4OCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcbmltcG9ydCBOYXZiYXIgZnJvbSAnQC9jb21wb25lbnRzL05hdmJhcidcbmltcG9ydCBGb290ZXIgZnJvbSAnQC9jb21wb25lbnRzL0Zvb3RlcidcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdZb3VyIFBlcnNvbmFsIFdlYnNpdGUnLFxuICBkZXNjcmlwdGlvbjogJ0EgcGVyc29uYWwgd2Vic2l0ZSB3aXRoIHZhcmlvdXMgdG9waWMgY2F0ZWdvcmllcycsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPVwiYmctc3RvbmUtMTAwXCI+XG4gICAgICAgIDxOYXZiYXIgLz5cbiAgICAgICAgPG1haW4+e2NoaWxkcmVufTwvbWFpbj5cbiAgICAgICAgPEZvb3RlciAvPlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKVxufSJdLCJuYW1lcyI6WyJOYXZiYXIiLCJGb290ZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiLCJtYWluIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/Footer.tsx":
/*!*******************************!*\
  !*** ./components/Footer.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FaFacebook_FaInstagram_FaLinkedin_FaPinterest_react_icons_fa__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FaFacebook,FaInstagram,FaLinkedin,FaPinterest!=!react-icons/fa */ \"(rsc)/./node_modules/react-icons/fa/index.esm.js\");\n\n\n\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-800 text-white py-12 mt-16\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-serif mb-4\",\n                                    children: \"Your Name \\xa9\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                                    lineNumber: 10,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300\",\n                                    children: \"A personal website exploring various topics and sharing insights on life, growth, and discovery.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                                    lineNumber: 11,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                            lineNumber: 9,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-semibold mb-4\",\n                                    children: \"Categories\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                                    lineNumber: 17,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-gray-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/history\",\n                                                className: \"hover:text-white\",\n                                                children: \"History\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                                                lineNumber: 19,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                                            lineNumber: 19,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/business\",\n                                                className: \"hover:text-white\",\n                                                children: \"Business\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                                                lineNumber: 20,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                                            lineNumber: 20,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/technology\",\n                                                className: \"hover:text-white\",\n                                                children: \"Technology\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                                                lineNumber: 21,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                                            lineNumber: 21,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/politics\",\n                                                className: \"hover:text-white\",\n                                                children: \"Politics\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                                                lineNumber: 22,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                                            lineNumber: 22,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/art\",\n                                                className: \"hover:text-white\",\n                                                children: \"Art\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                                                lineNumber: 23,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                                            lineNumber: 23,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/social-life\",\n                                                className: \"hover:text-white\",\n                                                children: \"Social Life\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                                                lineNumber: 24,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                                            lineNumber: 24,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                                    lineNumber: 18,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-semibold mb-4\",\n                                    children: \"Quick Links\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-gray-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/about\",\n                                                className: \"hover:text-white\",\n                                                children: \"About\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                                                lineNumber: 31,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                                            lineNumber: 31,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/contact\",\n                                                className: \"hover:text-white\",\n                                                children: \"Contact\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                                                lineNumber: 32,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                                            lineNumber: 32,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/subscribe\",\n                                                className: \"hover:text-white\",\n                                                children: \"Subscribe\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                                                lineNumber: 33,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                                            lineNumber: 33,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/privacy\",\n                                                className: \"hover:text-white\",\n                                                children: \"Privacy Policy\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                                                lineNumber: 34,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                                            lineNumber: 34,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-semibold mb-4\",\n                                    children: \"Follow Me\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"#\",\n                                            \"aria-label\": \"Instagram\",\n                                            className: \"text-gray-300 hover:text-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaFacebook_FaInstagram_FaLinkedin_FaPinterest_react_icons_fa__WEBPACK_IMPORTED_MODULE_2__.FaInstagram, {\n                                                size: 24\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                                                lineNumber: 42,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                                            lineNumber: 41,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"#\",\n                                            \"aria-label\": \"Facebook\",\n                                            className: \"text-gray-300 hover:text-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaFacebook_FaInstagram_FaLinkedin_FaPinterest_react_icons_fa__WEBPACK_IMPORTED_MODULE_2__.FaFacebook, {\n                                                size: 24\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                                                lineNumber: 45,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"#\",\n                                            \"aria-label\": \"LinkedIn\",\n                                            className: \"text-gray-300 hover:text-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaFacebook_FaInstagram_FaLinkedin_FaPinterest_react_icons_fa__WEBPACK_IMPORTED_MODULE_2__.FaLinkedin, {\n                                                size: 24\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                                                lineNumber: 48,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"#\",\n                                            \"aria-label\": \"Pinterest\",\n                                            className: \"text-gray-300 hover:text-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaFacebook_FaInstagram_FaLinkedin_FaPinterest_react_icons_fa__WEBPACK_IMPORTED_MODULE_2__.FaPinterest, {\n                                                size: 24\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                                                lineNumber: 51,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-700 mt-8 pt-8 text-center text-gray-300\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"\\xa9 2024 Your Name. All rights reserved.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Footer.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/Footer.tsx\n");

/***/ }),

/***/ "(rsc)/./components/Navbar.tsx":
/*!*******************************!*\
  !*** ./components/Navbar.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FaFacebook_FaInstagram_FaLinkedin_FaPinterest_react_icons_fa__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FaFacebook,FaInstagram,FaLinkedin,FaPinterest!=!react-icons/fa */ \"(rsc)/./node_modules/react-icons/fa/index.esm.js\");\n\n\n\nconst categories = [\n    {\n        name: \"Home\",\n        path: \"/\"\n    },\n    {\n        name: \"History\",\n        path: \"/history\"\n    },\n    {\n        name: \"Business\",\n        path: \"/business\"\n    },\n    {\n        name: \"Technology\",\n        path: \"/technology\"\n    },\n    {\n        name: \"Politics\",\n        path: \"/politics\"\n    },\n    {\n        name: \"Art\",\n        path: \"/art\"\n    },\n    {\n        name: \"Social Life\",\n        path: \"/social-life\"\n    }\n];\nfunction Navbar() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"py-4 px-6 flex justify-between items-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-2xl font-serif italic\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    href: \"/\",\n                    children: \"Your Name \\xa9\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Navbar.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Navbar.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden md:flex space-x-6\",\n                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: category.path,\n                        className: \"text-gray-700 hover:text-gray-900\",\n                        children: category.name\n                    }, category.path, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Navbar.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Navbar.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: \"#\",\n                        \"aria-label\": \"Instagram\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaFacebook_FaInstagram_FaLinkedin_FaPinterest_react_icons_fa__WEBPACK_IMPORTED_MODULE_2__.FaInstagram, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Navbar.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 47\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Navbar.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: \"#\",\n                        \"aria-label\": \"Facebook\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaFacebook_FaInstagram_FaLinkedin_FaPinterest_react_icons_fa__WEBPACK_IMPORTED_MODULE_2__.FaFacebook, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Navbar.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 46\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Navbar.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: \"#\",\n                        \"aria-label\": \"LinkedIn\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaFacebook_FaInstagram_FaLinkedin_FaPinterest_react_icons_fa__WEBPACK_IMPORTED_MODULE_2__.FaLinkedin, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Navbar.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 46\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Navbar.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: \"#\",\n                        \"aria-label\": \"Pinterest\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaFacebook_FaInstagram_FaLinkedin_FaPinterest_react_icons_fa__WEBPACK_IMPORTED_MODULE_2__.FaPinterest, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Navbar.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 47\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Navbar.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: \"/subscribe\",\n                        className: \"ml-4 px-4 py-2 border border-gray-700 text-gray-700 hover:bg-gray-700 hover:text-white transition\",\n                        children: \"SUBSCRIBE\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Navbar.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Navbar.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Personal_website/components/Navbar.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/Navbar.tsx\n");

/***/ }),

/***/ "(rsc)/./lib/storage.ts":
/*!************************!*\
  !*** ./lib/storage.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   articleStorage: () => (/* binding */ articleStorage)\n/* harmony export */ });\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils */ \"(rsc)/./lib/utils.ts\");\n\n\n\nconst DATA_DIR = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"data\");\nconst ARTICLES_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(DATA_DIR, \"articles.json\");\nclass JSONArticleStorage {\n    async ensureDataDir() {\n        try {\n            await fs_promises__WEBPACK_IMPORTED_MODULE_0___default().access(DATA_DIR);\n        } catch  {\n            await fs_promises__WEBPACK_IMPORTED_MODULE_0___default().mkdir(DATA_DIR, {\n                recursive: true\n            });\n        }\n    }\n    async loadArticles() {\n        await this.ensureDataDir();\n        try {\n            const data = await fs_promises__WEBPACK_IMPORTED_MODULE_0___default().readFile(ARTICLES_FILE, \"utf-8\");\n            return JSON.parse(data);\n        } catch  {\n            return [];\n        }\n    }\n    async saveArticles(articles) {\n        await this.ensureDataDir();\n        await fs_promises__WEBPACK_IMPORTED_MODULE_0___default().writeFile(ARTICLES_FILE, JSON.stringify(articles, null, 2));\n    }\n    generateId() {\n        return Date.now().toString(36) + Math.random().toString(36).substr(2);\n    }\n    filterArticles(articles, params = {}) {\n        let filtered = [\n            ...articles\n        ];\n        // Filter by status (default to published for public queries)\n        if (params.status) {\n            filtered = filtered.filter((article)=>article.status === params.status);\n        } else {\n            filtered = filtered.filter((article)=>article.status === \"published\");\n        }\n        // Filter by category\n        if (params.category) {\n            filtered = filtered.filter((article)=>article.category === params.category);\n        }\n        // Filter by search term\n        if (params.search) {\n            const searchTerm = params.search.toLowerCase();\n            filtered = filtered.filter((article)=>article.title.toLowerCase().includes(searchTerm) || article.excerpt.toLowerCase().includes(searchTerm) || article.content.toLowerCase().includes(searchTerm) || article.tags?.some((tag)=>tag.toLowerCase().includes(searchTerm)));\n        }\n        // Sort articles\n        const sortBy = params.sortBy || \"publishedAt\";\n        const sortOrder = params.sortOrder || \"desc\";\n        filtered.sort((a, b)=>{\n            let aValue = a[sortBy];\n            let bValue = b[sortBy];\n            if (sortBy === \"publishedAt\") {\n                aValue = new Date(aValue).getTime();\n                bValue = new Date(bValue).getTime();\n            }\n            if (sortOrder === \"asc\") {\n                return aValue > bValue ? 1 : -1;\n            } else {\n                return aValue < bValue ? 1 : -1;\n            }\n        });\n        return filtered;\n    }\n    paginateResults(items, page = 1, limit = 10) {\n        const total = items.length;\n        const totalPages = Math.ceil(total / limit);\n        const startIndex = (page - 1) * limit;\n        const endIndex = startIndex + limit;\n        const paginatedItems = items.slice(startIndex, endIndex);\n        return {\n            success: true,\n            data: paginatedItems,\n            pagination: {\n                page,\n                limit,\n                total,\n                totalPages,\n                hasNext: page < totalPages,\n                hasPrev: page > 1\n            }\n        };\n    }\n    async getAll(params = {}) {\n        const articles = await this.loadArticles();\n        const filtered = this.filterArticles(articles, params);\n        return this.paginateResults(filtered, params.page, params.limit);\n    }\n    async getByCategory(category, params = {}) {\n        const articles = await this.loadArticles();\n        const filtered = this.filterArticles(articles, {\n            ...params,\n            category\n        });\n        return this.paginateResults(filtered, params.page, params.limit);\n    }\n    async getBySlug(category, slug) {\n        const articles = await this.loadArticles();\n        const article = articles.find((a)=>a.category === category && a.slug === slug && a.status === \"published\");\n        if (article) {\n            // Increment view count\n            article.viewCount = (article.viewCount || 0) + 1;\n            await this.saveArticles(articles);\n        }\n        return article || null;\n    }\n    async getById(id) {\n        const articles = await this.loadArticles();\n        return articles.find((a)=>a.id === id) || null;\n    }\n    async create(input) {\n        const articles = await this.loadArticles();\n        const now = new Date().toISOString();\n        const article = {\n            id: this.generateId(),\n            ...input,\n            slug: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.generateSlug)(input.title),\n            status: input.status || \"draft\",\n            publishedAt: input.status === \"published\" ? now : \"\",\n            lastModified: now,\n            createdAt: now,\n            readingTime: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.calculateReadingTime)(input.content),\n            viewCount: 0\n        };\n        articles.push(article);\n        await this.saveArticles(articles);\n        return article;\n    }\n    async update(id, updates) {\n        const articles = await this.loadArticles();\n        const index = articles.findIndex((a)=>a.id === id);\n        if (index === -1) {\n            throw new Error(\"Article not found\");\n        }\n        const article = articles[index];\n        const now = new Date().toISOString();\n        // Update fields\n        Object.assign(article, updates);\n        article.lastModified = now;\n        // Update slug if title changed\n        if (updates.title) {\n            article.slug = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.generateSlug)(updates.title);\n        }\n        // Update reading time if content changed\n        if (updates.content) {\n            article.readingTime = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.calculateReadingTime)(updates.content);\n        }\n        // Set published date if status changed to published\n        if (updates.status === \"published\" && !article.publishedAt) {\n            article.publishedAt = now;\n        }\n        await this.saveArticles(articles);\n        return article;\n    }\n    async delete(id) {\n        const articles = await this.loadArticles();\n        const index = articles.findIndex((a)=>a.id === id);\n        if (index === -1) {\n            return false;\n        }\n        articles.splice(index, 1);\n        await this.saveArticles(articles);\n        return true;\n    }\n    async getCategoryStats() {\n        const articles = await this.loadArticles();\n        const publishedArticles = articles.filter((a)=>a.status === \"published\");\n        const categories = [\n            \"history\",\n            \"business\",\n            \"technology\",\n            \"politics\",\n            \"art\",\n            \"social-life\"\n        ];\n        return categories.map((category)=>({\n                name: category,\n                displayName: category.charAt(0).toUpperCase() + category.slice(1).replace(\"-\", \" \"),\n                description: `Articles about ${category.replace(\"-\", \" \")}`,\n                articleCount: publishedArticles.filter((a)=>a.category === category).length\n            }));\n    }\n}\nconst articleStorage = new JSONArticleStorage();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/storage.ts\n");

/***/ }),

/***/ "(rsc)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateReadingTime: () => (/* binding */ calculateReadingTime),\n/* harmony export */   createApiResponse: () => (/* binding */ createApiResponse),\n/* harmony export */   createMetaDescription: () => (/* binding */ createMetaDescription),\n/* harmony export */   extractExcerpt: () => (/* binding */ extractExcerpt),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDateISO: () => (/* binding */ formatDateISO),\n/* harmony export */   generateApiKey: () => (/* binding */ generateApiKey),\n/* harmony export */   generateSlug: () => (/* binding */ generateSlug),\n/* harmony export */   getCategoryDisplayName: () => (/* binding */ getCategoryDisplayName),\n/* harmony export */   handleApiError: () => (/* binding */ handleApiError),\n/* harmony export */   isValidCategory: () => (/* binding */ isValidCategory),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   parseQueryParams: () => (/* binding */ parseQueryParams),\n/* harmony export */   sanitizeHtml: () => (/* binding */ sanitizeHtml),\n/* harmony export */   truncateText: () => (/* binding */ truncateText),\n/* harmony export */   validateSlugFormat: () => (/* binding */ validateSlugFormat)\n/* harmony export */ });\n/**\n * Generate a URL-friendly slug from a title\n */ function generateSlug(title) {\n    return title.toLowerCase().trim().replace(/[^\\w\\s-]/g, \"\") // Remove special characters\n    .replace(/[\\s_-]+/g, \"-\") // Replace spaces and underscores with hyphens\n    .replace(/^-+|-+$/g, \"\"); // Remove leading/trailing hyphens\n}\n/**\n * Calculate estimated reading time in minutes\n */ function calculateReadingTime(content) {\n    const wordsPerMinute = 200; // Average reading speed\n    const wordCount = content.trim().split(/\\s+/).length;\n    const readingTime = Math.ceil(wordCount / wordsPerMinute);\n    return Math.max(1, readingTime); // Minimum 1 minute\n}\n/**\n * Validate if a string is a valid article category\n */ function isValidCategory(category) {\n    const validCategories = [\n        \"history\",\n        \"business\",\n        \"technology\",\n        \"politics\",\n        \"art\",\n        \"social-life\"\n    ];\n    return validCategories.includes(category);\n}\n/**\n * Get display name for a category\n */ function getCategoryDisplayName(category) {\n    const displayNames = {\n        \"history\": \"History\",\n        \"business\": \"Business\",\n        \"technology\": \"Technology\",\n        \"politics\": \"Politics\",\n        \"art\": \"Art\",\n        \"social-life\": \"Social Life\"\n    };\n    return displayNames[category];\n}\n/**\n * Format date for display\n */ function formatDate(dateString) {\n    const date = new Date(dateString);\n    return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\"\n    });\n}\n/**\n * Format date for SEO (ISO format)\n */ function formatDateISO(dateString) {\n    return new Date(dateString).toISOString();\n}\n/**\n * Truncate text to a specified length\n */ function truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength).trim() + \"...\";\n}\n/**\n * Extract excerpt from content if not provided\n */ function extractExcerpt(content, maxLength = 160) {\n    // Remove HTML tags and markdown formatting\n    const plainText = content.replace(/<[^>]*>/g, \"\") // Remove HTML tags\n    .replace(/[#*_`]/g, \"\") // Remove basic markdown formatting\n    .replace(/\\n+/g, \" \") // Replace newlines with spaces\n    .trim();\n    return truncateText(plainText, maxLength);\n}\n/**\n * Validate email format\n */ function isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n/**\n * Generate a random API key\n */ function generateApiKey() {\n    return \"ak_\" + Math.random().toString(36).substring(2) + Date.now().toString(36);\n}\n/**\n * Sanitize HTML content (basic implementation)\n */ function sanitizeHtml(html) {\n    // This is a basic implementation. In production, use a proper HTML sanitizer like DOMPurify\n    return html.replace(/<script[^>]*>.*?<\\/script>/gi, \"\").replace(/<iframe[^>]*>.*?<\\/iframe>/gi, \"\").replace(/on\\w+=\"[^\"]*\"/gi, \"\"); // Remove event handlers\n}\n/**\n * Create SEO-friendly meta description\n */ function createMetaDescription(content, maxLength = 160) {\n    const excerpt = extractExcerpt(content, maxLength);\n    return excerpt.endsWith(\"...\") ? excerpt : excerpt + \".\";\n}\n/**\n * Validate article slug uniqueness\n */ function validateSlugFormat(slug) {\n    const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;\n    return slugRegex.test(slug) && slug.length >= 3 && slug.length <= 100;\n}\n/**\n * Parse query parameters for pagination\n */ function parseQueryParams(query) {\n    return {\n        page: parseInt(query.page) || 1,\n        limit: Math.min(parseInt(query.limit) || 10, 50),\n        search: query.search || undefined,\n        sortBy: query.sortBy || \"publishedAt\",\n        sortOrder: query.sortOrder === \"asc\" ? \"asc\" : \"desc\"\n    };\n}\n/**\n * Create a standardized API response\n */ function createApiResponse(success, data, error, message) {\n    return {\n        success,\n        ...data !== undefined && {\n            data\n        },\n        ...error && {\n            error\n        },\n        ...message && {\n            message\n        }\n    };\n}\n/**\n * Handle API errors consistently\n */ function handleApiError(error) {\n    console.error(\"API Error:\", error);\n    if (error.message) {\n        return createApiResponse(false, undefined, error.message);\n    }\n    return createApiResponse(false, undefined, \"An unexpected error occurred\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/utils.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/react-icons","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Bcategory%5D%2F%5Bslug%5D%2Fpage&page=%2F%5Bcategory%5D%2F%5Bslug%5D%2Fpage&appPaths=%2F%5Bcategory%5D%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bcategory%5D%2F%5Bslug%5D%2Fpage.tsx&appDir=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();