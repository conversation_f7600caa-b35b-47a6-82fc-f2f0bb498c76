"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/articles/[category]/[slug]/route";
exports.ids = ["app/api/articles/[category]/[slug]/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("fs/promises");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Farticles%2F%5Bcategory%5D%2F%5Bslug%5D%2Froute&page=%2Fapi%2Farticles%2F%5Bcategory%5D%2F%5Bslug%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Farticles%2F%5Bcategory%5D%2F%5Bslug%5D%2Froute.ts&appDir=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Farticles%2F%5Bcategory%5D%2F%5Bslug%5D%2Froute&page=%2Fapi%2Farticles%2F%5Bcategory%5D%2F%5Bslug%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Farticles%2F%5Bcategory%5D%2F%5Bslug%5D%2Froute.ts&appDir=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/node-polyfill-headers */ \"(rsc)/./node_modules/next/dist/server/node-polyfill-headers.js\");\n/* harmony import */ var next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var _Users_chauminhkhai_Documents_augment_projects_Personal_website_app_api_articles_category_slug_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/articles/[category]/[slug]/route.ts */ \"(rsc)/./app/api/articles/[category]/[slug]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_2__.RouteKind.APP_ROUTE,\n        page: \"/api/articles/[category]/[slug]/route\",\n        pathname: \"/api/articles/[category]/[slug]\",\n        filename: \"route\",\n        bundlePath: \"app/api/articles/[category]/[slug]/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/augment-projects/Personal_website/app/api/articles/[category]/[slug]/route.ts\",\n    nextConfigOutput,\n    userland: _Users_chauminhkhai_Documents_augment_projects_Personal_website_app_api_articles_category_slug_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/articles/[category]/[slug]/route\";\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Farticles%2F%5Bcategory%5D%2F%5Bslug%5D%2Froute&page=%2Fapi%2Farticles%2F%5Bcategory%5D%2F%5Bslug%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Farticles%2F%5Bcategory%5D%2F%5Bslug%5D%2Froute.ts&appDir=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/articles/[category]/[slug]/route.ts":
/*!*****************************************************!*\
  !*** ./app/api/articles/[category]/[slug]/route.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_storage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/storage */ \"(rsc)/./lib/storage.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./lib/utils.ts\");\n\n\n\n// GET /api/articles/[category]/[slug] - Fetch individual article by category and slug\nasync function GET(request, { params }) {\n    try {\n        // Validate category\n        if (!(0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.isValidCategory)(params.category)) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json((0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.createApiResponse)(false, undefined, \"Invalid category\"), {\n                status: 400\n            });\n        }\n        const article = await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.articleStorage.getBySlug(params.category, params.slug);\n        if (!article) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json((0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.createApiResponse)(false, undefined, \"Article not found\"), {\n                status: 404\n            });\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json((0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.createApiResponse)(true, article));\n    } catch (error) {\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json((0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.handleApiError)(error), {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/articles/[category]/[slug]/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/storage.ts":
/*!************************!*\
  !*** ./lib/storage.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   articleStorage: () => (/* binding */ articleStorage)\n/* harmony export */ });\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils */ \"(rsc)/./lib/utils.ts\");\n\n\n\nconst DATA_DIR = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"data\");\nconst ARTICLES_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(DATA_DIR, \"articles.json\");\nclass JSONArticleStorage {\n    async ensureDataDir() {\n        try {\n            await fs_promises__WEBPACK_IMPORTED_MODULE_0___default().access(DATA_DIR);\n        } catch  {\n            await fs_promises__WEBPACK_IMPORTED_MODULE_0___default().mkdir(DATA_DIR, {\n                recursive: true\n            });\n        }\n    }\n    async loadArticles() {\n        await this.ensureDataDir();\n        try {\n            const data = await fs_promises__WEBPACK_IMPORTED_MODULE_0___default().readFile(ARTICLES_FILE, \"utf-8\");\n            return JSON.parse(data);\n        } catch  {\n            return [];\n        }\n    }\n    async saveArticles(articles) {\n        await this.ensureDataDir();\n        await fs_promises__WEBPACK_IMPORTED_MODULE_0___default().writeFile(ARTICLES_FILE, JSON.stringify(articles, null, 2));\n    }\n    generateId() {\n        return Date.now().toString(36) + Math.random().toString(36).substr(2);\n    }\n    filterArticles(articles, params = {}) {\n        let filtered = [\n            ...articles\n        ];\n        // Filter by status (default to published for public queries)\n        if (params.status) {\n            filtered = filtered.filter((article)=>article.status === params.status);\n        } else {\n            filtered = filtered.filter((article)=>article.status === \"published\");\n        }\n        // Filter by category\n        if (params.category) {\n            filtered = filtered.filter((article)=>article.category === params.category);\n        }\n        // Filter by search term\n        if (params.search) {\n            const searchTerm = params.search.toLowerCase();\n            filtered = filtered.filter((article)=>article.title.toLowerCase().includes(searchTerm) || article.excerpt.toLowerCase().includes(searchTerm) || article.content.toLowerCase().includes(searchTerm) || article.tags?.some((tag)=>tag.toLowerCase().includes(searchTerm)));\n        }\n        // Sort articles\n        const sortBy = params.sortBy || \"publishedAt\";\n        const sortOrder = params.sortOrder || \"desc\";\n        filtered.sort((a, b)=>{\n            let aValue = a[sortBy];\n            let bValue = b[sortBy];\n            if (sortBy === \"publishedAt\") {\n                aValue = new Date(aValue).getTime();\n                bValue = new Date(bValue).getTime();\n            }\n            if (sortOrder === \"asc\") {\n                return aValue > bValue ? 1 : -1;\n            } else {\n                return aValue < bValue ? 1 : -1;\n            }\n        });\n        return filtered;\n    }\n    paginateResults(items, page = 1, limit = 10) {\n        const total = items.length;\n        const totalPages = Math.ceil(total / limit);\n        const startIndex = (page - 1) * limit;\n        const endIndex = startIndex + limit;\n        const paginatedItems = items.slice(startIndex, endIndex);\n        return {\n            success: true,\n            data: paginatedItems,\n            pagination: {\n                page,\n                limit,\n                total,\n                totalPages,\n                hasNext: page < totalPages,\n                hasPrev: page > 1\n            }\n        };\n    }\n    async getAll(params = {}) {\n        const articles = await this.loadArticles();\n        const filtered = this.filterArticles(articles, params);\n        return this.paginateResults(filtered, params.page, params.limit);\n    }\n    async getByCategory(category, params = {}) {\n        const articles = await this.loadArticles();\n        const filtered = this.filterArticles(articles, {\n            ...params,\n            category\n        });\n        return this.paginateResults(filtered, params.page, params.limit);\n    }\n    async getBySlug(category, slug) {\n        const articles = await this.loadArticles();\n        const article = articles.find((a)=>a.category === category && a.slug === slug && a.status === \"published\");\n        if (article) {\n            // Increment view count\n            article.viewCount = (article.viewCount || 0) + 1;\n            await this.saveArticles(articles);\n        }\n        return article || null;\n    }\n    async getById(id) {\n        const articles = await this.loadArticles();\n        return articles.find((a)=>a.id === id) || null;\n    }\n    async create(input) {\n        const articles = await this.loadArticles();\n        const now = new Date().toISOString();\n        const article = {\n            id: this.generateId(),\n            ...input,\n            slug: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.generateSlug)(input.title),\n            status: input.status || \"draft\",\n            publishedAt: input.status === \"published\" ? now : \"\",\n            lastModified: now,\n            createdAt: now,\n            readingTime: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.calculateReadingTime)(input.content),\n            viewCount: 0\n        };\n        articles.push(article);\n        await this.saveArticles(articles);\n        return article;\n    }\n    async update(id, updates) {\n        const articles = await this.loadArticles();\n        const index = articles.findIndex((a)=>a.id === id);\n        if (index === -1) {\n            throw new Error(\"Article not found\");\n        }\n        const article = articles[index];\n        const now = new Date().toISOString();\n        // Update fields\n        Object.assign(article, updates);\n        article.lastModified = now;\n        // Update slug if title changed\n        if (updates.title) {\n            article.slug = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.generateSlug)(updates.title);\n        }\n        // Update reading time if content changed\n        if (updates.content) {\n            article.readingTime = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.calculateReadingTime)(updates.content);\n        }\n        // Set published date if status changed to published\n        if (updates.status === \"published\" && !article.publishedAt) {\n            article.publishedAt = now;\n        }\n        await this.saveArticles(articles);\n        return article;\n    }\n    async delete(id) {\n        const articles = await this.loadArticles();\n        const index = articles.findIndex((a)=>a.id === id);\n        if (index === -1) {\n            return false;\n        }\n        articles.splice(index, 1);\n        await this.saveArticles(articles);\n        return true;\n    }\n    async getCategoryStats() {\n        const articles = await this.loadArticles();\n        const publishedArticles = articles.filter((a)=>a.status === \"published\");\n        const categories = [\n            \"history\",\n            \"business\",\n            \"technology\",\n            \"politics\",\n            \"art\",\n            \"social-life\"\n        ];\n        return categories.map((category)=>({\n                name: category,\n                displayName: category.charAt(0).toUpperCase() + category.slice(1).replace(\"-\", \" \"),\n                description: `Articles about ${category.replace(\"-\", \" \")}`,\n                articleCount: publishedArticles.filter((a)=>a.category === category).length\n            }));\n    }\n}\nconst articleStorage = new JSONArticleStorage();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/storage.ts\n");

/***/ }),

/***/ "(rsc)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateReadingTime: () => (/* binding */ calculateReadingTime),\n/* harmony export */   createApiResponse: () => (/* binding */ createApiResponse),\n/* harmony export */   createMetaDescription: () => (/* binding */ createMetaDescription),\n/* harmony export */   extractExcerpt: () => (/* binding */ extractExcerpt),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDateISO: () => (/* binding */ formatDateISO),\n/* harmony export */   generateApiKey: () => (/* binding */ generateApiKey),\n/* harmony export */   generateSlug: () => (/* binding */ generateSlug),\n/* harmony export */   getCategoryDisplayName: () => (/* binding */ getCategoryDisplayName),\n/* harmony export */   handleApiError: () => (/* binding */ handleApiError),\n/* harmony export */   isValidCategory: () => (/* binding */ isValidCategory),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   parseQueryParams: () => (/* binding */ parseQueryParams),\n/* harmony export */   sanitizeHtml: () => (/* binding */ sanitizeHtml),\n/* harmony export */   truncateText: () => (/* binding */ truncateText),\n/* harmony export */   validateSlugFormat: () => (/* binding */ validateSlugFormat)\n/* harmony export */ });\n/**\n * Generate a URL-friendly slug from a title\n */ function generateSlug(title) {\n    return title.toLowerCase().trim().replace(/[^\\w\\s-]/g, \"\") // Remove special characters\n    .replace(/[\\s_-]+/g, \"-\") // Replace spaces and underscores with hyphens\n    .replace(/^-+|-+$/g, \"\"); // Remove leading/trailing hyphens\n}\n/**\n * Calculate estimated reading time in minutes\n */ function calculateReadingTime(content) {\n    const wordsPerMinute = 200; // Average reading speed\n    const wordCount = content.trim().split(/\\s+/).length;\n    const readingTime = Math.ceil(wordCount / wordsPerMinute);\n    return Math.max(1, readingTime); // Minimum 1 minute\n}\n/**\n * Validate if a string is a valid article category\n */ function isValidCategory(category) {\n    const validCategories = [\n        \"history\",\n        \"business\",\n        \"technology\",\n        \"politics\",\n        \"art\",\n        \"social-life\"\n    ];\n    return validCategories.includes(category);\n}\n/**\n * Get display name for a category\n */ function getCategoryDisplayName(category) {\n    const displayNames = {\n        \"history\": \"History\",\n        \"business\": \"Business\",\n        \"technology\": \"Technology\",\n        \"politics\": \"Politics\",\n        \"art\": \"Art\",\n        \"social-life\": \"Social Life\"\n    };\n    return displayNames[category];\n}\n/**\n * Format date for display\n */ function formatDate(dateString) {\n    const date = new Date(dateString);\n    return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\"\n    });\n}\n/**\n * Format date for SEO (ISO format)\n */ function formatDateISO(dateString) {\n    return new Date(dateString).toISOString();\n}\n/**\n * Truncate text to a specified length\n */ function truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength).trim() + \"...\";\n}\n/**\n * Extract excerpt from content if not provided\n */ function extractExcerpt(content, maxLength = 160) {\n    // Remove HTML tags and markdown formatting\n    const plainText = content.replace(/<[^>]*>/g, \"\") // Remove HTML tags\n    .replace(/[#*_`]/g, \"\") // Remove basic markdown formatting\n    .replace(/\\n+/g, \" \") // Replace newlines with spaces\n    .trim();\n    return truncateText(plainText, maxLength);\n}\n/**\n * Validate email format\n */ function isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n/**\n * Generate a random API key\n */ function generateApiKey() {\n    return \"ak_\" + Math.random().toString(36).substring(2) + Date.now().toString(36);\n}\n/**\n * Sanitize HTML content (basic implementation)\n */ function sanitizeHtml(html) {\n    // This is a basic implementation. In production, use a proper HTML sanitizer like DOMPurify\n    return html.replace(/<script[^>]*>.*?<\\/script>/gi, \"\").replace(/<iframe[^>]*>.*?<\\/iframe>/gi, \"\").replace(/on\\w+=\"[^\"]*\"/gi, \"\"); // Remove event handlers\n}\n/**\n * Create SEO-friendly meta description\n */ function createMetaDescription(content, maxLength = 160) {\n    const excerpt = extractExcerpt(content, maxLength);\n    return excerpt.endsWith(\"...\") ? excerpt : excerpt + \".\";\n}\n/**\n * Validate article slug uniqueness\n */ function validateSlugFormat(slug) {\n    const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;\n    return slugRegex.test(slug) && slug.length >= 3 && slug.length <= 100;\n}\n/**\n * Parse query parameters for pagination\n */ function parseQueryParams(query) {\n    return {\n        page: parseInt(query.page) || 1,\n        limit: Math.min(parseInt(query.limit) || 10, 50),\n        search: query.search || undefined,\n        sortBy: query.sortBy || \"publishedAt\",\n        sortOrder: query.sortOrder === \"asc\" ? \"asc\" : \"desc\"\n    };\n}\n/**\n * Create a standardized API response\n */ function createApiResponse(success, data, error, message) {\n    return {\n        success,\n        ...data !== undefined && {\n            data\n        },\n        ...error && {\n            error\n        },\n        ...message && {\n            message\n        }\n    };\n}\n/**\n * Handle API errors consistently\n */ function handleApiError(error) {\n    console.error(\"API Error:\", error);\n    if (error.message) {\n        return createApiResponse(false, undefined, error.message);\n    }\n    return createApiResponse(false, undefined, \"An unexpected error occurred\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/utils.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Farticles%2F%5Bcategory%5D%2F%5Bslug%5D%2Froute&page=%2Fapi%2Farticles%2F%5Bcategory%5D%2F%5Bslug%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Farticles%2F%5Bcategory%5D%2F%5Bslug%5D%2Froute.ts&appDir=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fchauminhkhai%2FDocuments%2Faugment-projects%2FPersonal_website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();