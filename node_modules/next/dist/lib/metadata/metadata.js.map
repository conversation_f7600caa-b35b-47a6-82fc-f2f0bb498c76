{"version": 3, "sources": ["../../../src/lib/metadata/metadata.tsx"], "names": ["createMetadataComponents", "tree", "pathname", "searchParams", "getDynamicParamFromSegment", "appUsingSizeAdjustment", "errorType", "metadataContext", "resolve", "metadataErrorResolving", "Promise", "res", "MetadataTree", "defaultMetadata", "createDefaultMetadata", "metadata", "error", "errorMetadataItem", "errorConvention", "undefined", "resolvedMetadata", "resolvedError", "resolveMetadata", "parentParams", "metadataItems", "isNotFoundError", "notFoundMetadata", "notFoundMetadataError", "elements", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BasicMetadata", "AlternatesMetadata", "alternates", "ItunesMeta", "itunes", "FormatDetectionMeta", "formatDetection", "VerificationMeta", "verification", "AppleWebAppMeta", "appleWebApp", "OpenGraphMetadata", "openGraph", "TwitterMetadata", "twitter", "AppLinksMeta", "appLinks", "IconsMetadata", "icons", "push", "meta", "name", "map", "el", "index", "React", "cloneElement", "key", "MetadataOutlet"], "mappings": ";;;;+BA8BgBA;;;eAAAA;;;8DA3BE;uBAOX;2BAC4B;2BAK5B;uBACuB;iCACE;sBACL;iCAEW;0BACN;;;;;;AAQzB,SAASA,yBAAyB,EACvCC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,0BAA0B,EAC1BC,sBAAsB,EACtBC,SAAS,EAQV;IACC,MAAMC,kBAAkB;QACtBL;IACF;IAEA,IAAIM;IACJ,8DAA8D;IAC9D,MAAMC,yBAAyB,IAAIC,QAA2B,CAACC;QAC7DH,UAAUG;IACZ;IAEA,eAAeC;QACb,MAAMC,kBAAkBC,IAAAA,sCAAqB;QAC7C,IAAIC,WAAyCF;QAC7C,IAAIG;QACJ,MAAMC,oBAAkC;YAAC;YAAM;SAAK;QACpD,MAAMC,kBAAkBZ,cAAc,aAAaa,YAAYb;QAE/D,MAAM,CAACc,kBAAkBC,cAAc,GAAG,MAAMC,IAAAA,gCAAe,EAAC;YAC9DrB;YACAsB,cAAc,CAAC;YACfC,eAAe,EAAE;YACjBP;YACAd;YACAC;YACAc;YACAX;QACF;QACA,IAAI,CAACc,eAAe;YAClBN,WAAWK;YACXZ,QAAQW;QACV,OAAO;YACLH,QAAQK;YACR,0FAA0F;YAC1F,kGAAkG;YAClG,kDAAkD;YAClD,IAAI,CAACf,aAAamB,IAAAA,yBAAe,EAACJ,gBAAgB;gBAChD,MAAM,CAACK,kBAAkBC,sBAAsB,GAAG,MAAML,IAAAA,gCAAe,EACrE;oBACErB;oBACAsB,cAAc,CAAC;oBACfC,eAAe,EAAE;oBACjBP;oBACAd;oBACAC;oBACAc,iBAAiB;oBACjBX;gBACF;gBAEFQ,WAAWW;gBACXV,QAAQW,yBAAyBX;YACnC;YACAR,QAAQQ;QACV;QAEA,MAAMY,WAAWC,IAAAA,gBAAU,EAAC;YAC1BC,IAAAA,oBAAa,EAAC;gBAAEf;YAAS;YACzBgB,IAAAA,6BAAkB,EAAC;gBAAEC,YAAYjB,SAASiB,UAAU;YAAC;YACrDC,IAAAA,iBAAU,EAAC;gBAAEC,QAAQnB,SAASmB,MAAM;YAAC;YACrCC,IAAAA,0BAAmB,EAAC;gBAAEC,iBAAiBrB,SAASqB,eAAe;YAAC;YAChEC,IAAAA,uBAAgB,EAAC;gBAAEC,cAAcvB,SAASuB,YAAY;YAAC;YACvDC,IAAAA,sBAAe,EAAC;gBAAEC,aAAazB,SAASyB,WAAW;YAAC;YACpDC,IAAAA,4BAAiB,EAAC;gBAAEC,WAAW3B,SAAS2B,SAAS;YAAC;YAClDC,IAAAA,0BAAe,EAAC;gBAAEC,SAAS7B,SAAS6B,OAAO;YAAC;YAC5CC,IAAAA,uBAAY,EAAC;gBAAEC,UAAU/B,SAAS+B,QAAQ;YAAC;YAC3CC,IAAAA,oBAAa,EAAC;gBAAEC,OAAOjC,SAASiC,KAAK;YAAC;SACvC;QAED,IAAI3C,wBAAwBuB,SAASqB,IAAI,eAAC,6BAACC;YAAKC,MAAK;;QAErD,qBACE,4DACGvB,SAASwB,GAAG,CAAC,CAACC,IAAIC;YACjB,qBAAOC,cAAK,CAACC,YAAY,CAACH,IAA0B;gBAAEI,KAAKH;YAAM;QACnE;IAGN;IAEA,eAAeI;QACb,MAAM1C,QAAQ,MAAMP;QACpB,IAAIO,OAAO;YACT,MAAMA;QACR;QACA,OAAO;IACT;IAEA,2CAA2C;IAC3C,OAAO;QAACJ;QAAc8C;KAAe;AACvC"}