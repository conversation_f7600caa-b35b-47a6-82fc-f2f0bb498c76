{"version": 3, "sources": ["../../src/lib/mkcert.ts"], "names": ["createSelfSignedCertificate", "fetch", "require", "MKCERT_VERSION", "getBinaryName", "platform", "process", "arch", "Error", "downloadBinary", "binaryName", "cacheDirectory", "getCacheDirectory", "binaryPath", "path", "join", "fs", "existsSync", "downloadUrl", "promises", "mkdir", "recursive", "Log", "info", "response", "ok", "body", "status", "arrayBuffer", "buffer", "<PERSON><PERSON><PERSON>", "from", "writeFile", "chmod", "err", "error", "host", "certDir", "resolvedCertDir", "resolve", "cwd", "keyP<PERSON>", "certPath", "defaultHosts", "hosts", "includes", "execSync", "stdio", "caLocation", "toString", "trim", "gitignore<PERSON>ath", "gitignore", "readFile", "appendFile", "key", "cert", "rootCA"], "mappings": ";;;;+BAuEsBA;;;eAAAA;;;2DAvEP;6DACE;mCACiB;6DACb;+BACI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEzB,MAAM,EAAEC,KAAK,EAAE,GAAGC,QAAQ;AAI1B,MAAMC,iBAAiB;AAQvB,SAASC;IACP,MAAMC,WAAWC,QAAQD,QAAQ;IACjC,MAAME,OAAOD,QAAQC,IAAI,KAAK,QAAQ,UAAUD,QAAQC,IAAI;IAE5D,IAAIF,aAAa,SAAS;QACxB,OAAO,CAAC,OAAO,EAAEF,eAAe,SAAS,EAAEI,KAAK,IAAI,CAAC;IACvD;IACA,IAAIF,aAAa,UAAU;QACzB,OAAO,CAAC,OAAO,EAAEF,eAAe,QAAQ,EAAEI,KAAK,CAAC;IAClD;IACA,IAAIF,aAAa,SAAS;QACxB,OAAO,CAAC,OAAO,EAAEF,eAAe,OAAO,EAAEI,KAAK,CAAC;IACjD;IAEA,MAAM,IAAIC,MAAM,CAAC,sBAAsB,EAAEH,SAAS,CAAC;AACrD;AAEA,eAAeI;IACb,IAAI;QACF,MAAMC,aAAaN;QACnB,MAAMO,iBAAiBC,IAAAA,oCAAiB,EAAC;QACzC,MAAMC,aAAaC,aAAI,CAACC,IAAI,CAACJ,gBAAgBD;QAE7C,IAAIM,WAAE,CAACC,UAAU,CAACJ,aAAa;YAC7B,OAAOA;QACT;QAEA,MAAMK,cAAc,CAAC,wDAAwD,EAAEf,eAAe,CAAC,EAAEO,WAAW,CAAC;QAE7G,MAAMM,WAAE,CAACG,QAAQ,CAACC,KAAK,CAACT,gBAAgB;YAAEU,WAAW;QAAK;QAE1DC,KAAIC,IAAI,CAAC,CAAC,6BAA6B,CAAC;QAExC,MAAMC,WAAW,MAAMvB,MAAMiB;QAE7B,IAAI,CAACM,SAASC,EAAE,IAAI,CAACD,SAASE,IAAI,EAAE;YAClC,MAAM,IAAIlB,MAAM,CAAC,2BAA2B,EAAEgB,SAASG,MAAM,CAAC,CAAC;QACjE;QAEAL,KAAIC,IAAI,CAAC,CAAC,iDAAiD,CAAC;QAE5D,MAAMK,cAAc,MAAMJ,SAASI,WAAW;QAC9C,MAAMC,SAASC,OAAOC,IAAI,CAACH;QAE3B,MAAMZ,WAAE,CAACG,QAAQ,CAACa,SAAS,CAACnB,YAAYgB;QACxC,MAAMb,WAAE,CAACG,QAAQ,CAACc,KAAK,CAACpB,YAAY;QAEpC,OAAOA;IACT,EAAE,OAAOqB,KAAK;QACZZ,KAAIa,KAAK,CAAC,6BAA6BD;IACzC;AACF;AAEO,eAAelC,4BACpBoC,IAAa,EACbC,UAAkB,cAAc;IAEhC,IAAI;QACF,MAAMxB,aAAa,MAAMJ;QACzB,IAAI,CAACI,YAAY,MAAM,IAAIL,MAAM;QAEjC,MAAM8B,kBAAkBxB,aAAI,CAACyB,OAAO,CAACjC,QAAQkC,GAAG,IAAI,CAAC,EAAE,EAAEH,QAAQ,CAAC;QAElE,MAAMrB,WAAE,CAACG,QAAQ,CAACC,KAAK,CAACkB,iBAAiB;YACvCjB,WAAW;QACb;QAEA,MAAMoB,UAAU3B,aAAI,CAACyB,OAAO,CAACD,iBAAiB;QAC9C,MAAMI,WAAW5B,aAAI,CAACyB,OAAO,CAACD,iBAAiB;QAE/ChB,KAAIC,IAAI,CACN;QAGF,MAAMoB,eAAe;YAAC;YAAa;YAAa;SAAM;QAEtD,MAAMC,QACJR,QAAQ,CAACO,aAAaE,QAAQ,CAACT,QAC3B;eAAIO;YAAcP;SAAK,GACvBO;QAENG,IAAAA,uBAAQ,EACN,CAAC,CAAC,EAAEjC,WAAW,sBAAsB,EAAE4B,QAAQ,cAAc,EAAEC,SAAS,EAAE,EAAEE,MAAM7B,IAAI,CACpF,KACA,CAAC,EACH;YAAEgC,OAAO;QAAS;QAGpB,MAAMC,aAAaF,IAAAA,uBAAQ,EAAC,CAAC,CAAC,EAAEjC,WAAW,SAAS,CAAC,EAAEoC,QAAQ,GAAGC,IAAI;QAEtE,IAAI,CAAClC,WAAE,CAACC,UAAU,CAACwB,YAAY,CAACzB,WAAE,CAACC,UAAU,CAACyB,WAAW;YACvD,MAAM,IAAIlC,MAAM;QAClB;QAEAc,KAAIC,IAAI,CAAC,CAAC,+BAA+B,EAAEyB,WAAW,CAAC;QACvD1B,KAAIC,IAAI,CAAC,CAAC,wBAAwB,EAAEe,gBAAgB,CAAC;QAErD,MAAMa,gBAAgBrC,aAAI,CAACyB,OAAO,CAACjC,QAAQkC,GAAG,IAAI;QAElD,IAAIxB,WAAE,CAACC,UAAU,CAACkC,gBAAgB;YAChC,MAAMC,YAAY,MAAMpC,WAAE,CAACG,QAAQ,CAACkC,QAAQ,CAACF,eAAe;YAC5D,IAAI,CAACC,UAAUP,QAAQ,CAACR,UAAU;gBAChCf,KAAIC,IAAI,CAAC;gBAET,MAAMP,WAAE,CAACG,QAAQ,CAACmC,UAAU,CAACH,eAAe,CAAC,EAAE,EAAEd,QAAQ,CAAC;YAC5D;QACF;QAEA,OAAO;YACLkB,KAAKd;YACLe,MAAMd;YACNe,QAAQ,CAAC,EAAET,WAAW,WAAW,CAAC;QACpC;IACF,EAAE,OAAOd,KAAK;QACZZ,KAAIa,KAAK,CACP,qEACAD;IAEJ;AACF"}