{"version": 3, "sources": ["../../src/lib/download-swc.ts"], "names": ["downloadNativeNextSwc", "downloadWasmSwc", "fetch", "require", "WritableStream", "MAX_VERSIONS_TO_CACHE", "extractBinary", "outputDirectory", "pkgName", "tarFileName", "cacheDirectory", "getCacheDirectory", "process", "env", "extractFromTar", "tar", "x", "file", "path", "join", "cwd", "strip", "fs", "existsSync", "Log", "info", "promises", "mkdir", "recursive", "tempFile", "Date", "now", "registry", "getRegistry", "downloadUrl", "then", "res", "ok", "body", "error", "Error", "status", "cacheWriteStream", "createWriteStream", "pipeTo", "write", "chunk", "close", "rename", "cacheFiles", "readdir", "length", "sort", "a", "b", "localeCompare", "i", "unlink", "catch", "version", "bindingsDirectory", "triplesABI", "triple", "substring", "wasmDirectory", "variant"], "mappings": ";;;;;;;;;;;;;;;IAyFsBA,qBAAqB;eAArBA;;IAqBAC,eAAe;eAAfA;;;2DA9GP;6DACE;6DACI;4DACL;6BAOY;mCACM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAPlC,MAAM,EAAEC,KAAK,EAAE,GAAGC,QAAQ;AAG1B,MAAM,EAAEC,cAAc,EAAE,GAAGD,QAAQ;AAMnC,MAAME,wBAAwB;AAE9B,eAAeC,cACbC,eAAuB,EACvBC,OAAe,EACfC,WAAmB;IAEnB,MAAMC,iBAAiBC,IAAAA,oCAAiB,EACtC,YACAC,QAAQC,GAAG,CAAC,gBAAgB;IAG9B,MAAMC,iBAAiB,IACrBC,YAAG,CAACC,CAAC,CAAC;YACJC,MAAMC,aAAI,CAACC,IAAI,CAACT,gBAAgBD;YAChCW,KAAKb;YACLc,OAAO;QACT;IAEF,IAAI,CAACC,WAAE,CAACC,UAAU,CAACL,aAAI,CAACC,IAAI,CAACT,gBAAgBD,eAAe;QAC1De,KAAIC,IAAI,CAAC,CAAC,wBAAwB,EAAEjB,QAAQ,GAAG,CAAC;QAChD,MAAMc,WAAE,CAACI,QAAQ,CAACC,KAAK,CAACjB,gBAAgB;YAAEkB,WAAW;QAAK;QAC1D,MAAMC,WAAWX,aAAI,CAACC,IAAI,CACxBT,gBACA,CAAC,EAAED,YAAY,MAAM,EAAEqB,KAAKC,GAAG,GAAG,CAAC;QAGrC,MAAMC,WAAWC,IAAAA,wBAAW;QAE5B,MAAMC,cAAc,CAAC,EAAEF,SAAS,EAAExB,QAAQ,GAAG,EAAEC,YAAY,CAAC;QAE5D,MAAMP,MAAMgC,aAAaC,IAAI,CAAC,CAACC;YAC7B,MAAM,EAAEC,EAAE,EAAEC,IAAI,EAAE,GAAGF;YACrB,IAAI,CAACC,MAAM,CAACC,MAAM;gBAChBd,KAAIe,KAAK,CAAC,CAAC,oCAAoC,EAAEL,YAAY,CAAC;YAChE;YAEA,IAAI,CAACG,IAAI;gBACP,MAAM,IAAIG,MAAM,CAAC,2BAA2B,EAAEJ,IAAIK,MAAM,CAAC,CAAC;YAC5D;YACA,IAAI,CAACH,MAAM;gBACT,MAAM,IAAIE,MAAM;YAClB;YACA,MAAME,mBAAmBpB,WAAE,CAACqB,iBAAiB,CAACd;YAC9C,OAAOS,KAAKM,MAAM,CAChB,IAAIxC,eAAe;gBACjByC,OAAMC,KAAK;oBACTJ,iBAAiBG,KAAK,CAACC;gBACzB;gBACAC;oBACEL,iBAAiBK,KAAK;gBACxB;YACF;QAEJ;QACA,MAAMzB,WAAE,CAACI,QAAQ,CAACsB,MAAM,CAACnB,UAAUX,aAAI,CAACC,IAAI,CAACT,gBAAgBD;IAC/D;IACA,MAAMK;IAEN,MAAMmC,aAAa,MAAM3B,WAAE,CAACI,QAAQ,CAACwB,OAAO,CAACxC;IAE7C,IAAIuC,WAAWE,MAAM,GAAG9C,uBAAuB;QAC7C4C,WAAWG,IAAI,CAAC,CAACC,GAAGC;YAClB,IAAID,EAAEF,MAAM,GAAGG,EAAEH,MAAM,EAAE,OAAO,CAAC;YACjC,OAAOE,EAAEE,aAAa,CAACD;QACzB;QAEA,iCAAiC;QACjC,IAAK,IAAIE,IAAI,GAAGA,KAAKA,IAAIP,WAAWE,MAAM,GAAG9C,sBAAuB;YAClE,MAAMiB,WAAE,CAACI,QAAQ,CACd+B,MAAM,CAACvC,aAAI,CAACC,IAAI,CAACT,gBAAgBuC,UAAU,CAACO,EAAE,GAC9CE,KAAK,CAAC,KAAO;QAClB;IACF;AACF;AAEO,eAAe1D,sBACpB2D,OAAe,EACfC,iBAAyB,EACzBC,UAAyB;IAEzB,KAAK,MAAMC,UAAUD,WAAY;QAC/B,MAAMrD,UAAU,CAAC,UAAU,EAAEsD,OAAO,CAAC;QACrC,MAAMrD,cAAc,CAAC,EAAED,QAAQuD,SAAS,CAAC,GAAG,CAAC,EAAEJ,QAAQ,IAAI,CAAC;QAC5D,MAAMpD,kBAAkBW,aAAI,CAACC,IAAI,CAACyC,mBAAmBpD;QAErD,IAAIc,WAAE,CAACC,UAAU,CAAChB,kBAAkB;YAClC,mDAAmD;YACnD,0CAA0C;YAC1C;QACF;QAEA,MAAMe,WAAE,CAACI,QAAQ,CAACC,KAAK,CAACpB,iBAAiB;YAAEqB,WAAW;QAAK;QAC3D,MAAMtB,cAAcC,iBAAiBC,SAASC;IAChD;AACF;AAEO,eAAeR,gBACpB0D,OAAe,EACfK,aAAqB,EACrBC,UAA4B,QAAQ;IAEpC,MAAMzD,UAAU,CAAC,eAAe,EAAEyD,QAAQ,CAAC;IAC3C,MAAMxD,cAAc,CAAC,EAAED,QAAQuD,SAAS,CAAC,GAAG,CAAC,EAAEJ,QAAQ,IAAI,CAAC;IAC5D,MAAMpD,kBAAkBW,aAAI,CAACC,IAAI,CAAC6C,eAAexD;IAEjD,IAAIc,WAAE,CAACC,UAAU,CAAChB,kBAAkB;QAClC,mDAAmD;QACnD,0CAA0C;QAC1C;IACF;IAEA,MAAMe,WAAE,CAACI,QAAQ,CAACC,KAAK,CAACpB,iBAAiB;QAAEqB,WAAW;IAAK;IAC3D,MAAMtB,cAAcC,iBAAiBC,SAASC;AAChD"}