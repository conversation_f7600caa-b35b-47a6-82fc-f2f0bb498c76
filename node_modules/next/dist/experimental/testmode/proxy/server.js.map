{"version": 3, "sources": ["../../../../src/experimental/testmode/proxy/server.ts"], "names": ["createProxyServer", "readBody", "req", "Promise", "resolve", "reject", "acc", "on", "chunk", "push", "<PERSON><PERSON><PERSON>", "concat", "onFetch", "server", "http", "createServer", "res", "url", "writeHead", "end", "json", "JSON", "parse", "toString", "e", "api", "response", "handleFetch", "UNHANDLED", "write", "stringify", "listen", "undefined", "address", "close", "Error", "port", "fetchWith", "input", "init", "testData", "request", "Request", "headers", "set", "String", "fetch"], "mappings": ";;;;+BAoBsBA;;;eAAAA;;;6DApBL;uBAGS;0BAEE;;;;;;AAE5B,SAASC,SAASC,GAAoB;IACpC,OAAO,IAAIC,QAAgB,CAACC,SAASC;QACnC,MAAMC,MAAgB,EAAE;QACxBJ,IAAIK,EAAE,CAAC,QAAQ,CAACC;YACdF,IAAIG,IAAI,CAACD;QACX;QACAN,IAAIK,EAAE,CAAC,OAAO;YACZH,QAAQM,OAAOC,MAAM,CAACL;QACxB;QACAJ,IAAIK,EAAE,CAAC,SAASF;IAClB;AACF;AAEO,eAAeL,kBAAkB,EACtCY,OAAO,EAGR;IACC,MAAMC,SAASC,aAAI,CAACC,YAAY,CAAC,OAAOb,KAAKc;QAC3C,IAAId,IAAIe,GAAG,KAAK,KAAK;YACnBD,IAAIE,SAAS,CAAC;YACdF,IAAIG,GAAG;YACP;QACF;QAEA,IAAIC;QACJ,IAAI;YACFA,OAAOC,KAAKC,KAAK,CAAC,AAAC,CAAA,MAAMrB,SAASC,IAAG,EAAGqB,QAAQ,CAAC;QACnD,EAAE,OAAOC,GAAG;YACVR,IAAIE,SAAS,CAAC;YACdF,IAAIG,GAAG;YACP;QACF;QAEA,MAAM,EAAEM,GAAG,EAAE,GAAGL;QAEhB,IAAIM;QACJ,OAAQD;YACN,KAAK;gBACH,IAAIb,SAAS;oBACXc,WAAW,MAAMC,IAAAA,qBAAW,EAACP,MAAMR;gBACrC;gBACA;YACF;gBACE;QACJ;QACA,IAAI,CAACc,UAAU;YACbA,WAAWE,gBAAS;QACtB;QAEAZ,IAAIE,SAAS,CAAC,KAAK;YAAE,gBAAgB;QAAmB;QACxDF,IAAIa,KAAK,CAACR,KAAKS,SAAS,CAACJ;QACzBV,IAAIG,GAAG;IACT;IAEA,MAAM,IAAIhB,QAAQ,CAACC;QACjBS,OAAOkB,MAAM,CAAC,GAAG,aAAa;YAC5B3B,QAAQ4B;QACV;IACF;IAEA,MAAMC,UAAUpB,OAAOoB,OAAO;IAC9B,IAAI,CAACA,WAAW,OAAOA,YAAY,UAAU;QAC3CpB,OAAOqB,KAAK;QACZ,MAAM,IAAIC,MAAM;IAClB;IACA,MAAMC,OAAOH,QAAQG,IAAI;IAEzB,MAAMC,YAAsC,CAACC,OAAOC,MAAMC;QACxD,MAAMC,UAAU,IAAIC,QAAQJ,OAAOC;QACnCE,QAAQE,OAAO,CAACC,GAAG,CAAC,wBAAwBC,OAAOT;QACnDK,QAAQE,OAAO,CAACC,GAAG,CAAC,kBAAkBJ,YAAY;QAClD,OAAOM,MAAML;IACf;IAEA,OAAO;QAAEL;QAAMF,OAAO,IAAMrB,OAAOqB,KAAK;QAAIG;IAAU;AACxD"}