{"version": 3, "sources": ["../../../src/server/send-payload/index.ts"], "names": ["setRevalidateHeaders", "sendEtagResponse", "sendRenderResult", "req", "res", "etag", "<PERSON><PERSON><PERSON><PERSON>", "fresh", "headers", "statusCode", "end", "result", "type", "generateEtags", "poweredByHeader", "options", "isResSent", "payload", "isDynamic", "toUnchunkedString", "generateETag", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "contentType", "RSC_CONTENT_TYPE_HEADER", "<PERSON><PERSON><PERSON>", "byteLength", "method", "pipe"], "mappings": ";;;;;;;;;;;;;;;;IAcSA,oBAAoB;eAApBA,uCAAoB;;IAEbC,gBAAgB;eAAhBA;;IAwBMC,gBAAgB;eAAhBA;;;uBAtCI;sBACG;8DACX;mCAEmB;kCACG;;;;;;AASjC,SAASD,iBACdE,GAAoB,EACpBC,GAAmB,EACnBC,IAAwB;IAExB,IAAIA,MAAM;QACR;;;;;KAKC,GACDD,IAAIE,SAAS,CAAC,QAAQD;IACxB;IAEA,IAAIE,IAAAA,cAAK,EAACJ,IAAIK,OAAO,EAAE;QAAEH;IAAK,IAAI;QAChCD,IAAIK,UAAU,GAAG;QACjBL,IAAIM,GAAG;QACP,OAAO;IACT;IAEA,OAAO;AACT;AAEO,eAAeR,iBAAiB,EACrCC,GAAG,EACHC,GAAG,EACHO,MAAM,EACNC,IAAI,EACJC,aAAa,EACbC,eAAe,EACfC,OAAO,EASR;IACC,IAAIC,IAAAA,gBAAS,EAACZ,MAAM;QAClB;IACF;IAEA,IAAIU,mBAAmBF,SAAS,QAAQ;QACtCR,IAAIE,SAAS,CAAC,gBAAgB;IAChC;IAEA,IAAIS,WAAW,MAAM;QACnBf,IAAAA,uCAAoB,EAACI,KAAKW;IAC5B;IAEA,MAAME,UAAUN,OAAOO,SAAS,GAAG,OAAO,MAAMP,OAAOQ,iBAAiB;IAExE,IAAIF,YAAY,MAAM;QACpB,MAAMZ,OAAOQ,gBAAgBO,IAAAA,kBAAY,EAACH,WAAWI;QACrD,IAAIpB,iBAAiBE,KAAKC,KAAKC,OAAO;YACpC;QACF;IACF;IAEA,IAAI,CAACD,IAAIkB,SAAS,CAAC,iBAAiB;QAClClB,IAAIE,SAAS,CACX,gBACAK,OAAOY,WAAW,GACdZ,OAAOY,WAAW,GAClBX,SAAS,QACTY,yCAAuB,GACvBZ,SAAS,SACT,qBACA;IAER;IAEA,IAAIK,SAAS;QACXb,IAAIE,SAAS,CAAC,kBAAkBmB,OAAOC,UAAU,CAACT;IACpD;IAEA,IAAId,IAAIwB,MAAM,KAAK,QAAQ;QACzBvB,IAAIM,GAAG,CAAC;IACV,OAAO,IAAIO,YAAY,MAAM;QAC3Bb,IAAIM,GAAG,CAACO;IACV,OAAO;QACL,MAAMN,OAAOiB,IAAI,CAACxB;IACpB;AACF"}