{"version": 3, "sources": ["../../../src/server/app-render/make-get-server-inserted-html.tsx"], "names": ["makeGetServerInsertedHTML", "polyfills", "renderServerInsertedHTML", "flushedErrorMetaTagsUntilIndex", "polyfillsFlushed", "getServerInsertedHTML", "serverCapturedErrors", "errorMetaTags", "length", "error", "isNotFoundError", "push", "meta", "name", "content", "key", "digest", "process", "env", "NODE_ENV", "isRedirectError", "redirectUrl", "getURLFromRedirectError", "isPermanent", "getRedirectStatusCodeFromError", "httpEquiv", "flushed", "renderToString", "ReactDOMServer", "require", "element", "map", "polyfill", "script", "src"], "mappings": ";;;;+BASgBA;;;eAAAA;;;8DATE;0BACc;0BAIzB;gDACwC;gCAChB;;;;;;AAExB,SAASA,0BAA0B,EACxCC,SAAS,EACTC,wBAAwB,EAIzB;IACC,IAAIC,iCAAiC;IACrC,IAAIC,mBAAmB;IAEvB,OAAO,SAASC,sBAAsBC,oBAA6B;QACjE,kEAAkE;QAClE,WAAW;QACX,MAAMC,gBAAgB,EAAE;QACxB,MAEEJ,iCAAiCG,qBAAqBE,MAAM,EAC5DL,iCACA;YACA,MAAMM,QAAQH,oBAAoB,CAACH,+BAA+B;YAElE,IAAIO,IAAAA,yBAAe,EAACD,QAAQ;gBAC1BF,cAAcI,IAAI,eAChB,6BAACC;oBAAKC,MAAK;oBAASC,SAAQ;oBAAUC,KAAKN,MAAMO,MAAM;oBACvDC,QAAQC,GAAG,CAACC,QAAQ,KAAK,8BACvB,6BAACP;oBAAKC,MAAK;oBAAaC,SAAQ;oBAAYC,KAAI;qBAC9C;YAER,OAAO,IAAIK,IAAAA,yBAAe,EAACX,QAAQ;gBACjC,MAAMY,cAAcC,IAAAA,iCAAuB,EAACb;gBAC5C,MAAMc,cACJC,IAAAA,8DAA8B,EAACf,WAAW,MAAM,OAAO;gBACzD,IAAIY,aAAa;oBACfd,cAAcI,IAAI,eAChB,6BAACC;wBACCa,WAAU;wBACVX,SAAS,CAAC,EAAES,cAAc,IAAI,EAAE,KAAK,EAAEF,YAAY,CAAC;wBACpDN,KAAKN,MAAMO,MAAM;;gBAGvB;YACF;QACF;QAEA,MAAMU,UAAUC,IAAAA,8BAAc,EAAC;YAC7BC,gBAAgBC,QAAQ;YACxBC,uBACE,4DACG1B,mBACG,OACAH,6BAAAA,UAAW8B,GAAG,CAAC,CAACC;gBACd,qBAAO,6BAACC;oBAAOlB,KAAKiB,SAASE,GAAG;oBAAG,GAAGF,QAAQ;;YAChD,IACH9B,4BACAK;QAGP;QACAH,mBAAmB;QACnB,OAAOsB;IACT;AACF"}