{"version": 3, "sources": ["../../../src/server/app-render/get-layer-assets.tsx"], "names": ["getLayerAssets", "ctx", "layoutOrPagePath", "injectedCSS", "injectedCSSWithCurrentLayout", "injectedFontPreloadTags", "injectedFontPreloadTagsWithCurrentLayout", "stylesheets", "getCssInlinedLinkTags", "clientReferenceManifest", "preloadedFontFiles", "getPreloadableFonts", "renderOpts", "nextFontManifest", "length", "i", "fontFilename", "ext", "exec", "type", "href", "assetPrefix", "componentMod", "preloadFont", "crossOrigin", "url", "URL", "preconnect", "origin", "error", "styles", "map", "index", "fullHref", "getAssetQueryString", "precedence", "process", "env", "NODE_ENV", "preloadStyle", "link", "rel", "key"], "mappings": ";;;;+BAMgBA;;;eAAAA;;;8DANE;uCACoB;qCACF;qCAEA;;;;;;AAE7B,SAASA,eAAe,EAC7BC,GAAG,EACHC,gBAAgB,EAChBC,aAAaC,4BAA4B,EACzCC,yBAAyBC,wCAAwC,EAMlE;IACC,MAAMC,cAAwBL,mBAC1BM,IAAAA,4CAAqB,EACnBP,IAAIQ,uBAAuB,EAC3BP,kBACAE,8BACA,QAEF,EAAE;IAEN,MAAMM,qBAAqBR,mBACvBS,IAAAA,wCAAmB,EACjBV,IAAIW,UAAU,CAACC,gBAAgB,EAC/BX,kBACAI,4CAEF;IAEJ,IAAII,oBAAoB;QACtB,IAAIA,mBAAmBI,MAAM,EAAE;YAC7B,IAAK,IAAIC,IAAI,GAAGA,IAAIL,mBAAmBI,MAAM,EAAEC,IAAK;gBAClD,MAAMC,eAAeN,kBAAkB,CAACK,EAAE;gBAC1C,MAAME,MAAM,8BAA8BC,IAAI,CAACF,aAAc,CAAC,EAAE;gBAChE,MAAMG,OAAO,CAAC,KAAK,EAAEF,IAAI,CAAC;gBAC1B,MAAMG,OAAO,CAAC,EAAEnB,IAAIoB,WAAW,CAAC,OAAO,EAAEL,aAAa,CAAC;gBACvDf,IAAIqB,YAAY,CAACC,WAAW,CAACH,MAAMD,MAAMlB,IAAIW,UAAU,CAACY,WAAW;YACrE;QACF,OAAO;YACL,IAAI;gBACF,IAAIC,MAAM,IAAIC,IAAIzB,IAAIoB,WAAW;gBACjCpB,IAAIqB,YAAY,CAACK,UAAU,CAACF,IAAIG,MAAM,EAAE;YAC1C,EAAE,OAAOC,OAAO;gBACd,mEAAmE;gBACnE,8CAA8C;gBAC9C5B,IAAIqB,YAAY,CAACK,UAAU,CAAC,KAAK;YACnC;QACF;IACF;IAEA,MAAMG,SAASvB,cACXA,YAAYwB,GAAG,CAAC,CAACX,MAAMY;QACrB,iEAAiE;QACjE,kDAAkD;QAClD,mDAAmD;QACnD,mEAAmE;QACnE,mEAAmE;QACnE,cAAc;QACd,MAAMC,WAAW,CAAC,EAAEhC,IAAIoB,WAAW,CAAC,OAAO,EAAED,KAAK,EAAEc,IAAAA,wCAAmB,EACrEjC,KACA,MACA,CAAC;QAEH,gEAAgE;QAChE,oEAAoE;QACpE,2DAA2D;QAC3D,iEAAiE;QACjE,0DAA0D;QAC1D,+CAA+C;QAC/C,MAAMkC,aACJC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,UAAUlB,OAAO;QAE5DnB,IAAIqB,YAAY,CAACiB,YAAY,CAACN,UAAUhC,IAAIW,UAAU,CAACY,WAAW;QAElE,qBACE,6BAACgB;YACCC,KAAI;YACJrB,MAAMa;YACN,aAAa;YACbE,YAAYA;YACZX,aAAavB,IAAIW,UAAU,CAACY,WAAW;YACvCkB,KAAKV;;IAGX,KACA;IAEJ,OAAOF;AACT"}