{"version": 3, "sources": ["../../../src/server/app-render/action-handler.ts"], "names": ["handleAction", "nodeToWebReadableStream", "nodeReadable", "process", "env", "NEXT_RUNTIME", "Readable", "require", "toWeb", "ReadableStream", "start", "controller", "on", "chunk", "enqueue", "close", "error", "Error", "formDataFromSearchQueryString", "query", "searchParams", "URLSearchParams", "formData", "FormData", "key", "value", "append", "nodeHeadersToRecord", "headers", "record", "Object", "entries", "undefined", "Array", "isArray", "join", "getForwardedHeaders", "req", "res", "requestHeaders", "requestCookies", "responseHeaders", "getHeaders", "rawSetCookies", "setCookies", "map", "<PERSON><PERSON><PERSON><PERSON>", "cookie", "split", "mergedHeaders", "filterReqHeaders", "actionsForbiddenHeaders", "mergedCookies", "concat", "Headers", "addRevalidationHeader", "staticGenerationStore", "requestStore", "Promise", "all", "pendingRevalidates", "isTagRevalidated", "revalidatedTags", "length", "isCookieRevalidated", "getModifiedCookieValues", "mutableCookies", "<PERSON><PERSON><PERSON><PERSON>", "JSON", "stringify", "createRedirectRenderResult", "redirectUrl", "startsWith", "forwardedHeaders", "set", "RSC", "host", "proto", "incrementalCache", "requestProtocol", "fetchUrl", "URL", "NEXT_CACHE_REVALIDATED_TAGS_HEADER", "NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER", "prerenderManifest", "preview", "previewModeId", "delete", "headResponse", "fetch", "method", "next", "internal", "get", "RSC_CONTENT_TYPE_HEADER", "response", "includes", "FlightRenderResult", "body", "err", "console", "RenderResult", "ComponentMod", "page", "serverActionsManifest", "generateFlight", "serverActionsBodySizeLimit", "ctx", "actionId", "ACTION", "toLowerCase", "contentType", "isURLEncodedAction", "isMultipartAction", "isFetchAction", "originHostname", "warn", "statusCode", "promise", "reject", "type", "result", "actionResult", "skipFlight", "pathWasRevalidated", "bound", "worker<PERSON>ame", "serverModuleMap", "Proxy", "_", "id", "workers", "name", "chunks", "actionAsyncStorage", "formState", "run", "isAction", "decodeReply", "decodeAction", "decodeFormState", "webRequest", "request", "action", "actionReturnedState", "actionData", "reader", "<PERSON><PERSON><PERSON><PERSON>", "done", "read", "TextDecoder", "decode", "decodeReplyFromBusboy", "busboy", "bb", "pipe", "UndiciRequest", "Request", "fakeRequest", "duplex", "parseBody", "e", "message", "actionWorkers", "actionModId", "actionHandler", "__next_app__", "returnVal", "apply", "resolve", "isRedirectError", "getURLFromRedirectError", "appendMutableCookies", "from", "values", "isNotFoundError", "asNotFound"], "mappings": ";;;;+BA8OsBA;;;eAAAA;;;kCAhOf;0BACyB;0BAIzB;qEACkB;oCAEU;uBAK5B;gCAIA;2BAMA;;;;;;AAGP,SAASC,wBAAwBC,YAAuC;IACtE,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;QACvC,MAAM,EAAEC,QAAQ,EAAE,GAAGC,QAAQ;QAC7B,IAAI,WAAWD,YAAY,OAAOA,SAASE,KAAK,KAAK,YAAY;YAC/D,OAAOF,SAASE,KAAK,CAACN;QACxB;QAEA,OAAO,IAAIO,eAAe;YACxBC,OAAMC,UAAU;gBACdT,aAAaU,EAAE,CAAC,QAAQ,CAACC;oBACvBF,WAAWG,OAAO,CAACD;gBACrB;gBAEAX,aAAaU,EAAE,CAAC,OAAO;oBACrBD,WAAWI,KAAK;gBAClB;gBAEAb,aAAaU,EAAE,CAAC,SAAS,CAACI;oBACxBL,WAAWK,KAAK,CAACA;gBACnB;YACF;QACF;IACF,OAAO;QACL,MAAM,IAAIC,MAAM;IAClB;AACF;AAEA,SAASC,8BAA8BC,KAAa;IAClD,MAAMC,eAAe,IAAIC,gBAAgBF;IACzC,MAAMG,WAAW,IAAIC;IACrB,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIL,aAAc;QACvCE,SAASI,MAAM,CAACF,KAAKC;IACvB;IACA,OAAOH;AACT;AAEA,SAASK,oBACPC,OAAkD;IAElD,MAAMC,SAAiC,CAAC;IACxC,KAAK,MAAM,CAACL,KAAKC,MAAM,IAAIK,OAAOC,OAAO,CAACH,SAAU;QAClD,IAAIH,UAAUO,WAAW;YACvBH,MAAM,CAACL,IAAI,GAAGS,MAAMC,OAAO,CAACT,SAASA,MAAMU,IAAI,CAAC,QAAQ,CAAC,EAAEV,MAAM,CAAC;QACpE;IACF;IACA,OAAOI;AACT;AAEA,SAASO,oBACPC,GAAoB,EACpBC,GAAmB;IAEnB,kCAAkC;IAClC,MAAMC,iBAAiBF,IAAIT,OAAO;IAClC,MAAMY,iBAAiBD,cAAc,CAAC,SAAS,IAAI;IAEnD,6CAA6C;IAC7C,MAAME,kBAAkBH,IAAII,UAAU;IACtC,MAAMC,gBAAgBF,eAAe,CAAC,aAAa;IACnD,MAAMG,aAAa,AACjBX,CAAAA,MAAMC,OAAO,CAACS,iBAAiBA,gBAAgB;QAACA;KAAc,AAAD,EAC7DE,GAAG,CAAC,CAACC;QACL,qDAAqD;QACrD,MAAM,CAACC,OAAO,GAAG,CAAC,EAAED,UAAU,CAAC,CAACE,KAAK,CAAC;QACtC,OAAOD;IACT;IAEA,qCAAqC;IACrC,MAAME,gBAAgBC,IAAAA,uBAAgB,EACpC;QACE,GAAGvB,oBAAoBY,eAAe;QACtC,GAAGZ,oBAAoBc,gBAAgB;IACzC,GACAU,8BAAuB;IAGzB,gBAAgB;IAChB,MAAMC,gBAAgBZ,eAAeQ,KAAK,CAAC,MAAMK,MAAM,CAACT,YAAYT,IAAI,CAAC;IAEzE,qDAAqD;IACrDc,aAAa,CAAC,SAAS,GAAGG;IAE1B,8CAA8C;IAC9C,OAAOH,aAAa,CAAC,oBAAoB;IAEzC,OAAO,IAAIK,QAAQL;AACrB;AAEA,eAAeM,sBACbjB,GAAmB,EACnB,EACEkB,qBAAqB,EACrBC,YAAY,EAIb;QAiBwBD;IAfzB,MAAME,QAAQC,GAAG,CAACH,sBAAsBI,kBAAkB,IAAI,EAAE;IAEhE,0EAA0E;IAC1E,+EAA+E;IAC/E,2DAA2D;IAE3D,mDAAmD;IACnD,8EAA8E;IAC9E,4BAA4B;IAE5B,2EAA2E;IAC3E,uEAAuE;IACvE,oFAAoF;IACpF,mBAAmB;IAEnB,MAAMC,mBAAmBL,EAAAA,yCAAAA,sBAAsBM,eAAe,qBAArCN,uCAAuCO,MAAM,IAAG,IAAI;IAC7E,MAAMC,sBAAsBC,IAAAA,uCAAuB,EACjDR,aAAaS,cAAc,EAC3BH,MAAM,GACJ,IACA;IAEJzB,IAAI6B,SAAS,CACX,wBACAC,KAAKC,SAAS,CAAC;QAAC,EAAE;QAAER;QAAkBG;KAAoB;AAE9D;AAEA,eAAeM,2BACbjC,GAAoB,EACpBC,GAAmB,EACnBiC,WAAmB,EACnBf,qBAA4C;IAE5ClB,IAAI6B,SAAS,CAAC,qBAAqBI;IACnC,4EAA4E;IAC5E,IAAIA,YAAYC,UAAU,CAAC,MAAM;YAM7BhB;QALF,MAAMiB,mBAAmBrC,oBAAoBC,KAAKC;QAClDmC,iBAAiBC,GAAG,CAACC,qBAAG,EAAE;QAE1B,MAAMC,OAAOvC,IAAIT,OAAO,CAAC,OAAO;QAChC,MAAMiD,QACJrB,EAAAA,0CAAAA,sBAAsBsB,gBAAgB,qBAAtCtB,wCAAwCuB,eAAe,KAAI;QAC7D,MAAMC,WAAW,IAAIC,IAAI,CAAC,EAAEJ,MAAM,GAAG,EAAED,KAAK,EAAEL,YAAY,CAAC;QAE3D,IAAIf,sBAAsBM,eAAe,EAAE;gBAOvCN,mEAAAA,2DAAAA;YANFiB,iBAAiBC,GAAG,CAClBQ,6CAAkC,EAClC1B,sBAAsBM,eAAe,CAAC3B,IAAI,CAAC;YAE7CsC,iBAAiBC,GAAG,CAClBS,iDAAsC,EACtC3B,EAAAA,2CAAAA,sBAAsBsB,gBAAgB,sBAAtCtB,4DAAAA,yCAAwC4B,iBAAiB,sBAAzD5B,oEAAAA,0DAA2D6B,OAAO,qBAAlE7B,kEACI8B,aAAa,KAAI;QAEzB;QAEA,6FAA6F;QAC7F,kDAAkD;QAClDb,iBAAiBc,MAAM,CAAC;QACxB,IAAI;QAEJ,IAAI;YACF,MAAMC,eAAe,MAAMC,MAAMT,UAAU;gBACzCU,QAAQ;gBACR9D,SAAS6C;gBACTkB,MAAM;oBACJ,aAAa;oBACbC,UAAU;gBACZ;YACF;YAEA,IACEJ,aAAa5D,OAAO,CAACiE,GAAG,CAAC,oBAAoBC,yCAAuB,EACpE;gBACA,MAAMC,WAAW,MAAMN,MAAMT,UAAU;oBACrCU,QAAQ;oBACR9D,SAAS6C;oBACTkB,MAAM;wBACJ,aAAa;wBACbC,UAAU;oBACZ;gBACF;gBACA,4EAA4E;gBAC5E,KAAK,MAAM,CAACpE,KAAKC,MAAM,IAAIsE,SAASnE,OAAO,CAAE;oBAC3C,IAAI,CAACuB,8BAAuB,CAAC6C,QAAQ,CAACxE,MAAM;wBAC1Cc,IAAI6B,SAAS,CAAC3C,KAAKC;oBACrB;gBACF;gBAEA,OAAO,IAAIwE,sCAAkB,CAACF,SAASG,IAAI;YAC7C;QACF,EAAE,OAAOC,KAAK;YACZ,+EAA+E;YAC/EC,QAAQpF,KAAK,CAAC,CAAC,+BAA+B,CAAC,EAAEmF;QACnD;IACF;IACA,OAAO,IAAIE,qBAAY,CAACjC,KAAKC,SAAS,CAAC,CAAC;AAC1C;AAEO,eAAerE,aAAa,EACjCqC,GAAG,EACHC,GAAG,EACHgE,YAAY,EACZC,IAAI,EACJC,qBAAqB,EACrBC,cAAc,EACdjD,qBAAqB,EACrBC,YAAY,EACZiD,0BAA0B,EAC1BC,GAAG,EAYJ;IAWC,IAAIC,WAAWvE,IAAIT,OAAO,CAACiF,wBAAM,CAACC,WAAW,GAAG;IAChD,MAAMC,cAAc1E,IAAIT,OAAO,CAAC,eAAe;IAC/C,MAAMoF,qBACJ3E,IAAIqD,MAAM,KAAK,UAAUqB,gBAAgB;IAC3C,MAAME,oBACJ5E,IAAIqD,MAAM,KAAK,WAAUqB,+BAAAA,YAAavC,UAAU,CAAC;IAEnD,MAAM0C,gBACJN,aAAa5E,aACb,OAAO4E,aAAa,YACpBvE,IAAIqD,MAAM,KAAK;IAEjB,8CAA8C;IAC9C,IAAI,CAAEwB,CAAAA,iBAAiBF,sBAAsBC,iBAAgB,GAAI;QAC/D;IACF;IAEA,MAAME,iBACJ,OAAO9E,IAAIT,OAAO,CAAC,SAAS,KAAK,WAC7B,IAAIqD,IAAI5C,IAAIT,OAAO,CAAC,SAAS,EAAEgD,IAAI,GACnC5C;IACN,MAAM4C,OAAOvC,IAAIT,OAAO,CAAC,mBAAmB,IAAIS,IAAIT,OAAO,CAAC,OAAO;IAEnE,4EAA4E;IAC5E,wDAAwD;IACxD,IAAI,CAACuF,gBAAgB;QACnB,0EAA0E;QAC1E,aAAa;QACbf,QAAQgB,IAAI,CACV;IAEJ,OAAO,IAAI,CAACxC,QAAQuC,mBAAmBvC,MAAM;QAC3C,uDAAuD;QACvDwB,QAAQpF,KAAK,CACX;QAGF,MAAMA,QAAQ,IAAIC,MAAM;QAExB,IAAIiG,eAAe;YACjB5E,IAAI+E,UAAU,GAAG;YACjB,MAAM3D,QAAQC,GAAG,CAACH,sBAAsBI,kBAAkB,IAAI,EAAE;YAChE,MAAM0D,UAAU5D,QAAQ6D,MAAM,CAACvG;YAC/B,IAAI;gBACF,MAAMsG;YACR,EAAE,OAAM,CAAC;YAET,OAAO;gBACLE,MAAM;gBACNC,QAAQ,MAAMhB,eAAeE,KAAK;oBAChCe,cAAcJ;oBACd,6EAA6E;oBAC7EK,YAAY,CAACnE,sBAAsBoE,kBAAkB;gBACvD;YACF;QACF;QAEA,MAAM5G;IACR;IAEA,sDAAsD;IACtDsB,IAAI6B,SAAS,CACX,iBACA;IAEF,IAAI0D,QAAQ,EAAE;IAEd,MAAMC,aAAa,QAAQvB;IAC3B,MAAMwB,kBAAkB,IAAIC,MAC1B,CAAC,GACD;QACEnC,KAAK,CAACoC,GAAGC;YACP,OAAO;gBACLA,IAAI1B,qBAAqB,CACvBrG,QAAQC,GAAG,CAACC,YAAY,KAAK,SAAS,SAAS,OAChD,CAAC6H,GAAG,CAACC,OAAO,CAACL,WAAW;gBACzBM,MAAMF;gBACNG,QAAQ,EAAE;YACZ;QACF;IACF;IAGF,MAAM,EAAEC,kBAAkB,EAAE,GAAGhC;IAI/B,IAAIoB;IACJ,IAAIa;IAEJ,IAAI;QACF,MAAMD,mBAAmBE,GAAG,CAAC;YAAEC,UAAU;QAAK,GAAG;YAC/C,IAAItI,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;gBACvC,2CAA2C;gBAC3C,MAAM,EAAEqI,WAAW,EAAEC,YAAY,EAAEC,eAAe,EAAE,GAAGtC;gBAEvD,MAAMuC,aAAaxG;gBACnB,IAAI,CAACwG,WAAW3C,IAAI,EAAE;oBACpB,MAAM,IAAIjF,MAAM;gBAClB;gBAEA,IAAIgG,mBAAmB;oBACrB,kCAAkC;oBAClC,MAAM3F,WAAW,MAAMuH,WAAWC,OAAO,CAACxH,QAAQ;oBAClD,IAAI4F,eAAe;wBACjBW,QAAQ,MAAMa,YAAYpH,UAAUyG;oBACtC,OAAO;wBACL,MAAMgB,SAAS,MAAMJ,aAAarH,UAAUyG;wBAC5C,MAAMiB,sBAAsB,MAAMD;wBAClCR,YAAYK,gBAAgBI,qBAAqB1H;wBAEjD,sBAAsB;wBACtB;oBACF;gBACF,OAAO;oBACL,IAAI2H,aAAa;oBAEjB,MAAMC,SAASL,WAAW3C,IAAI,CAACiD,SAAS;oBACxC,MAAO,KAAM;wBACX,MAAM,EAAEC,IAAI,EAAE3H,KAAK,EAAE,GAAG,MAAMyH,OAAOG,IAAI;wBACzC,IAAID,MAAM;4BACR;wBACF;wBAEAH,cAAc,IAAIK,cAAcC,MAAM,CAAC9H;oBACzC;oBAEA,IAAIuF,oBAAoB;wBACtB,MAAM1F,WAAWJ,8BAA8B+H;wBAC/CpB,QAAQ,MAAMa,YAAYpH,UAAUyG;oBACtC,OAAO;wBACLF,QAAQ,MAAMa,YAAYO,YAAYlB;oBACxC;gBACF;YACF,OAAO;gBACL,oEAAoE;gBACpE,MAAM,EACJW,WAAW,EACXc,qBAAqB,EACrBb,YAAY,EACZC,eAAe,EAChB,GAAGrI,QAAQ,CAAC,mBAAmB,CAAC;gBAEjC,IAAI0G,mBAAmB;oBACrB,IAAIC,eAAe;wBACjB,MAAMuC,SAASlJ,QAAQ;wBACvB,MAAMmJ,KAAKD,OAAO;4BAAE7H,SAASS,IAAIT,OAAO;wBAAC;wBACzCS,IAAIsH,IAAI,CAACD;wBAET7B,QAAQ,MAAM2B,sBAAsBE,IAAI3B;oBAC1C,OAAO;wBACL,6DAA6D;wBAC7D,0CAA0C;wBAC1C,MAAM6B,gBAAgBrJ,QAAQ,6BAA6BsJ,OAAO;wBAClE,MAAMC,cAAc,IAAIF,cAAc,oBAAoB;4BACxDlE,QAAQ;4BACR9D,SAAS;gCAAE,gBAAgBS,IAAIT,OAAO,CAAC,eAAe;4BAAC;4BACvDsE,MAAMjG,wBAAwBoC;4BAC9B0H,QAAQ;wBACV;wBACA,MAAMzI,WAAW,MAAMwI,YAAYxI,QAAQ;wBAC3C,MAAMyH,SAAS,MAAMJ,aAAarH,UAAUyG;wBAC5C,MAAMiB,sBAAsB,MAAMD;wBAClCR,YAAY,MAAMK,gBAAgBI,qBAAqB1H;wBAEvD,sBAAsB;wBACtB;oBACF;gBACF,OAAO;oBACL,MAAM,EAAE0I,SAAS,EAAE,GACjBzJ,QAAQ;oBAEV,IAAI0I;oBACJ,IAAI;wBACFA,aACE,AAAC,MAAMe,UAAU3H,KAAKqE,8BAA8B,UAAW;oBACnE,EAAE,OAAOuD,GAAQ;wBACf,IAAIA,KAAK,AAACA,EAAe5C,UAAU,KAAK,KAAK;4BAC3C,0BAA0B;4BAC1B4C,EAAEC,OAAO,GACPD,EAAEC,OAAO,GACT;wBACJ;wBACA,MAAMD;oBACR;oBAEA,IAAIjD,oBAAoB;wBACtB,MAAM1F,WAAWJ,8BAA8B+H;wBAC/CpB,QAAQ,MAAMa,YAAYpH,UAAUyG;oBACtC,OAAO;wBACLF,QAAQ,MAAMa,YAAYO,YAAYlB;oBACxC;gBACF;YACF;YAEA,aAAa;YACb,cAAc;YACd,mBAAmB;YACnB,iBAAiB;YAEjB,kBAAkB;YAClB,mBAAmB;YACnB,gBAAgB;YAEhB,wEAAwE;YACxE,8EAA8E;YAE9E,2CAA2C;YAC3C,MAAMoC,gBACJ3D,qBAAqB,CACnBrG,QAAQC,GAAG,CAACC,YAAY,KAAK,SAAS,SAAS,OAChD,CAACuG,SAAS;YAEb,IAAI,CAACuD,eAAe;gBAClB,yEAAyE;gBACzE,8EAA8E;gBAC9E/D,QAAQpF,KAAK,CACX,CAAC,8BAA8B,EAAE4F,SAAS,2DAA2D,CAAC;gBAExG,OAAO;oBACLY,MAAM;gBACR;YACF;YAEA,MAAM4C,cAAcD,cAAchC,OAAO,CAACL,WAAW;YACrD,MAAMuC,gBACJ/D,aAAagE,YAAY,CAAC/J,OAAO,CAAC6J,YAAY,CAACxD,SAAS;YAE1D,MAAM2D,YAAY,MAAMF,cAAcG,KAAK,CAAC,MAAM3C;YAElD,4DAA4D;YAC5D,IAAIX,eAAe;gBACjB,MAAM3D,sBAAsBjB,KAAK;oBAC/BkB;oBACAC;gBACF;gBAEAiE,eAAe,MAAMjB,eAAeE,KAAK;oBACvCe,cAAchE,QAAQ+G,OAAO,CAACF;oBAC9B,6EAA6E;oBAC7E5C,YAAY,CAACnE,sBAAsBoE,kBAAkB;gBACvD;YACF;QACF;QAEA,OAAO;YACLJ,MAAM;YACNC,QAAQC;YACRa;QACF;IACF,EAAE,OAAOpC,KAAK;QACZ,IAAIuE,IAAAA,yBAAe,EAACvE,MAAM;YACxB,MAAM5B,cAAcoG,IAAAA,iCAAuB,EAACxE;YAE5C,qEAAqE;YACrE,2CAA2C;YAC3C,MAAM5C,sBAAsBjB,KAAK;gBAC/BkB;gBACAC;YACF;YAEA,IAAIyD,eAAe;gBACjB,OAAO;oBACLM,MAAM;oBACNC,QAAQ,MAAMnD,2BACZjC,KACAC,KACAiC,aACAf;gBAEJ;YACF;YAEA,IAAI2C,IAAIjC,cAAc,EAAE;gBACtB,MAAMtC,UAAU,IAAI0B;gBAEpB,gEAAgE;gBAChE,YAAY;gBACZ,IAAIsH,IAAAA,oCAAoB,EAAChJ,SAASuE,IAAIjC,cAAc,GAAG;oBACrD5B,IAAI6B,SAAS,CAAC,cAAclC,MAAM4I,IAAI,CAACjJ,QAAQkJ,MAAM;gBACvD;YACF;YAEAxI,IAAI6B,SAAS,CAAC,YAAYI;YAC1BjC,IAAI+E,UAAU,GAAG;YACjB,OAAO;gBACLG,MAAM;gBACNC,QAAQ,IAAIpB,qBAAY,CAAC;YAC3B;QACF,OAAO,IAAI0E,IAAAA,yBAAe,EAAC5E,MAAM;YAC/B7D,IAAI+E,UAAU,GAAG;YAEjB,MAAM9D,sBAAsBjB,KAAK;gBAC/BkB;gBACAC;YACF;YAEA,IAAIyD,eAAe;gBACjB,MAAMI,UAAU5D,QAAQ6D,MAAM,CAACpB;gBAC/B,IAAI;oBACF,MAAMmB;gBACR,EAAE,OAAM,CAAC;gBACT,OAAO;oBACLE,MAAM;oBACNC,QAAQ,MAAMhB,eAAeE,KAAK;wBAChCgB,YAAY;wBACZD,cAAcJ;wBACd0D,YAAY;oBACd;gBACF;YACF;YACA,OAAO;gBACLxD,MAAM;YACR;QACF;QAEA,IAAIN,eAAe;YACjB5E,IAAI+E,UAAU,GAAG;YACjB,MAAM3D,QAAQC,GAAG,CAACH,sBAAsBI,kBAAkB,IAAI,EAAE;YAChE,MAAM0D,UAAU5D,QAAQ6D,MAAM,CAACpB;YAC/B,IAAI;gBACF,MAAMmB;YACR,EAAE,OAAM,CAAC;YAET,OAAO;gBACLE,MAAM;gBACNC,QAAQ,MAAMhB,eAAeE,KAAK;oBAChCe,cAAcJ;oBACd,6EAA6E;oBAC7EK,YAAY,CAACnE,sBAAsBoE,kBAAkB;gBACvD;YACF;QACF;QAEA,MAAMzB;IACR;AACF"}