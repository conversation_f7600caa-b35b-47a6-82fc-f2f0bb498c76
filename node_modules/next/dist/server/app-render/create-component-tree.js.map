{"version": 3, "sources": ["../../../src/server/app-render/create-component-tree.tsx"], "names": ["createComponentTree", "createSegmentPath", "loaderTree", "tree", "parentParams", "firstItem", "rootLayoutIncluded", "injectedCSS", "injectedFontPreloadTags", "asNotFound", "metadataOutlet", "ctx", "renderOpts", "nextConfigOutput", "staticGenerationStore", "componentMod", "staticGenerationBailout", "NotFoundBoundary", "LayoutRouter", "RenderFromTemplateContext", "StaticGenerationSearchParamsBailoutProvider", "serverHooks", "DynamicServerError", "pagePath", "getDynamicParamFromSegment", "query", "isPrefetch", "searchParamsProps", "page", "layoutOrPagePath", "segment", "components", "parallelRoutes", "parseLoaderTree", "layout", "template", "error", "loading", "notFound", "injectedCSSWithCurrentLayout", "Set", "injectedFontPreloadTagsWithCurrentLayout", "styles", "getLayerAssets", "Template", "templateStyles", "createComponentAndStyles", "filePath", "getComponent", "React", "Fragment", "ErrorComponent", "errorStyles", "Loading", "loadingStyles", "isLayout", "isPage", "layoutOrPageMod", "getLayoutOrPageModule", "rootLayoutAtThisLevel", "rootLayoutIncludedAtThisLevelOrAbove", "NotFound", "notFoundStyles", "dynamic", "forceDynamic", "dynamicShouldError", "link", "forceStatic", "fetchCache", "revalidate", "defaultRevalidate", "isStaticGeneration", "dynamicUsageDescription", "dynamicUsageErr", "LayoutOrPage", "interopDefault", "undefined", "Component", "parallelKeys", "Object", "keys", "hasSlot<PERSON>ey", "length", "componentProps", "NotFoundComponent", "RootLayoutComponent", "process", "env", "NODE_ENV", "isValidElementType", "require", "Error", "segmentParam", "currentParams", "value", "param", "actualSegment", "treeSegment", "parallelRouteMap", "Promise", "all", "map", "parallelRouteKey", "isChildrenRoute<PERSON>ey", "currentSegmentPath", "parallelRoute", "childSegment", "childSegmentParam", "notFoundComponent", "getParallelRoutePair", "currentChildProp", "currentStyles", "parallel<PERSON><PERSON>er<PERSON>ey", "segmentPath", "hasLoading", "Boolean", "childProp", "childElement", "childPropSegment", "addSearchParamsIfPageSegment", "hasLoadingComponentInTree", "ChildComponent", "childComponentStyles", "child", "current", "parallelRouteComponents", "reduce", "list", "Comp", "children", "isClientComponent", "isClientReference", "meta", "name", "content", "props", "params", "resolve", "then", "preloadComponent", "propsForComponent"], "mappings": ";;;;+BAkBsBA;;;eAAAA;;;8DAjBJ;iCACgB;8BACI;gCAEP;kCACE;uDACY;iCACb;0CAES;gCACV;2CACW;;;;;;AAMnC,eAAeA,oBAAoB,EACxCC,iBAAiB,EACjBC,YAAYC,IAAI,EAChBC,YAAY,EACZC,SAAS,EACTC,kBAAkB,EAClBC,WAAW,EACXC,uBAAuB,EACvBC,UAAU,EACVC,cAAc,EACdC,GAAG,EAYJ;IAIC,MAAM,EACJC,YAAY,EAAEC,gBAAgB,EAAE,EAChCC,qBAAqB,EACrBC,cAAc,EACZC,uBAAuB,EACvBC,gBAAgB,EAChBC,YAAY,EACZC,yBAAyB,EACzBC,2CAA2C,EAC3CC,aAAa,EAAEC,kBAAkB,EAAE,EACpC,EACDC,QAAQ,EACRC,0BAA0B,EAC1BC,KAAK,EACLC,UAAU,EACVC,iBAAiB,EAClB,GAAGhB;IAEJ,MAAM,EAAEiB,IAAI,EAAEC,gBAAgB,EAAEC,OAAO,EAAEC,UAAU,EAAEC,cAAc,EAAE,GACnEC,IAAAA,gCAAe,EAAC9B;IAElB,MAAM,EAAE+B,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,OAAO,EAAE,aAAaC,QAAQ,EAAE,GAAGP;IAEpE,MAAMQ,+BAA+B,IAAIC,IAAIjC;IAC7C,MAAMkC,2CAA2C,IAAID,IACnDhC;IAGF,MAAMkC,SAASC,IAAAA,8BAAc,EAAC;QAC5BhC;QACAkB;QACAtB,aAAagC;QACb/B,yBAAyBiC;IAC3B;IAEA,MAAM,CAACG,UAAUC,eAAe,GAAGV,WAC/B,MAAMW,IAAAA,kDAAwB,EAAC;QAC7BnC;QACAoC,UAAUZ,QAAQ,CAAC,EAAE;QACrBa,cAAcb,QAAQ,CAAC,EAAE;QACzB5B,aAAagC;IACf,KACA;QAACU,cAAK,CAACC,QAAQ;KAAC;IAEpB,MAAM,CAACC,gBAAgBC,YAAY,GAAGhB,QAClC,MAAMU,IAAAA,kDAAwB,EAAC;QAC7BnC;QACAoC,UAAUX,KAAK,CAAC,EAAE;QAClBY,cAAcZ,KAAK,CAAC,EAAE;QACtB7B,aAAagC;IACf,KACA,EAAE;IAEN,MAAM,CAACc,SAASC,cAAc,GAAGjB,UAC7B,MAAMS,IAAAA,kDAAwB,EAAC;QAC7BnC;QACAoC,UAAUV,OAAO,CAAC,EAAE;QACpBW,cAAcX,OAAO,CAAC,EAAE;QACxB9B,aAAagC;IACf,KACA,EAAE;IAEN,MAAMgB,WAAW,OAAOrB,WAAW;IACnC,MAAMsB,SAAS,OAAO5B,SAAS;IAC/B,MAAM,CAAC6B,gBAAgB,GAAG,MAAMC,IAAAA,mCAAqB,EAACvD;IAEtD;;GAEC,GACD,MAAMwD,wBAAwBJ,YAAY,CAACjD;IAC3C;;GAEC,GACD,MAAMsD,uCACJtD,sBAAsBqD;IAExB,MAAM,CAACE,UAAUC,eAAe,GAAGxB,WAC/B,MAAMQ,IAAAA,kDAAwB,EAAC;QAC7BnC;QACAoC,UAAUT,QAAQ,CAAC,EAAE;QACrBU,cAAcV,QAAQ,CAAC,EAAE;QACzB/B,aAAagC;IACf,KACA,EAAE;IAEN,IAAIwB,UAAUN,mCAAAA,gBAAiBM,OAAO;IAEtC,IAAIlD,qBAAqB,UAAU;QACjC,IAAI,CAACkD,WAAWA,YAAY,QAAQ;YAClCA,UAAU;QACZ,OAAO,IAAIA,YAAY,iBAAiB;YACtCjD,sBAAsBkD,YAAY,GAAG;YACrClD,sBAAsBmD,kBAAkB,GAAG;YAC3CjD,wBAAwB,CAAC,cAAc,CAAC,EAAE;gBACxC+C;gBACAG,MAAM;YACR;QACF;IACF;IAEA,IAAI,OAAOH,YAAY,UAAU;QAC/B,sDAAsD;QACtD,sDAAsD;QACtD,YAAY;QACZ,IAAIA,YAAY,SAAS;YACvBjD,sBAAsBmD,kBAAkB,GAAG;QAC7C,OAAO,IAAIF,YAAY,iBAAiB;YACtCjD,sBAAsBkD,YAAY,GAAG;YACrChD,wBAAwB,CAAC,aAAa,CAAC,EAAE;gBAAE+C;YAAQ;QACrD,OAAO;YACLjD,sBAAsBmD,kBAAkB,GAAG;YAC3C,IAAIF,YAAY,gBAAgB;gBAC9BjD,sBAAsBqD,WAAW,GAAG;YACtC,OAAO;gBACLrD,sBAAsBqD,WAAW,GAAG;YACtC;QACF;IACF;IAEA,IAAI,QAAOV,mCAAAA,gBAAiBW,UAAU,MAAK,UAAU;QACnDtD,sBAAsBsD,UAAU,GAAGX,mCAAAA,gBAAiBW,UAAU;IAChE;IAEA,IAAI,QAAOX,mCAAAA,gBAAiBY,UAAU,MAAK,UAAU;QACnD1D,IAAI2D,iBAAiB,GAAGb,gBAAgBY,UAAU;QAElD,IACE,OAAOvD,sBAAsBuD,UAAU,KAAK,eAC3C,OAAOvD,sBAAsBuD,UAAU,KAAK,YAC3CvD,sBAAsBuD,UAAU,GAAG1D,IAAI2D,iBAAiB,EAC1D;YACAxD,sBAAsBuD,UAAU,GAAG1D,IAAI2D,iBAAiB;QAC1D;QAEA,IACExD,sBAAsByD,kBAAkB,IACxC5D,IAAI2D,iBAAiB,KAAK,GAC1B;YACA,MAAME,0BAA0B,CAAC,yBAAyB,EAAE1C,QAAQ,CAAC;YACrEhB,sBAAsB0D,uBAAuB,GAAGA;YAEhD,MAAM,IAAIlD,mBAAmBkD;QAC/B;IACF;IAEA,IAAI1D,yCAAAA,sBAAuB2D,eAAe,EAAE;QAC1C,MAAM3D,sBAAsB2D,eAAe;IAC7C;IAEA,MAAMC,eAAejB,kBACjBkB,IAAAA,8BAAc,EAAClB,mBACfmB;IAEJ;;GAEC,GACD,IAAIC,YAAYH;IAChB,MAAMI,eAAeC,OAAOC,IAAI,CAAChD;IACjC,MAAMiD,aAAaH,aAAaI,MAAM,GAAG;IAEzC,IAAID,cAActB,uBAAuB;QACvCkB,YAAY,CAACM;YACX,MAAMC,oBAAoBvB;YAC1B,MAAMwB,sBAAsBX;YAC5B,qBACE,6BAACzD;gBACCqB,wBACE,4DACGI,sBACD,6BAAC2C,2BACEvB,8BACD,6BAACsB;6BAKP,6BAACC,qBAAwBF;QAG/B;IACF;IAEA,IAAIG,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,MAAM,EAAEC,kBAAkB,EAAE,GAAGC,QAAQ;QACvC,IACE,AAAClC,CAAAA,UAAU,OAAOqB,cAAc,WAAU,KAC1C,CAACY,mBAAmBZ,YACpB;YACA,MAAM,IAAIc,MACR,CAAC,sDAAsD,EAAEpE,SAAS,CAAC,CAAC;QAExE;QAEA,IACE,OAAO4B,mBAAmB,eAC1B,CAACsC,mBAAmBtC,iBACpB;YACA,MAAM,IAAIwC,MACR,CAAC,8DAA8D,EAAE7D,QAAQ,CAAC;QAE9E;QAEA,IAAI,OAAOuB,YAAY,eAAe,CAACoC,mBAAmBpC,UAAU;YAClE,MAAM,IAAIsC,MACR,CAAC,0DAA0D,EAAE7D,QAAQ,CAAC;QAE1E;QAEA,IAAI,OAAO+B,aAAa,eAAe,CAAC4B,mBAAmB5B,WAAW;YACpE,MAAM,IAAI8B,MACR,CAAC,2DAA2D,EAAE7D,QAAQ,CAAC;QAE3E;IACF;IAEA,iCAAiC;IACjC,MAAM8D,eAAepE,2BAA2BM;IAChD;;GAEC,GACD,MAAM+D,gBACJ,mDAAmD;IACnDD,gBAAgBA,aAAaE,KAAK,KAAK,OACnC;QACE,GAAG1F,YAAY;QACf,CAACwF,aAAaG,KAAK,CAAC,EAAEH,aAAaE,KAAK;IAC1C,IAEA1F;IACN,4BAA4B;IAC5B,MAAM4F,gBAAgBJ,eAAeA,aAAaK,WAAW,GAAGnE;IAEhE,mHAAmH;IACnH,MAAMoE,mBAAmB,MAAMC,QAAQC,GAAG,CACxCrB,OAAOC,IAAI,CAAChD,gBAAgBqE,GAAG,CAC7B,OAAOC;QACL,MAAMC,qBAAqBD,qBAAqB;QAChD,MAAME,qBAAwCnG,YAC1C;YAACiG;SAAiB,GAClB;YAACN;YAAeM;SAAiB;QAErC,MAAMG,gBAAgBzE,cAAc,CAACsE,iBAAiB;QAEtD,MAAMI,eAAeD,aAAa,CAAC,EAAE;QACrC,MAAME,oBAAoBnF,2BAA2BkF;QACrD,MAAME,oBACJ/C,YAAY0C,mCAAqB,6BAAC1C,kBAAce;QAElD,SAASiC,qBACPC,gBAA2B,EAC3BC,aAA8B;YAE9B,4CAA4C;YAC5C,OAAO;gBACLT;8BACA,6BAACpF;oBACC8F,mBAAmBV;oBACnBW,aAAahH,kBAAkBuG;oBAC/BnE,SAASgB,wBAAU,6BAACA,iBAAauB;oBACjCtB,eAAeA;oBACf,sKAAsK;oBACtK4D,YAAYC,QAAQ9D;oBACpBjB,OAAOe;oBACPC,aAAaA;oBACbjB,wBACE,6BAACS,8BACC,6BAACzB;oBAGL0B,gBAAgBA;oBAChBP,UAAUsE;oBACV9C,gBAAgBA;oBAChBsD,WAAWN;oBACXpE,QAAQqE;;aAEX;QACH;QAEA,yEAAyE;QACzE,gDAAgD;QAChD,wEAAwE;QACxE,IAAIA,gBAAgBnC;QACpB,IAAIyC,eAAe;QACnB,MAAMC,mBAAmBC,IAAAA,mEAA4B,EACnDZ,oBAAoBA,kBAAkBV,WAAW,GAAGS,cACpDjF;QAEF,IACE,CACEC,CAAAA,cACC2B,CAAAA,WAAW,CAACmE,IAAAA,oDAAyB,EAACf,cAAa,CAAC,GAEvD;YACA,6BAA6B;YAC7B,MAAM,EAAE5B,WAAW4C,cAAc,EAAE/E,QAAQgF,oBAAoB,EAAE,GAC/D,MAAM1H,oBAAoB;gBACxBC,mBAAmB,CAAC0H;oBAClB,OAAO1H,kBAAkB;2BAAIuG;2BAAuBmB;qBAAM;gBAC5D;gBACAzH,YAAYuG;gBACZrG,cAAcyF;gBACdvF,oBAAoBsD;gBACpBrD,aAAagC;gBACb/B,yBAAyBiC;gBACzBhC;gBACAC;gBACAC;YACF;YAEFoG,gBAAgBW;YAChBL,6BAAe,6BAACI;QAClB;QAEA,MAAML,YAAuB;YAC3BQ,SAASP;YACTvF,SAASwF;QACX;QAEA,OAAOT,qBAAqBO,WAAWL;IACzC;IAIJ,uFAAuF;IACvF,MAAMc,0BAA0B3B,iBAAiB4B,MAAM,CACrD,CAACC,MAAM,CAACzB,kBAAkB0B,KAAK;QAC7BD,IAAI,CAACzB,iBAAiB,GAAG0B;QACzB,OAAOD;IACT,GACA,CAAC;IAGH,wIAAwI;IACxI,IAAI,CAAClD,WAAW;QACd,OAAO;YACLA,WAAW,kBAAM,4DAAGgD,wBAAwBI,QAAQ;YACpDvF;QACF;IACF;IAEA,MAAMwF,oBAAoBC,IAAAA,kCAAiB,EAAC1E;IAE5C,oEAAoE;IACpE,iEAAiE;IACjE,IAAImD,oBAAoB,CAAC;IACzB,IACE/C,YACApD,cACA,2GAA2G;IAC3G,6DAA6D;IAC7D,CAACyF,iBAAiBhB,MAAM,EACxB;QACA0B,oBAAoB;YAClBqB,wBACE,0EACE,6BAACG;gBAAKC,MAAK;gBAASC,SAAQ;gBAC3BhD,QAAQC,GAAG,CAACC,QAAQ,KAAK,+BACxB,6BAAC4C;gBAAKC,MAAK;gBAAaC,SAAQ;gBAEjCxE,8BACD,6BAACD;QAGP;IACF;IAEA,MAAM0E,QAAQ;QACZ,GAAGV,uBAAuB;QAC1B,GAAGjB,iBAAiB;QACpB,8GAA8G;QAC9G,gEAAgE;QAChE,+GAA+G;QAC/G4B,QAAQ3C;QACR,iCAAiC;QACjC,GAAG,AAAC,CAAA;YACF,IAAIqC,qBAAqBpH,sBAAsByD,kBAAkB,EAAE;gBACjE,OAAO,CAAC;YACV;YAEA,IAAIf,QAAQ;gBACV,OAAO7B;YACT;QACF,CAAA,GAAI;IACN;IAEA,kEAAkE;IAClE,IAAI,CAACuG,mBAAmB;QACtBrD,YAAY,MAAMsB,QAAQsC,OAAO,GAAGC,IAAI,CAAC,IACvCC,IAAAA,kCAAgB,EAAC9D,WAAW0D;IAEhC;IAEA,OAAO;QACL1D,WAAW;YACT,qBACE,4DACGrB,SAAS9C,iBAAiB,MAE1B8C,UAAU0E,kCACT,6BAAC9G;gBACCwH,mBAAmBL;gBACnB1D,WAAWA;gBACXN,oBAAoBzD,sBAAsByD,kBAAkB;+BAG9D,6BAACM,WAAc0D,QAUhB;QAGP;QACA7F;IACF;AACF"}