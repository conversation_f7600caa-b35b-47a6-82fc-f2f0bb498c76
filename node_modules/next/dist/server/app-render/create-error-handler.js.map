{"version": 3, "sources": ["../../../src/server/app-render/create-error-handler.tsx"], "names": ["createErrorHandler", "_source", "dev", "isNextExport", "errorLogger", "capturedErrors", "allCapturedErrors", "err", "push", "digest", "DYNAMIC_ERROR_CODE", "isNotFoundError", "NEXT_DYNAMIC_NO_SSR_CODE", "isRedirectError", "formatServerError", "message", "includes", "span", "getTracer", "getActiveScopeSpan", "recordException", "setStatus", "code", "SpanStatusCode", "ERROR", "catch", "process", "env", "NODE_ENV", "logAppDirError", "require", "console", "error", "stringHash", "stack", "toString"], "mappings": ";;;;+<PERSON>eg<PERSON>;;;eAAAA;;;oCAfmB;mEACZ;mCACW;0BACF;0BACA;4BACS;wBACC;;;;;;AASnC,SAASA,mBAAmB,EACjC;;GAEC,GACDC,OAAO,EACPC,GAAG,EACHC,YAAY,EACZC,WAAW,EACXC,cAAc,EACdC,iBAAiB,EAQlB;IACC,OAAO,CAACC;YAuBFA;QAtBJ,IAAID,mBAAmBA,kBAAkBE,IAAI,CAACD;QAE9C,IACEA,OACCA,CAAAA,IAAIE,MAAM,KAAKC,sCAAkB,IAChCC,IAAAA,yBAAe,EAACJ,QAChBA,IAAIE,MAAM,KAAKG,oCAAwB,IACvCC,IAAAA,yBAAe,EAACN,IAAG,GACrB;YACA,OAAOA,IAAIE,MAAM;QACnB;QAEA,yEAAyE;QACzE,IAAIP,KAAK;YACPY,IAAAA,oCAAiB,EAACP;QACpB;QACA,kCAAkC;QAClC,8BAA8B;QAC9B,+CAA+C;QAC/C,IACE,CACEJ,CAAAA,iBACAI,wBAAAA,eAAAA,IAAKQ,OAAO,qBAAZR,aAAcS,QAAQ,CACpB,4FACF,GAEF;YACA,oDAAoD;YACpD,MAAMC,OAAOC,IAAAA,iBAAS,IAAGC,kBAAkB;YAC3C,IAAIF,MAAM;gBACRA,KAAKG,eAAe,CAACb;gBACrBU,KAAKI,SAAS,CAAC;oBACbC,MAAMC,sBAAc,CAACC,KAAK;oBAC1BT,SAASR,IAAIQ,OAAO;gBACtB;YACF;YAEA,IAAIX,aAAa;gBACfA,YAAYG,KAAKkB,KAAK,CAAC,KAAO;YAChC,OAAO;gBACL,kEAAkE;gBAClE,mCAAmC;gBACnC,mEAAmE;gBACnE,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;oBACzC,MAAM,EAAEC,cAAc,EAAE,GACtBC,QAAQ;oBACVD,eAAetB;gBACjB;gBACA,IAAImB,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;oBACzCG,QAAQC,KAAK,CAACzB;gBAChB;YACF;QACF;QAEAF,eAAeG,IAAI,CAACD;QACpB,+EAA+E;QAC/E,OAAO0B,IAAAA,mBAAU,EAAC1B,IAAIQ,OAAO,GAAGR,IAAI2B,KAAK,GAAI3B,CAAAA,IAAIE,MAAM,IAAI,EAAC,GAAI0B,QAAQ;IAC1E;AACF"}