{"version": 3, "sources": ["../../../src/server/app-render/use-flight-response.tsx"], "names": ["useFlightResponse", "isEdgeRuntime", "process", "env", "NEXT_RUNTIME", "INLINE_FLIGHT_PAYLOAD_BOOTSTRAP", "INLINE_FLIGHT_PAYLOAD_DATA", "INLINE_FLIGHT_PAYLOAD_FORM_STATE", "writable", "flightStream", "clientReferenceManifest", "flightResponseRef", "formState", "nonce", "current", "createFromReadableStream", "TURBOPACK", "require", "renderStream", "forwardStream", "tee", "res", "ssrManifest", "moduleLoading", "moduleMap", "edgeSSRModuleMapping", "ssrModuleMapping", "bootstrapped", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "writer", "getWriter", "startScriptTag", "JSON", "stringify", "textDecoder", "TextDecoder", "read", "then", "done", "value", "write", "encodeText", "htmlEscapeJsonString", "setTimeout", "close", "responsePartial", "decodeText", "scripts"], "mappings": ";;;;+<PERSON><PERSON><PERSON>;;;eAAAA;;;8BAbuB;4BACF;AAErC,MAAMC,gBAAgBC,QAAQC,GAAG,CAACC,YAAY,KAAK;AAEnD,MAAMC,kCAAkC;AACxC,MAAMC,6BAA6B;AACnC,MAAMC,mCAAmC;AAMlC,SAASP,kBACdQ,QAAoC,EACpCC,YAAwC,EACxCC,uBAAgD,EAChDC,iBAAoC,EACpCC,SAAqB,EACrBC,KAAc;IAEd,IAAIF,kBAAkBG,OAAO,KAAK,MAAM;QACtC,OAAOH,kBAAkBG,OAAO;IAClC;IACA,wGAAwG;IACxG,IAAIC;IACJ,uGAAuG;IACvG,IAAIb,QAAQC,GAAG,CAACa,SAAS,EAAE;QACzBD,2BACE,6DAA6D;QAC7DE,QAAQ,0CAA0CF,wBAAwB;IAC9E,OAAO;QACLA,2BACE,6DAA6D;QAC7DE,QAAQ,wCAAwCF,wBAAwB;IAC5E;IAEA,MAAM,CAACG,cAAcC,cAAc,GAAGV,aAAaW,GAAG;IACtD,MAAMC,MAAMN,yBAAyBG,cAAc;QACjDI,aAAa;YACXC,eAAeb,wBAAwBa,aAAa;YACpDC,WAAWvB,gBACPS,wBAAwBe,oBAAoB,GAC5Cf,wBAAwBgB,gBAAgB;QAC9C;QACAb;IACF;IACAF,kBAAkBG,OAAO,GAAGO;IAE5B,IAAIM,eAAe;IACnB,iDAAiD;IACjD,MAAMC,gBAAgBT,cAAcU,SAAS;IAC7C,MAAMC,SAAStB,SAASuB,SAAS;IACjC,MAAMC,iBAAiBnB,QACnB,CAAC,cAAc,EAAEoB,KAAKC,SAAS,CAACrB,OAAO,CAAC,CAAC,GACzC;IACJ,MAAMsB,cAAc,IAAIC;IAExB,SAASC;QACPT,cAAcS,IAAI,GAAGC,IAAI,CAAC,CAAC,EAAEC,IAAI,EAAEC,KAAK,EAAE;YACxC,IAAI,CAACb,cAAc;gBACjBA,eAAe;gBACfG,OAAOW,KAAK,CACVC,IAAAA,wBAAU,EACR,CAAC,EAAEV,eAAe,uCAAuC,EAAEW,IAAAA,gCAAoB,EAC7EV,KAAKC,SAAS,CAAC;oBAAC7B;iBAAgC,GAChD,qBAAqB,EAAEsC,IAAAA,gCAAoB,EAC3CV,KAAKC,SAAS,CAAC;oBAAC3B;oBAAkCK;iBAAU,GAC5D,UAAU,CAAC;YAGnB;YACA,IAAI2B,MAAM;gBACR,iIAAiI;gBACjI,iEAAiE;gBACjE,+IAA+I;gBAC/I,iDAAiD;gBACjDK,WAAW;oBACTjC,kBAAkBG,OAAO,GAAG;gBAC9B;gBACAgB,OAAOe,KAAK;YACd,OAAO;gBACL,MAAMC,kBAAkBC,IAAAA,wBAAU,EAACP,OAAOL;gBAC1C,MAAMa,UAAU,CAAC,EAAEhB,eAAe,mBAAmB,EAAEW,IAAAA,gCAAoB,EACzEV,KAAKC,SAAS,CAAC;oBAAC5B;oBAA4BwC;iBAAgB,GAC5D,UAAU,CAAC;gBAEbhB,OAAOW,KAAK,CAACC,IAAAA,wBAAU,EAACM;gBACxBX;YACF;QACF;IACF;IACAA;IAEA,OAAOhB;AACT"}