{"version": 3, "sources": ["../../src/server/image-optimizer.ts"], "names": ["getHash", "detectContentType", "ImageOptimizerCache", "ImageError", "getMaxAge", "optimizeImage", "imageOptimizer", "sendResponse", "getImageSize", "AVIF", "WEBP", "PNG", "JPEG", "GIF", "SVG", "ICO", "CACHE_VERSION", "ANIMATABLE_TYPES", "VECTOR_TYPES", "BLUR_IMG_SIZE", "BLUR_QUALITY", "sharp", "require", "process", "env", "NEXT_SHARP_PATH", "concurrency", "divisor", "NODE_ENV", "Math", "floor", "max", "cpus", "length", "e", "showSharpMissingWarning", "getSupportedMimeType", "options", "accept", "mimeType", "mediaType", "includes", "items", "hash", "createHash", "item", "update", "String", "digest", "replace", "writeToCacheDir", "dir", "extension", "maxAge", "expireAt", "buffer", "etag", "filename", "join", "promises", "rm", "recursive", "force", "catch", "mkdir", "writeFile", "every", "b", "i", "validateParams", "req", "query", "nextConfig", "isDev", "imageData", "images", "deviceSizes", "imageSizes", "domains", "minimumCacheTTL", "formats", "remotePatterns", "url", "w", "q", "href", "errorMessage", "Array", "isArray", "isAbsolute", "startsWith", "hrefParsed", "URL", "toString", "_error", "protocol", "hasMatch", "width", "parseInt", "isNaN", "sizes", "push", "isValidSize", "quality", "headers", "isStatic", "basePath", "get<PERSON><PERSON><PERSON><PERSON>", "constructor", "distDir", "cacheDir", "get", "cache<PERSON>ey", "files", "readdir", "now", "Date", "file", "maxAgeSt", "expireAtSt", "split", "readFile", "Number", "value", "kind", "revalidateAfter", "curRevalidate", "isStale", "_", "set", "revalidate", "Error", "err", "console", "error", "statusCode", "message", "parseCacheControl", "str", "map", "Map", "directive", "key", "trim", "toLowerCase", "age", "endsWith", "slice", "n", "contentType", "height", "nextConfigOutput", "optimizedBuffer", "transformer", "sequentialRead", "rotate", "resize", "undefined", "withoutEnlargement", "avif", "avifQuality", "chromaSubsampling", "warn", "yellow", "bold", "webp", "png", "jpeg", "<PERSON><PERSON><PERSON><PERSON>", "orientation", "getOrientation", "operations", "Orientation", "RIGHT_TOP", "type", "numRotations", "BOTTOM_RIGHT", "LEFT_BOTTOM", "processBuffer", "_req", "_res", "paramsResult", "handleRequest", "upstreamBuffer", "upstreamType", "upstreamRes", "fetch", "ok", "status", "<PERSON><PERSON><PERSON>", "from", "arrayBuffer", "mocked", "createRequestResponseMocks", "method", "socket", "res", "nodeUrl", "parse", "hasStreamed", "concat", "buffers", "<PERSON><PERSON><PERSON><PERSON>", "cacheControl", "dangerouslyAllowSVG", "vector", "animate", "isAnimated", "getExtension", "output", "getMetadata", "meta", "opts", "blur<PERSON>idth", "blurHeight", "blurDataURL", "unescape", "getImageBlurSvg", "getFileNameWithExtension", "urlWithoutQueryParams", "fileNameWithExtension", "pop", "fileName", "setResponseHeaders", "xCache", "imagesConfig", "<PERSON><PERSON><PERSON><PERSON>", "sendEtagResponse", "finished", "contentDisposition", "contentDispositionType", "contentSecurityPolicy", "getContentType", "result", "byteLength", "end", "metadata", "decodeBuffer", "imageSizeOf"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;IA+EgBA,OAAO;eAAPA;;IAiCAC,iBAAiB;eAAjBA;;IAqCHC,mBAAmB;eAAnBA;;IA6MAC,UAAU;eAAVA;;IA+BGC,SAAS;eAATA;;IAeMC,aAAa;eAAbA;;IAkHAC,cAAc;eAAdA;;IAgONC,YAAY;eAAZA;;IAgCMC,YAAY;eAAZA;;;wBAlwBK;oBACF;oBACJ;wBAEK;4BACG;2EACE;gCACa;kEACpB;mEACD;sBACF;4DAC4B;8BAEjB;oCAEP;6BAEkB;6BAWV;6BACY;;;;;;AAI7C,MAAMC,OAAO;AACb,MAAMC,OAAO;AACb,MAAMC,MAAM;AACZ,MAAMC,OAAO;AACb,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,gBAAgB;AACtB,MAAMC,mBAAmB;IAACP;IAAMC;IAAKE;CAAI;AACzC,MAAMK,eAAe;IAACJ;CAAI;AAC1B,MAAMK,gBAAgB,EAAE,mCAAmC;;AAC3D,MAAMC,eAAe,GAAG,mCAAmC;;AAE3D,IAAIC;AAEJ,IAAI;IACFA,QAAQC,QAAQC,QAAQC,GAAG,CAACC,eAAe,IAAI;IAC/C,IAAIJ,SAASA,MAAMK,WAAW,KAAK,GAAG;QACpC,2DAA2D;QAC3D,8DAA8D;QAC9D,0DAA0D;QAC1D,MAAMC,UAAUJ,QAAQC,GAAG,CAACI,QAAQ,KAAK,gBAAgB,IAAI;QAC7DP,MAAMK,WAAW,CAACG,KAAKC,KAAK,CAACD,KAAKE,GAAG,CAACC,IAAAA,QAAI,IAAGC,MAAM,GAAGN,SAAS;IACjE;AACF,EAAE,OAAOO,GAAG;AACV,iEAAiE;AACnE;AAEA,IAAIC,0BAA0BZ,QAAQC,GAAG,CAACI,QAAQ,KAAK;AAavD,SAASQ,qBAAqBC,OAAiB,EAAEC,SAAS,EAAE;IAC1D,MAAMC,WAAWC,IAAAA,iBAAS,EAACF,QAAQD;IACnC,OAAOC,OAAOG,QAAQ,CAACF,YAAYA,WAAW;AAChD;AAEO,SAASvC,QAAQ0C,KAAmC;IACzD,MAAMC,OAAOC,IAAAA,kBAAU,EAAC;IACxB,KAAK,IAAIC,QAAQH,MAAO;QACtB,IAAI,OAAOG,SAAS,UAAUF,KAAKG,MAAM,CAACC,OAAOF;aAC5C;YACHF,KAAKG,MAAM,CAACD;QACd;IACF;IACA,qDAAqD;IACrD,OAAOF,KAAKK,MAAM,CAAC,UAAUC,OAAO,CAAC,OAAO;AAC9C;AAEA,eAAeC,gBACbC,GAAW,EACXC,SAAiB,EACjBC,MAAc,EACdC,QAAgB,EAChBC,MAAc,EACdC,IAAY;IAEZ,MAAMC,WAAWC,IAAAA,UAAI,EAACP,KAAK,CAAC,EAAEE,OAAO,CAAC,EAAEC,SAAS,CAAC,EAAEE,KAAK,CAAC,EAAEJ,UAAU,CAAC;IAEvE,MAAMO,YAAQ,CAACC,EAAE,CAACT,KAAK;QAAEU,WAAW;QAAMC,OAAO;IAAK,GAAGC,KAAK,CAAC,KAAO;IAEtE,MAAMJ,YAAQ,CAACK,KAAK,CAACb,KAAK;QAAEU,WAAW;IAAK;IAC5C,MAAMF,YAAQ,CAACM,SAAS,CAACR,UAAUF;AACrC;AAOO,SAAStD,kBAAkBsD,MAAc;IAC9C,IAAI;QAAC;QAAM;QAAM;KAAK,CAACW,KAAK,CAAC,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAAI;QACvD,OAAOvD;IACT;IACA,IACE;QAAC;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK,CAACsD,KAAK,CACpD,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAE1B;QACA,OAAOxD;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;KAAK,CAACuD,KAAK,CAAC,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAAI;QAC7D,OAAOtD;IACT;IACA,IACE;QAAC;QAAM;QAAM;QAAM;QAAM;QAAG;QAAG;QAAG;QAAG;QAAM;QAAM;QAAM;KAAK,CAACqD,KAAK,CAChE,CAACC,GAAGC,IAAM,CAACD,KAAKZ,MAAM,CAACa,EAAE,KAAKD,IAEhC;QACA,OAAOzD;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;QAAM;KAAK,CAACwD,KAAK,CAAC,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAAI;QACnE,OAAOrD;IACT;IACA,IACE;QAAC;QAAG;QAAG;QAAG;QAAG;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK,CAACoD,KAAK,CAChE,CAACC,GAAGC,IAAM,CAACD,KAAKZ,MAAM,CAACa,EAAE,KAAKD,IAEhC;QACA,OAAO1D;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;KAAK,CAACyD,KAAK,CAAC,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAAI;QAC7D,OAAOpD;IACT;IACA,OAAO;AACT;AAEO,MAAMb;IAIX,OAAOmE,eACLC,GAAoB,EACpBC,KAAkC,EAClCC,UAA8B,EAC9BC,KAAc,EACgC;YASvBD;QARvB,MAAME,YAAYF,WAAWG,MAAM;QACnC,MAAM,EACJC,cAAc,EAAE,EAChBC,aAAa,EAAE,EACfC,UAAU,EAAE,EACZC,kBAAkB,EAAE,EACpBC,UAAU;YAAC;SAAa,EACzB,GAAGN;QACJ,MAAMO,iBAAiBT,EAAAA,qBAAAA,WAAWG,MAAM,qBAAjBH,mBAAmBS,cAAc,KAAI,EAAE;QAC9D,MAAM,EAAEC,GAAG,EAAEC,CAAC,EAAEC,CAAC,EAAE,GAAGb;QACtB,IAAIc;QAEJ,IAAI,CAACH,KAAK;YACR,OAAO;gBAAEI,cAAc;YAA8B;QACvD,OAAO,IAAIC,MAAMC,OAAO,CAACN,MAAM;YAC7B,OAAO;gBAAEI,cAAc;YAAqC;QAC9D;QAEA,IAAIG;QAEJ,IAAIP,IAAIQ,UAAU,CAAC,MAAM;YACvBL,OAAOH;YACPO,aAAa;QACf,OAAO;YACL,IAAIE;YAEJ,IAAI;gBACFA,aAAa,IAAIC,IAAIV;gBACrBG,OAAOM,WAAWE,QAAQ;gBAC1BJ,aAAa;YACf,EAAE,OAAOK,QAAQ;gBACf,OAAO;oBAAER,cAAc;gBAA6B;YACtD;YAEA,IAAI,CAAC;gBAAC;gBAAS;aAAS,CAAC7C,QAAQ,CAACkD,WAAWI,QAAQ,GAAG;gBACtD,OAAO;oBAAET,cAAc;gBAA6B;YACtD;YAEA,IAAI,CAACU,IAAAA,4BAAQ,EAAClB,SAASG,gBAAgBU,aAAa;gBAClD,OAAO;oBAAEL,cAAc;gBAAiC;YAC1D;QACF;QAEA,IAAI,CAACH,GAAG;YACN,OAAO;gBAAEG,cAAc;YAAoC;QAC7D,OAAO,IAAIC,MAAMC,OAAO,CAACL,IAAI;YAC3B,OAAO;gBAAEG,cAAc;YAA2C;QACpE;QAEA,IAAI,CAACF,GAAG;YACN,OAAO;gBAAEE,cAAc;YAAsC;QAC/D,OAAO,IAAIC,MAAMC,OAAO,CAACJ,IAAI;YAC3B,OAAO;gBAAEE,cAAc;YAA6C;QACtE;QAEA,MAAMW,QAAQC,SAASf,GAAG;QAE1B,IAAIc,SAAS,KAAKE,MAAMF,QAAQ;YAC9B,OAAO;gBACLX,cAAc;YAChB;QACF;QAEA,MAAMc,QAAQ;eAAKxB,eAAe,EAAE;eAAOC,cAAc,EAAE;SAAE;QAE7D,IAAIJ,OAAO;YACT2B,MAAMC,IAAI,CAAClF;QACb;QAEA,MAAMmF,cACJF,MAAM3D,QAAQ,CAACwD,UAAWxB,SAASwB,SAAS9E;QAE9C,IAAI,CAACmF,aAAa;YAChB,OAAO;gBACLhB,cAAc,CAAC,yBAAyB,EAAEW,MAAM,eAAe,CAAC;YAClE;QACF;QAEA,MAAMM,UAAUL,SAASd;QAEzB,IAAIe,MAAMI,YAAYA,UAAU,KAAKA,UAAU,KAAK;YAClD,OAAO;gBACLjB,cACE;YACJ;QACF;QAEA,MAAM/C,WAAWH,qBAAqB4C,WAAW,EAAE,EAAEV,IAAIkC,OAAO,CAAC,SAAS;QAE1E,MAAMC,WAAWvB,IAAIQ,UAAU,CAC7B,CAAC,EAAElB,WAAWkC,QAAQ,IAAI,GAAG,mBAAmB,CAAC;QAGnD,OAAO;YACLrB;YACAe;YACAX;YACAgB;YACAR;YACAM;YACAhE;YACAwC;QACF;IACF;IAEA,OAAO4B,YAAY,EACjBtB,IAAI,EACJY,KAAK,EACLM,OAAO,EACPhE,QAAQ,EAMT,EAAU;QACT,OAAOvC,QAAQ;YAACgB;YAAeqE;YAAMY;YAAOM;YAAShE;SAAS;IAChE;IAEAqE,YAAY,EACVC,OAAO,EACPrC,UAAU,EAIX,CAAE;QACD,IAAI,CAACsC,QAAQ,GAAGpD,IAAAA,UAAI,EAACmD,SAAS,SAAS;QACvC,IAAI,CAACrC,UAAU,GAAGA;IACpB;IAEA,MAAMuC,IAAIC,QAAgB,EAAyC;QACjE,IAAI;YACF,MAAMF,WAAWpD,IAAAA,UAAI,EAAC,IAAI,CAACoD,QAAQ,EAAEE;YACrC,MAAMC,QAAQ,MAAMtD,YAAQ,CAACuD,OAAO,CAACJ;YACrC,MAAMK,MAAMC,KAAKD,GAAG;YAEpB,KAAK,MAAME,QAAQJ,MAAO;gBACxB,MAAM,CAACK,UAAUC,YAAY/D,MAAMJ,UAAU,GAAGiE,KAAKG,KAAK,CAAC;gBAC3D,MAAMjE,SAAS,MAAMI,YAAQ,CAAC8D,QAAQ,CAAC/D,IAAAA,UAAI,EAACoD,UAAUO;gBACtD,MAAM/D,WAAWoE,OAAOH;gBACxB,MAAMlE,SAASqE,OAAOJ;gBAEtB,OAAO;oBACLK,OAAO;wBACLC,MAAM;wBACNpE;wBACAD;wBACAH;oBACF;oBACAyE,iBACEhG,KAAKE,GAAG,CAACsB,QAAQ,IAAI,CAACmB,UAAU,CAACG,MAAM,CAACI,eAAe,IAAI,OAC3DqC,KAAKD,GAAG;oBACVW,eAAezE;oBACf0E,SAASZ,MAAM7D;gBACjB;YACF;QACF,EAAE,OAAO0E,GAAG;QACV,qDAAqD;QACvD;QACA,OAAO;IACT;IACA,MAAMC,IACJjB,QAAgB,EAChBW,KAAmC,EACnC,EACEO,UAAU,EAGX,EACD;QACA,IAAIP,CAAAA,yBAAAA,MAAOC,IAAI,MAAK,SAAS;YAC3B,MAAM,IAAIO,MAAM;QAClB;QAEA,IAAI,OAAOD,eAAe,UAAU;YAClC,MAAM,IAAIC,MAAM;QAClB;QACA,MAAM7E,WACJzB,KAAKE,GAAG,CAACmG,YAAY,IAAI,CAAC1D,UAAU,CAACG,MAAM,CAACI,eAAe,IAAI,OAC/DqC,KAAKD,GAAG;QAEV,IAAI;YACF,MAAMjE,gBACJQ,IAAAA,UAAI,EAAC,IAAI,CAACoD,QAAQ,EAAEE,WACpBW,MAAMvE,SAAS,EACf8E,YACA5E,UACAqE,MAAMpE,MAAM,EACZoE,MAAMnE,IAAI;QAEd,EAAE,OAAO4E,KAAK;YACZC,QAAQC,KAAK,CAAC,CAAC,+BAA+B,EAAEtB,SAAS,CAAC,EAAEoB;QAC9D;IACF;AACF;AACO,MAAMjI,mBAAmBgI;IAG9BvB,YAAY2B,UAAkB,EAAEC,OAAe,CAAE;QAC/C,KAAK,CAACA;QAEN,uCAAuC;QACvC,IAAID,cAAc,KAAK;YACrB,IAAI,CAACA,UAAU,GAAGA;QACpB,OAAO;YACL,IAAI,CAACA,UAAU,GAAG;QACpB;IACF;AACF;AAEA,SAASE,kBAAkBC,GAAkB;IAC3C,MAAMC,MAAM,IAAIC;IAChB,IAAI,CAACF,KAAK;QACR,OAAOC;IACT;IACA,KAAK,IAAIE,aAAaH,IAAIlB,KAAK,CAAC,KAAM;QACpC,IAAI,CAACsB,KAAKnB,MAAM,GAAGkB,UAAUE,IAAI,GAAGvB,KAAK,CAAC;QAC1CsB,MAAMA,IAAIE,WAAW;QACrB,IAAIrB,OAAO;YACTA,QAAQA,MAAMqB,WAAW;QAC3B;QACAL,IAAIV,GAAG,CAACa,KAAKnB;IACf;IACA,OAAOgB;AACT;AAEO,SAASvI,UAAUsI,GAAkB;IAC1C,MAAMC,MAAMF,kBAAkBC;IAC9B,IAAIC,KAAK;QACP,IAAIM,MAAMN,IAAI5B,GAAG,CAAC,eAAe4B,IAAI5B,GAAG,CAAC,cAAc;QACvD,IAAIkC,IAAIvD,UAAU,CAAC,QAAQuD,IAAIC,QAAQ,CAAC,MAAM;YAC5CD,MAAMA,IAAIE,KAAK,CAAC,GAAG,CAAC;QACtB;QACA,MAAMC,IAAIlD,SAAS+C,KAAK;QACxB,IAAI,CAAC9C,MAAMiD,IAAI;YACb,OAAOA;QACT;IACF;IACA,OAAO;AACT;AAEO,eAAe/I,cAAc,EAClCkD,MAAM,EACN8F,WAAW,EACX9C,OAAO,EACPN,KAAK,EACLqD,MAAM,EACNC,gBAAgB,EAQjB;IACC,IAAIC,kBAAkBjG;IACtB,IAAIlC,OAAO;QACT,mCAAmC;QACnC,MAAMoI,cAAcpI,MAAMkC,QAAQ;YAChCmG,gBAAgB;QAClB;QAEAD,YAAYE,MAAM;QAElB,IAAIL,QAAQ;YACVG,YAAYG,MAAM,CAAC3D,OAAOqD;QAC5B,OAAO;YACLG,YAAYG,MAAM,CAAC3D,OAAO4D,WAAW;gBACnCC,oBAAoB;YACtB;QACF;QAEA,IAAIT,gBAAgB5I,MAAM;YACxB,IAAIgJ,YAAYM,IAAI,EAAE;gBACpB,MAAMC,cAAczD,UAAU;gBAC9BkD,YAAYM,IAAI,CAAC;oBACfxD,SAAS1E,KAAKE,GAAG,CAACiI,aAAa;oBAC/BC,mBAAmB;gBACrB;YACF,OAAO;gBACL5B,QAAQ6B,IAAI,CACVC,IAAAA,kBAAM,EAACC,IAAAA,gBAAI,EAAC,gBACV,CAAC,wIAAwI,CAAC,GAC1I;gBAEJX,YAAYY,IAAI,CAAC;oBAAE9D;gBAAQ;YAC7B;QACF,OAAO,IAAI8C,gBAAgB3I,MAAM;YAC/B+I,YAAYY,IAAI,CAAC;gBAAE9D;YAAQ;QAC7B,OAAO,IAAI8C,gBAAgB1I,KAAK;YAC9B8I,YAAYa,GAAG,CAAC;gBAAE/D;YAAQ;QAC5B,OAAO,IAAI8C,gBAAgBzI,MAAM;YAC/B6I,YAAYc,IAAI,CAAC;gBAAEhE;YAAQ;QAC7B;QAEAiD,kBAAkB,MAAMC,YAAYe,QAAQ;IAC5C,iCAAiC;IACnC,OAAO;QACL,IAAIrI,2BAA2BoH,qBAAqB,cAAc;YAChElB,QAAQC,KAAK,CACX,CAAC,0LAA0L,CAAC;YAE9L,MAAM,IAAInI,WAAW,KAAK;QAC5B;QACA,wCAAwC;QACxC,IAAIgC,yBAAyB;YAC3BkG,QAAQ6B,IAAI,CACVC,IAAAA,kBAAM,EAACC,IAAAA,gBAAI,EAAC,gBACV,CAAC,wLAAwL,CAAC,GAC1L;YAEJjI,0BAA0B;QAC5B;QAEA,qCAAqC;QACrC,MAAMsI,cAAc,MAAMC,IAAAA,8BAAc,EAACnH;QAEzC,MAAMoH,aAA0B,EAAE;QAElC,IAAIF,gBAAgBG,2BAAW,CAACC,SAAS,EAAE;YACzCF,WAAWtE,IAAI,CAAC;gBAAEyE,MAAM;gBAAUC,cAAc;YAAE;QACpD,OAAO,IAAIN,gBAAgBG,2BAAW,CAACI,YAAY,EAAE;YACnDL,WAAWtE,IAAI,CAAC;gBAAEyE,MAAM;gBAAUC,cAAc;YAAE;QACpD,OAAO,IAAIN,gBAAgBG,2BAAW,CAACK,WAAW,EAAE;YAClDN,WAAWtE,IAAI,CAAC;gBAAEyE,MAAM;gBAAUC,cAAc;YAAE;QACpD,OAAO;QACL,kCAAkC;QAClC,6DAA6D;QAC7D,+BAA+B;QACjC;QAEA,IAAIzB,QAAQ;YACVqB,WAAWtE,IAAI,CAAC;gBAAEyE,MAAM;gBAAU7E;gBAAOqD;YAAO;QAClD,OAAO;YACLqB,WAAWtE,IAAI,CAAC;gBAAEyE,MAAM;gBAAU7E;YAAM;QAC1C;QAEA,MAAM,EAAEiF,aAAa,EAAE,GACrB5J,QAAQ;QAEV,IAAI+H,gBAAgB5I,MAAM;YACxB+I,kBAAkB,MAAM0B,cAAc3H,QAAQoH,YAAY,QAAQpE;QACpE,OAAO,IAAI8C,gBAAgB3I,MAAM;YAC/B8I,kBAAkB,MAAM0B,cAAc3H,QAAQoH,YAAY,QAAQpE;QACpE,OAAO,IAAI8C,gBAAgB1I,KAAK;YAC9B6I,kBAAkB,MAAM0B,cAAc3H,QAAQoH,YAAY,OAAOpE;QACnE,OAAO,IAAI8C,gBAAgBzI,MAAM;YAC/B4I,kBAAkB,MAAM0B,cAAc3H,QAAQoH,YAAY,QAAQpE;QACpE;IACF;IAEA,OAAOiD;AACT;AAEO,eAAelJ,eACpB6K,IAAqB,EACrBC,IAAoB,EACpBC,YAA+B,EAC/B7G,UAA8B,EAC9BC,KAA0B,EAC1B6G,aAIkB;IAElB,IAAIC;IACJ,IAAIC;IACJ,IAAInI;IACJ,MAAM,EAAEoC,UAAU,EAAEJ,IAAI,EAAEY,KAAK,EAAE1D,QAAQ,EAAEgE,OAAO,EAAE,GAAG8E;IAEvD,IAAI5F,YAAY;QACd,MAAMgG,cAAc,MAAMC,MAAMrG;QAEhC,IAAI,CAACoG,YAAYE,EAAE,EAAE;YACnBtD,QAAQC,KAAK,CACX,sCACAjD,MACAoG,YAAYG,MAAM;YAEpB,MAAM,IAAIzL,WACRsL,YAAYG,MAAM,EAClB;QAEJ;QAEAL,iBAAiBM,OAAOC,IAAI,CAAC,MAAML,YAAYM,WAAW;QAC1DP,eACEvL,kBAAkBsL,mBAClBE,YAAYjF,OAAO,CAACO,GAAG,CAAC;QAC1B1D,SAASjD,UAAUqL,YAAYjF,OAAO,CAACO,GAAG,CAAC;IAC7C,OAAO;QACL,IAAI;YACF,MAAMiF,SAASC,IAAAA,uCAA0B,EAAC;gBACxC/G,KAAKG;gBACL6G,QAAQf,KAAKe,MAAM,IAAI;gBACvB1F,SAAS2E,KAAK3E,OAAO;gBACrB2F,QAAQhB,KAAKgB,MAAM;YACrB;YAEA,MAAMb,cAAcU,OAAO1H,GAAG,EAAE0H,OAAOI,GAAG,EAAEC,YAAO,CAACC,KAAK,CAACjH,MAAM;YAChE,MAAM2G,OAAOI,GAAG,CAACG,WAAW;YAE5B,IAAI,CAACP,OAAOI,GAAG,CAAC7D,UAAU,EAAE;gBAC1BF,QAAQC,KAAK,CAAC,6BAA6BjD,MAAM2G,OAAOI,GAAG,CAAC7D,UAAU;gBACtE,MAAM,IAAIpI,WACR6L,OAAOI,GAAG,CAAC7D,UAAU,EACrB;YAEJ;YAEAgD,iBAAiBM,OAAOW,MAAM,CAACR,OAAOI,GAAG,CAACK,OAAO;YACjDjB,eACEvL,kBAAkBsL,mBAClBS,OAAOI,GAAG,CAACM,SAAS,CAAC;YACvB,MAAMC,eAAeX,OAAOI,GAAG,CAACM,SAAS,CAAC;YAC1CrJ,SAASsJ,eAAevM,UAAUuM,gBAAgB;QACpD,EAAE,OAAOvE,KAAK;YACZC,QAAQC,KAAK,CAAC,sCAAsCjD,MAAM+C;YAC1D,MAAM,IAAIjI,WACR,KACA;QAEJ;IACF;IAEA,IAAIqL,cAAc;QAChBA,eAAeA,aAAaxC,WAAW,GAAGD,IAAI;QAE9C,IACEyC,aAAa9F,UAAU,CAAC,gBACxB,CAAClB,WAAWG,MAAM,CAACiI,mBAAmB,EACtC;YACAvE,QAAQC,KAAK,CACX,CAAC,wBAAwB,EAAEjD,KAAK,YAAY,EAAEmG,aAAa,qCAAqC,CAAC;YAEnG,MAAM,IAAIrL,WACR,KACA;QAEJ;QACA,MAAM0M,SAAS3L,aAAauB,QAAQ,CAAC+I;QACrC,MAAMsB,UACJ7L,iBAAiBwB,QAAQ,CAAC+I,iBAAiBuB,IAAAA,mBAAU,EAACxB;QAExD,IAAIsB,UAAUC,SAAS;YACrB,OAAO;gBAAEvJ,QAAQgI;gBAAgBlC,aAAamC;gBAAcnI;YAAO;QACrE;QACA,IAAI,CAACmI,aAAa9F,UAAU,CAAC,aAAa8F,aAAa/I,QAAQ,CAAC,MAAM;YACpE4F,QAAQC,KAAK,CACX,kDACAjD,MACA,YACAmG;YAEF,MAAM,IAAIrL,WAAW,KAAK;QAC5B;IACF;IAEA,IAAIkJ;IAEJ,IAAI9G,UAAU;QACZ8G,cAAc9G;IAChB,OAAO,IACLiJ,CAAAA,gCAAAA,aAAc9F,UAAU,CAAC,cACzBsH,IAAAA,yBAAY,EAACxB,iBACbA,iBAAiB9K,QACjB8K,iBAAiB/K,MACjB;QACA4I,cAAcmC;IAChB,OAAO;QACLnC,cAAczI;IAChB;IACA,IAAI;QACF,IAAI4I,kBAAkB,MAAMnJ,cAAc;YACxCkD,QAAQgI;YACRlC;YACA9C;YACAN;YACAsD,kBAAkB/E,WAAWyI,MAAM;QACrC;QACA,IAAIzD,iBAAiB;YACnB,IAAI/E,SAASwB,SAAS9E,iBAAiBoF,YAAYnF,cAAc;gBAC/D,MAAM,EAAE8L,WAAW,EAAE,GACnB5L,QAAQ;gBACV,8EAA8E;gBAC9E,gFAAgF;gBAChF,qFAAqF;gBACrF,MAAM6L,OAAO,MAAMD,YAAY1D;gBAC/B,MAAM4D,OAAO;oBACXC,WAAWF,KAAKlH,KAAK;oBACrBqH,YAAYH,KAAK7D,MAAM;oBACvBiE,aAAa,CAAC,KAAK,EAAElE,YAAY,QAAQ,EAAEG,gBAAgB3D,QAAQ,CACjE,UACA,CAAC;gBACL;gBACA2D,kBAAkBqC,OAAOC,IAAI,CAAC0B,SAASC,IAAAA,6BAAe,EAACL;gBACvD/D,cAAc;YAChB;YACA,OAAO;gBACL9F,QAAQiG;gBACRH;gBACAhG,QAAQxB,KAAKE,GAAG,CAACsB,QAAQmB,WAAWG,MAAM,CAACI,eAAe;YAC5D;QACF,OAAO;YACL,MAAM,IAAI5E,WAAW,KAAK;QAC5B;IACF,EAAE,OAAOmI,OAAO;QACd,IAAIiD,kBAAkBC,cAAc;YAClC,yDAAyD;YACzD,OAAO;gBACLjI,QAAQgI;gBACRlC,aAAamC;gBACbnI,QAAQmB,WAAWG,MAAM,CAACI,eAAe;YAC3C;QACF,OAAO;YACL,MAAM,IAAI5E,WACR,KACA;QAEJ;IACF;AACF;AAEA,SAASuN,yBACPxI,GAAW,EACXmE,WAA0B;IAE1B,MAAM,CAACsE,sBAAsB,GAAGzI,IAAIsC,KAAK,CAAC;IAC1C,MAAMoG,wBAAwBD,sBAAsBnG,KAAK,CAAC,KAAKqG,GAAG;IAClE,IAAI,CAACxE,eAAe,CAACuE,uBAAuB;QAC1C,OAAO;IACT;IAEA,MAAM,CAACE,SAAS,GAAGF,sBAAsBpG,KAAK,CAAC;IAC/C,MAAMpE,YAAY4J,IAAAA,yBAAY,EAAC3D;IAC/B,OAAO,CAAC,EAAEyE,SAAS,CAAC,EAAE1K,UAAU,CAAC;AACnC;AAEA,SAAS2K,mBACPzJ,GAAoB,EACpB8H,GAAmB,EACnBlH,GAAW,EACX1B,IAAY,EACZ6F,WAA0B,EAC1B5C,QAAiB,EACjBuH,MAAoB,EACpBC,YAAiC,EACjC5K,MAAc,EACdoB,KAAc;IAEd2H,IAAI8B,SAAS,CAAC,QAAQ;IACtB9B,IAAI8B,SAAS,CACX,iBACAzH,WACI,yCACA,CAAC,gBAAgB,EAAEhC,QAAQ,IAAIpB,OAAO,iBAAiB,CAAC;IAE9D,IAAI8K,IAAAA,6BAAgB,EAAC7J,KAAK8H,KAAK5I,OAAO;QACpC,6CAA6C;QAC7C,OAAO;YAAE4K,UAAU;QAAK;IAC1B;IACA,IAAI/E,aAAa;QACf+C,IAAI8B,SAAS,CAAC,gBAAgB7E;IAChC;IAEA,MAAMyE,WAAWJ,yBAAyBxI,KAAKmE;IAC/C+C,IAAI8B,SAAS,CACX,uBACAG,IAAAA,2BAAkB,EAACP,UAAU;QAAEhD,MAAMmD,aAAaK,sBAAsB;IAAC;IAG3ElC,IAAI8B,SAAS,CAAC,2BAA2BD,aAAaM,qBAAqB;IAC3EnC,IAAI8B,SAAS,CAAC,kBAAkBF;IAEhC,OAAO;QAAEI,UAAU;IAAM;AAC3B;AAEO,SAAS7N,aACd+D,GAAoB,EACpB8H,GAAmB,EACnBlH,GAAW,EACX9B,SAAiB,EACjBG,MAAc,EACdkD,QAAiB,EACjBuH,MAAoB,EACpBC,YAAiC,EACjC5K,MAAc,EACdoB,KAAc;IAEd,MAAM4E,cAAcmF,IAAAA,2BAAc,EAACpL;IACnC,MAAMI,OAAOxD,QAAQ;QAACuD;KAAO;IAC7B,MAAMkL,SAASV,mBACbzJ,KACA8H,KACAlH,KACA1B,MACA6F,aACA5C,UACAuH,QACAC,cACA5K,QACAoB;IAEF,IAAI,CAACgK,OAAOL,QAAQ,EAAE;QACpBhC,IAAI8B,SAAS,CAAC,kBAAkBrC,OAAO6C,UAAU,CAACnL;QAClD6I,IAAIuC,GAAG,CAACpL;IACV;AACF;AAEO,eAAe/C,aACpB+C,MAAc,EACd,8BAA8B;AAC9BH,SAA2C;IAK3C,qDAAqD;IACrD,0DAA0D;IAC1D,IAAIA,cAAc,QAAQ;QACxB,IAAI/B,OAAO;YACT,MAAMoI,cAAcpI,MAAMkC;YAC1B,MAAM,EAAE0C,KAAK,EAAEqD,MAAM,EAAE,GAAG,MAAMG,YAAYmF,QAAQ;YACpD,OAAO;gBAAE3I;gBAAOqD;YAAO;QACzB,OAAO;YACL,MAAM,EAAEuF,YAAY,EAAE,GACpBvN,QAAQ;YACV,MAAM,EAAE2E,KAAK,EAAEqD,MAAM,EAAE,GAAG,MAAMuF,aAAatL;YAC7C,OAAO;gBAAE0C;gBAAOqD;YAAO;QACzB;IACF;IAEA,MAAM,EAAErD,KAAK,EAAEqD,MAAM,EAAE,GAAGwF,IAAAA,kBAAW,EAACvL;IACtC,OAAO;QAAE0C;QAAOqD;IAAO;AACzB"}