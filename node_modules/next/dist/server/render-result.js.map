{"version": 3, "sources": ["../../src/server/render-result.ts"], "names": ["RenderResult", "fromStatic", "value", "constructor", "response", "contentType", "waitUntil", "metadata", "extendMetadata", "Object", "assign", "isNull", "isDynamic", "toUnchunkedString", "Error", "pipe", "res", "pipeReadable"], "mappings": ";;;;+BAsBA;;;eAAqBA;;;8BAlBQ;AAkBd,MAAMA;IAqBnB;;;;;GAKC,GACD,OAAcC,WAAWC,KAAa,EAAgB;QACpD,OAAO,IAAIF,aAAaE;IAC1B;IAIAC,YACEC,QAA8B,EAC9B,EACEC,WAAW,EACXC,SAAS,EACT,GAAGC,UAGmB,GAAG,CAAC,CAAC,CAC7B;QACA,IAAI,CAACH,QAAQ,GAAGA;QAChB,IAAI,CAACC,WAAW,GAAGA;QACnB,IAAI,CAACE,QAAQ,GAAGA;QAChB,IAAI,CAACD,SAAS,GAAGA;IACnB;IAEOE,eAAeD,QAA8B,EAAE;QACpDE,OAAOC,MAAM,CAAC,IAAI,CAACH,QAAQ,EAAEA;IAC/B;IAEA;;;GAGC,GACD,IAAWI,SAAkB;QAC3B,OAAO,IAAI,CAACP,QAAQ,KAAK;IAC3B;IAEA;;;GAGC,GACD,IAAWQ,YAAqB;QAC9B,OAAO,OAAO,IAAI,CAACR,QAAQ,KAAK;IAClC;IAEA;;;;;GAKC,GACD,AAAOS,oBAA4B;QACjC,IAAI,OAAO,IAAI,CAACT,QAAQ,KAAK,UAAU;YACrC,MAAM,IAAIU,MACR;QAEJ;QAEA,OAAO,IAAI,CAACV,QAAQ;IACtB;IAEA,MAAaW,KAAKC,GAA2B,EAAiB;QAC5D,IAAI,IAAI,CAACZ,QAAQ,KAAK,MAAM;YAC1B,MAAM,IAAIU,MAAM;QAClB;QACA,IAAI,OAAO,IAAI,CAACV,QAAQ,KAAK,UAAU;YACrC,MAAM,IAAIU,MACR;QAEJ;QAEA,OAAO,MAAMG,IAAAA,0BAAY,EAAC,IAAI,CAACb,QAAQ,EAAEY,KAAK,IAAI,CAACV,SAAS;IAC9D;AACF"}