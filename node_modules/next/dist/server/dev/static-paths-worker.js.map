{"version": 3, "sources": ["../../../src/server/dev/static-paths-worker.ts"], "names": ["loadStaticPaths", "AppRouteRouteModule", "require", "distDir", "pathname", "config", "httpAgentOptions", "locales", "defaultLocale", "isAppPath", "page", "isrFlushToDisk", "fetchCacheKeyPrefix", "maxMemoryCacheSize", "requestHeaders", "incremental<PERSON>ache<PERSON>andlerPath", "setConfig", "setHttpClientAndAgentOptions", "components", "loadComponents", "getStaticPaths", "Error", "routeModule", "generateParams", "is", "revalidate", "userland", "dynamic", "dynamicParams", "generateStaticParams", "segmentPath", "collectGenerateParams", "ComponentMod", "tree", "buildAppStaticPaths", "configFileName", "serverHooks", "staticGenerationAsyncStorage", "buildStaticPaths"], "mappings": ";;;;+BA2BsBA;;;eAAAA;;;QAzBf;QACA;QACA;QACA;uBAMA;gCAEwB;mCACc;4EAEhB;sDACgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE7C,MAAM,EAAEC,mBAAmB,EAAE,GAC3BC,QAAQ;AAOH,eAAeF,gBAAgB,EACpCG,OAAO,EACPC,QAAQ,EACRC,MAAM,EACNC,gBAAgB,EAChBC,OAAO,EACPC,aAAa,EACbC,SAAS,EACTC,IAAI,EACJC,cAAc,EACdC,mBAAmB,EACnBC,kBAAkB,EAClBC,cAAc,EACdC,2BAA2B,EAe5B;IAKC,oCAAoC;IACpCb,QAAQ,4CAA4Cc,SAAS,CAACX;IAC9DY,IAAAA,+CAA4B,EAAC;QAC3BX;IACF;IAEA,MAAMY,aAAa,MAAMC,IAAAA,8BAAc,EAAC;QACtChB;QACA,qDAAqD;QACrDO,MAAMA,QAAQN;QACdK;IACF;IAEA,IAAI,CAACS,WAAWE,cAAc,IAAI,CAACX,WAAW;QAC5C,yDAAyD;QACzD,mDAAmD;QACnD,MAAM,IAAIY,MACR,CAAC,uDAAuD,EAAEjB,SAAS,CAAC;IAExE;IAEA,IAAIK,WAAW;QACb,MAAM,EAAEa,WAAW,EAAE,GAAGJ;QACxB,MAAMK,iBACJD,eAAerB,oBAAoBuB,EAAE,CAACF,eAClC;YACE;gBACEjB,QAAQ;oBACNoB,YAAYH,YAAYI,QAAQ,CAACD,UAAU;oBAC3CE,SAASL,YAAYI,QAAQ,CAACC,OAAO;oBACrCC,eAAeN,YAAYI,QAAQ,CAACE,aAAa;gBACnD;gBACAC,sBAAsBP,YAAYI,QAAQ,CAACG,oBAAoB;gBAC/DC,aAAa1B;YACf;SACD,GACD,MAAM2B,IAAAA,4BAAqB,EAACb,WAAWc,YAAY,CAACC,IAAI;QAE9D,OAAO,MAAMC,IAAAA,0BAAmB,EAAC;YAC/BxB,MAAMN;YACNmB;YACAY,gBAAgB9B,OAAO8B,cAAc;YACrChC;YACAW;YACAC;YACAqB,aAAAA;YACAC,8BAAAA,kEAA4B;YAC5B1B;YACAC;YACAC;QACF;IACF;IAEA,OAAO,MAAMyB,IAAAA,uBAAgB,EAAC;QAC5B5B,MAAMN;QACNgB,gBAAgBF,WAAWE,cAAc;QACzCe,gBAAgB9B,OAAO8B,cAAc;QACrC5B;QACAC;IACF;AACF"}