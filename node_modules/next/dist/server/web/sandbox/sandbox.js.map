{"version": 3, "sources": ["../../../../src/server/web/sandbox/sandbox.ts"], "names": ["ErrorSource", "getRuntimeContext", "run", "Symbol", "FORBIDDEN_HEADERS", "withTaggedErrors", "fn", "params", "then", "result", "waitUntil", "catch", "error", "getServerError", "runtime", "evaluateInContext", "getModuleContext", "moduleName", "name", "onWarning", "useCache", "edgeFunctionEntry", "distDir", "incrementalCache", "context", "globalThis", "__incrementalCache", "<PERSON><PERSON><PERSON><PERSON>", "paths", "runWithTaggedErrors", "subreq", "request", "headers", "subrequests", "split", "includes", "Promise", "resolve", "response", "Response", "edgeFunction", "_ENTRIES", "default", "cloned", "method", "body", "cloneBodyStream", "undefined", "KUint8Array", "evaluate", "urlInstance", "URL", "url", "searchParams", "delete", "NEXT_RSC_UNION_QUERY", "toString", "requestToBodyStream", "headerName", "finalize"], "mappings": ";;;;;;;;;;;;;;;;IAQaA,WAAW;eAAXA;;IAuCSC,iBAAiB;eAAjBA;;IA2BTC,GAAG;eAAHA;;;4BAvEkB;yBACE;6BACG;kCACC;AAE9B,MAAMF,cAAcG,OAAO;AAElC,MAAMC,oBAAoB;IACxB;IACA;IACA;CACD;AAaD;;;CAGC,GACD,SAASC,iBAAiBC,EAAY;IACpC,OAAO,CAACC,SACND,GAAGC,QACAC,IAAI,CAAC,CAACC;gBAEMA;mBAFM;gBACjB,GAAGA,MAAM;gBACTC,SAAS,EAAED,2BAAAA,oBAAAA,OAAQC,SAAS,qBAAjBD,kBAAmBE,KAAK,CAAC,CAACC;oBACnC,mGAAmG;oBACnG,MAAMC,IAAAA,0BAAc,EAACD,OAAO;gBAC9B;YACF;WACCD,KAAK,CAAC,CAACC;YACN,+CAA+C;YAC/C,MAAMC,IAAAA,0BAAc,EAACD,OAAO;QAC9B;AACN;AAEO,eAAeX,kBAAkBM,MAQvC;IACC,MAAM,EAAEO,OAAO,EAAEC,iBAAiB,EAAE,GAAG,MAAMC,IAAAA,yBAAgB,EAAC;QAC5DC,YAAYV,OAAOW,IAAI;QACvBC,WAAWZ,OAAOY,SAAS,IAAK,CAAA,KAAO,CAAA;QACvCC,UAAUb,OAAOa,QAAQ,KAAK;QAC9BC,mBAAmBd,OAAOc,iBAAiB;QAC3CC,SAASf,OAAOe,OAAO;IACzB;IAEA,IAAIf,OAAOgB,gBAAgB,EAAE;QAC3BT,QAAQU,OAAO,CAACC,UAAU,CAACC,kBAAkB,GAAGnB,OAAOgB,gBAAgB;IACzE;IAEA,KAAK,MAAMI,aAAapB,OAAOqB,KAAK,CAAE;QACpCb,kBAAkBY;IACpB;IACA,OAAOb;AACT;AAEO,MAAMZ,MAAMG,iBAAiB,eAAewB,oBAAoBtB,MAAM;QAqBvEA;IApBJ,MAAMO,UAAU,MAAMb,kBAAkBM;IACxC,MAAMuB,SAASvB,OAAOwB,OAAO,CAACC,OAAO,CAAC,CAAC,uBAAuB,CAAC,CAAC;IAChE,MAAMC,cAAc,OAAOH,WAAW,WAAWA,OAAOI,KAAK,CAAC,OAAO,EAAE;IACvE,IAAID,YAAYE,QAAQ,CAAC5B,OAAOW,IAAI,GAAG;QACrC,OAAO;YACLR,WAAW0B,QAAQC,OAAO;YAC1BC,UAAU,IAAIxB,QAAQU,OAAO,CAACe,QAAQ,CAAC,MAAM;gBAC3CP,SAAS;oBACP,qBAAqB;gBACvB;YACF;QACF;IACF;IAEA,MAAMQ,eAGJ1B,QAAQU,OAAO,CAACiB,QAAQ,CAAC,CAAC,WAAW,EAAElC,OAAOW,IAAI,CAAC,CAAC,CAAC,CAACwB,OAAO;IAE/D,MAAMC,SAAS,CAAC;QAAC;QAAQ;KAAM,CAACR,QAAQ,CAAC5B,OAAOwB,OAAO,CAACa,MAAM,KAC1DrC,uBAAAA,OAAOwB,OAAO,CAACc,IAAI,qBAAnBtC,qBAAqBuC,eAAe,KACpCC;IAEJ,MAAMC,cAAclC,QAAQmC,QAAQ,CAAC;IACrC,MAAMC,cAAc,IAAIC,IAAI5C,OAAOwB,OAAO,CAACqB,GAAG;IAC9CF,YAAYG,YAAY,CAACC,MAAM,CAACC,sCAAoB;IAEpDhD,OAAOwB,OAAO,CAACqB,GAAG,GAAGF,YAAYM,QAAQ;IAEzC,IAAI;QACF,MAAM/C,SAAS,MAAM+B,aAAa;YAChCT,SAAS;gBACP,GAAGxB,OAAOwB,OAAO;gBACjBc,MACEF,UAAUc,IAAAA,gCAAmB,EAAC3C,QAAQU,OAAO,EAAEwB,aAAaL;YAChE;QACF;QACA,KAAK,MAAMe,cAActD,kBAAmB;YAC1CK,OAAO6B,QAAQ,CAACN,OAAO,CAACsB,MAAM,CAACI;QACjC;QACA,OAAOjD;IACT,SAAU;YACFF;QAAN,QAAMA,wBAAAA,OAAOwB,OAAO,CAACc,IAAI,qBAAnBtC,sBAAqBoD,QAAQ;IACrC;AACF"}