{"version": 3, "sources": ["../../../../src/server/web/sandbox/context.ts"], "names": ["clearModuleContext", "getModuleContext", "moduleContexts", "Map", "pendingModuleCaches", "path", "handleContext", "key", "cache", "context", "paths", "has", "delete", "loadWasm", "wasm", "modules", "Promise", "all", "map", "binding", "module", "WebAssembly", "compile", "fs", "readFile", "filePath", "name", "buildEnvironmentVariablesFrom", "pairs", "Object", "keys", "process", "env", "fromEntries", "NEXT_RUNTIME", "throwUnsupportedAPIError", "error", "Error", "decorateServerError", "COMPILER_NAMES", "edgeServer", "createProcessPolyfill", "processPolyfill", "overridenValue", "defineProperty", "get", "undefined", "set", "value", "enumerable", "addStub", "getDecorateUnhandledError", "runtime", "EdgeRuntimeError", "evaluate", "getDecorateUnhandledRejection", "rejected", "reason", "NativeModuleMap", "mods", "pick", "BufferImplementation", "EventsImplementation", "AsyncHooksImplementation", "AssertImplementation", "UtilImplementation", "entries", "createModuleContext", "options", "warnedEvals", "Set", "warnedWasmCodegens", "edgeFunctionEntry", "EdgeRuntime", "codeGeneration", "NODE_ENV", "strings", "extend", "id", "TypeError", "__next_eval__", "fn", "toString", "warning", "getServerError", "captureStackTrace", "add", "onWarning", "__next_webassembly_compile__", "__next_webassembly_instantiate__", "result", "instantiatedFromBuffer", "hasOwnProperty", "__fetch", "fetch", "input", "init", "callingError", "assetResponse", "fetchInlineAsset", "assets", "distDir", "headers", "Headers", "prevs", "split", "concat", "moduleName", "join", "response", "url", "String", "catch", "err", "message", "stack", "__Request", "Request", "constructor", "validateURL", "next", "__redirect", "Response", "redirect", "bind", "args", "EDGE_UNSUPPORTED_NODE_APIS", "assign", "AsyncLocalStorage", "decorateUnhandledError", "addEventListener", "decorateUnhandledRejection", "getModuleContextShared", "deferredModuleContext", "lazyModuleContext", "useCache", "moduleContext", "evaluateInContext", "filepath", "content", "readFileSync", "runInContext", "filename"], "mappings": ";;;;;;;;;;;;;;;IA+CsBA,kBAAkB;eAAlBA;;IAuXAC,gBAAgB;eAAhBA;;;6BAhaY;4BAI3B;2BAIA;6BACqB;oBACiB;uBACjB;sBACP;mCACY;oBACJ;mEACI;mEACA;mEACA;iEACF;wEACM;;;;;;AAQrC;;;;CAIC,GACD,MAAMC,iBAAiB,IAAIC;AAE3B,MAAMC,sBAAsB,IAAID;AAOzB,eAAeH,mBAAmBK,IAAY;IACnD,MAAMC,gBAAgB,CACpBC,KACAC,OACAC;QAEA,IAAID,yBAAAA,MAAOE,KAAK,CAACC,GAAG,CAACN,OAAO;YAC1BI,QAAQG,MAAM,CAACL;QACjB;IACF;IAEA,KAAK,MAAM,CAACA,KAAKC,MAAM,IAAIN,eAAgB;QACzCI,cAAcC,KAAKC,OAAON;IAC5B;IACA,KAAK,MAAM,CAACK,KAAKC,MAAM,IAAIJ,oBAAqB;QAC9CE,cAAcC,KAAK,MAAMC,OAAOJ;IAClC;AACF;AAEA,eAAeS,SACbC,IAAoB;IAEpB,MAAMC,UAA8C,CAAC;IAErD,MAAMC,QAAQC,GAAG,CACfH,KAAKI,GAAG,CAAC,OAAOC;QACd,MAAMC,UAAS,MAAMC,YAAYC,OAAO,CACtC,MAAMC,YAAE,CAACC,QAAQ,CAACL,QAAQM,QAAQ;QAEpCV,OAAO,CAACI,QAAQO,IAAI,CAAC,GAAGN;IAC1B;IAGF,OAAOL;AACT;AAEA,SAASY;IACP,MAAMC,QAAQC,OAAOC,IAAI,CAACC,QAAQC,GAAG,EAAEd,GAAG,CAAC,CAACX,MAAQ;YAACA;YAAKwB,QAAQC,GAAG,CAACzB,IAAI;SAAC;IAC3E,MAAMyB,MAAMH,OAAOI,WAAW,CAACL;IAC/BI,IAAIE,YAAY,GAAG;IACnB,OAAOF;AACT;AAEA,SAASG,yBAAyBT,IAAY;IAC5C,MAAMU,QACJ,IAAIC,MAAM,CAAC,uBAAuB,EAAEX,KAAK;8DACiB,CAAC;IAC7DY,IAAAA,+BAAmB,EAACF,OAAOG,yBAAc,CAACC,UAAU;IACpD,MAAMJ;AACR;AAEA,SAASK;IACP,MAAMC,kBAAkB;QAAEV,KAAKL;IAAgC;IAC/D,MAAMgB,iBAAsC,CAAC;IAC7C,KAAK,MAAMpC,OAAOsB,OAAOC,IAAI,CAACC,SAAU;QACtC,IAAIxB,QAAQ,OAAO;QACnBsB,OAAOe,cAAc,CAACF,iBAAiBnC,KAAK;YAC1CsC;gBACE,IAAIF,cAAc,CAACpC,IAAI,KAAKuC,WAAW;oBACrC,OAAOH,cAAc,CAACpC,IAAI;gBAC5B;gBACA,IAAI,OAAO,AAACwB,OAAe,CAACxB,IAAI,KAAK,YAAY;oBAC/C,OAAO,IAAM4B,yBAAyB,CAAC,QAAQ,EAAE5B,IAAI,CAAC;gBACxD;gBACA,OAAOuC;YACT;YACAC,KAAIC,KAAK;gBACPL,cAAc,CAACpC,IAAI,GAAGyC;YACxB;YACAC,YAAY;QACd;IACF;IACA,OAAOP;AACT;AAEA,SAASQ,QAAQzC,OAA+B,EAAEiB,IAAY;IAC5DG,OAAOe,cAAc,CAACnC,SAASiB,MAAM;QACnCmB;YACE,OAAO;gBACLV,yBAAyBT;YAC3B;QACF;QACAuB,YAAY;IACd;AACF;AAEA,SAASE,0BAA0BC,OAAoB;IACrD,MAAMC,mBAAmBD,QAAQE,QAAQ,CAAC,CAAC,KAAK,CAAC;IACjD,OAAO,CAAClB;QACN,IAAIA,iBAAiBiB,kBAAkB;YACrCf,IAAAA,+BAAmB,EAACF,OAAOG,yBAAc,CAACC,UAAU;QACtD;IACF;AACF;AAEA,SAASe,8BAA8BH,OAAoB;IACzD,MAAMC,mBAAmBD,QAAQE,QAAQ,CAAC,CAAC,KAAK,CAAC;IACjD,OAAO,CAACE;QACN,IAAIA,SAASC,MAAM,YAAYJ,kBAAkB;YAC/Cf,IAAAA,+BAAmB,EAACkB,SAASC,MAAM,EAAElB,yBAAc,CAACC,UAAU;QAChE;IACF;AACF;AAEA,MAAMkB,kBAAkB,AAAC,CAAA;IACvB,MAAMC,OAGF;QACF,eAAeC,IAAAA,UAAI,EAACC,mBAAoB,EAAE;YACxC;YACA;YACA;YACA;YACA;SACD;QACD,eAAeD,IAAAA,UAAI,EAACE,mBAAoB,EAAE;YACxC;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,oBAAoBF,IAAAA,UAAI,EAACG,wBAAwB,EAAE;YACjD;YACA;SACD;QACD,eAAeH,IAAAA,UAAI,EAACI,mBAAoB,EAAE;YACxC;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAaJ,IAAAA,UAAI,EAACK,iBAAkB,EAAE;YACpC;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA,OAAO,IAAI9D,IAAI0B,OAAOqC,OAAO,CAACP;AAChC,CAAA;AAEA;;;CAGC,GACD,eAAeQ,oBAAoBC,OAA6B;IAC9D,MAAMC,cAAc,IAAIC;IACxB,MAAMC,qBAAqB,IAAID;IAC/B,MAAMxD,OAAO,MAAMD,SAASuD,QAAQI,iBAAiB,CAAC1D,IAAI,IAAI,EAAE;IAChE,MAAMsC,UAAU,IAAIqB,wBAAW,CAAC;QAC9BC,gBACE3C,QAAQC,GAAG,CAAC2C,QAAQ,KAAK,eACrB;YAAEC,SAAS;YAAM9D,MAAM;QAAK,IAC5BgC;QACN+B,QAAQ,CAACpE;YACPA,QAAQsB,OAAO,GAAGU;YAElBZ,OAAOe,cAAc,CAACnC,SAAS,WAAW;gBACxCwC,YAAY;gBACZD,OAAO,CAAC8B;oBACN,MAAM9B,QAAQU,gBAAgBb,GAAG,CAACiC;oBAClC,IAAI,CAAC9B,OAAO;wBACV,MAAM+B,UAAU,8BAA8BD;oBAChD;oBACA,OAAO9B;gBACT;YACF;YAEAvC,QAAQuE,aAAa,GAAG,SAASA,cAAcC,EAAY;gBACzD,MAAM1E,MAAM0E,GAAGC,QAAQ;gBACvB,IAAI,CAACb,YAAY1D,GAAG,CAACJ,MAAM;oBACzB,MAAM4E,UAAUC,IAAAA,0BAAc,EAC5B,IAAI/C,MACF,CAAC;yEAC0D,CAAC,GAE9DE,yBAAc,CAACC,UAAU;oBAE3B2C,QAAQzD,IAAI,GAAG;oBACfW,MAAMgD,iBAAiB,CAACF,SAASH;oBACjCX,YAAYiB,GAAG,CAAC/E;oBAChB6D,QAAQmB,SAAS,CAACJ;gBACpB;gBACA,OAAOF;YACT;YAEAxE,QAAQ+E,4BAA4B,GAClC,SAASA,6BAA6BP,EAAY;gBAChD,MAAM1E,MAAM0E,GAAGC,QAAQ;gBACvB,IAAI,CAACX,mBAAmB5D,GAAG,CAACJ,MAAM;oBAChC,MAAM4E,UAAUC,IAAAA,0BAAc,EAC5B,IAAI/C,MAAM,CAAC;yEACgD,CAAC,GAC5DE,yBAAc,CAACC,UAAU;oBAE3B2C,QAAQzD,IAAI,GAAG;oBACfW,MAAMgD,iBAAiB,CAACF,SAASK;oBACjCjB,mBAAmBe,GAAG,CAAC/E;oBACvB6D,QAAQmB,SAAS,CAACJ;gBACpB;gBACA,OAAOF;YACT;YAEFxE,QAAQgF,gCAAgC,GACtC,eAAeA,iCAAiCR,EAAY;gBAC1D,MAAMS,SAAS,MAAMT;gBAErB,kEAAkE;gBAClE,oEAAoE;gBACpE,oEAAoE;gBACpE,uCAAuC;gBACvC,EAAE;gBACF,wJAAwJ;gBACxJ,MAAMU,yBAAyBD,OAAOE,cAAc,CAAC;gBAErD,MAAMrF,MAAM0E,GAAGC,QAAQ;gBACvB,IAAIS,0BAA0B,CAACpB,mBAAmB5D,GAAG,CAACJ,MAAM;oBAC1D,MAAM4E,UAAUC,IAAAA,0BAAc,EAC5B,IAAI/C,MAAM,CAAC;yEACgD,CAAC,GAC5DE,yBAAc,CAACC,UAAU;oBAE3B2C,QAAQzD,IAAI,GAAG;oBACfW,MAAMgD,iBAAiB,CAACF,SAASM;oBACjClB,mBAAmBe,GAAG,CAAC/E;oBACvB6D,QAAQmB,SAAS,CAACJ;gBACpB;gBACA,OAAOO;YACT;YAEF,MAAMG,UAAUpF,QAAQqF,KAAK;YAC7BrF,QAAQqF,KAAK,GAAG,OAAOC,OAAOC,OAAO,CAAC,CAAC;oBAcnCA;gBAbF,MAAMC,eAAe,IAAI5D,MAAM;gBAC/B,MAAM6D,gBAAgB,MAAMC,IAAAA,mCAAgB,EAAC;oBAC3CJ;oBACAK,QAAQhC,QAAQI,iBAAiB,CAAC4B,MAAM;oBACxCC,SAASjC,QAAQiC,OAAO;oBACxB5F;gBACF;gBACA,IAAIyF,eAAe;oBACjB,OAAOA;gBACT;gBAEAF,KAAKM,OAAO,GAAG,IAAIC,QAAQP,KAAKM,OAAO,IAAI,CAAC;gBAC5C,MAAME,QACJR,EAAAA,oBAAAA,KAAKM,OAAO,CAACzD,GAAG,CAAC,CAAC,uBAAuB,CAAC,sBAA1CmD,kBAA6CS,KAAK,CAAC,SAAQ,EAAE;gBAC/D,MAAMzD,QAAQwD,MAAME,MAAM,CAACtC,QAAQuC,UAAU,EAAEC,IAAI,CAAC;gBACpDZ,KAAKM,OAAO,CAACvD,GAAG,CAAC,2BAA2BC;gBAE5C,IAAI,CAACgD,KAAKM,OAAO,CAAC3F,GAAG,CAAC,eAAe;oBACnCqF,KAAKM,OAAO,CAACvD,GAAG,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,kBAAkB,CAAC;gBACrD;gBAEA,MAAM8D,WACJ,OAAOd,UAAU,YAAY,SAASA,QAClCF,QAAQE,MAAMe,GAAG,EAAE;oBACjB,GAAGlD,IAAAA,UAAI,EAACmC,OAAO;wBACb;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;qBACD,CAAC;oBACF,GAAGC,IAAI;oBACPM,SAAS;wBACP,GAAGzE,OAAOI,WAAW,CAAC8D,MAAMO,OAAO,CAAC;wBACpC,GAAGzE,OAAOI,WAAW,CAAC+D,KAAKM,OAAO,CAAC;oBACrC;gBACF,KACAT,QAAQkB,OAAOhB,QAAQC;gBAE7B,OAAO,MAAMa,SAASG,KAAK,CAAC,CAACC;oBAC3BhB,aAAaiB,OAAO,GAAGD,IAAIC,OAAO;oBAClCD,IAAIE,KAAK,GAAGlB,aAAakB,KAAK;oBAC9B,MAAMF;gBACR;YACF;YAEA,MAAMG,YAAY3G,QAAQ4G,OAAO;YACjC5G,QAAQ4G,OAAO,GAAG,cAAcD;gBAE9BE,YAAYvB,KAAwB,EAAEC,IAA8B,CAAE;oBACpE,MAAMc,MACJ,OAAOf,UAAU,YAAY,SAASA,QAClCA,MAAMe,GAAG,GACTC,OAAOhB;oBACbwB,IAAAA,kBAAW,EAACT;oBACZ,KAAK,CAACA,KAAKd;oBACX,IAAI,CAACwB,IAAI,GAAGxB,wBAAAA,KAAMwB,IAAI;gBACxB;YACF;YAEA,MAAMC,aAAahH,QAAQiH,QAAQ,CAACC,QAAQ,CAACC,IAAI,CAACnH,QAAQiH,QAAQ;YAClEjH,QAAQiH,QAAQ,CAACC,QAAQ,GAAG,CAAC,GAAGE;gBAC9BN,IAAAA,kBAAW,EAACM,IAAI,CAAC,EAAE;gBACnB,OAAOJ,cAAcI;YACvB;YAEA,KAAK,MAAMnG,QAAQoG,qCAA0B,CAAE;gBAC7C5E,QAAQzC,SAASiB;YACnB;YAEAG,OAAOkG,MAAM,CAACtH,SAASK;YAEvBL,QAAQuH,iBAAiB,GAAGA,8BAAiB;YAE7C,OAAOvH;QACT;IACF;IAEA,MAAMwH,yBAAyB9E,0BAA0BC;IACzDA,QAAQ3C,OAAO,CAACyH,gBAAgB,CAAC,SAASD;IAC1C,MAAME,6BAA6B5E,8BAA8BH;IACjEA,QAAQ3C,OAAO,CAACyH,gBAAgB,CAC9B,sBACAC;IAGF,OAAO;QACL/E;QACA1C,OAAO,IAAIP;QACXkE,aAAa,IAAIC;IACnB;AACF;AAUA,SAAS8D,uBAAuBhE,OAA6B;IAC3D,IAAIiE,wBAAwBjI,oBAAoByC,GAAG,CAACuB,QAAQuC,UAAU;IACtE,IAAI,CAAC0B,uBAAuB;QAC1BA,wBAAwBlE,oBAAoBC;QAC5ChE,oBAAoB2C,GAAG,CAACqB,QAAQuC,UAAU,EAAE0B;IAC9C;IACA,OAAOA;AACT;AAQO,eAAepI,iBAAiBmE,OAA6B;IAMlE,IAAIkE;IAIJ,IAAIlE,QAAQmE,QAAQ,EAAE;QACpBD,oBACEpI,eAAe2C,GAAG,CAACuB,QAAQuC,UAAU,KACpC,MAAMyB,uBAAuBhE;IAClC;IAEA,IAAI,CAACkE,mBAAmB;QACtBA,oBAAoB,MAAMnE,oBAAoBC;QAC9ClE,eAAe6C,GAAG,CAACqB,QAAQuC,UAAU,EAAE2B;IACzC;IAEA,MAAME,gBAAgBF;IAEtB,MAAMG,oBAAoB,CAACC;QACzB,IAAI,CAACF,cAAc9H,KAAK,CAACC,GAAG,CAAC+H,WAAW;YACtC,MAAMC,UAAUC,IAAAA,gBAAY,EAACF,UAAU;YACvC,IAAI;gBACFG,IAAAA,gBAAY,EAACF,SAASH,cAAcpF,OAAO,CAAC3C,OAAO,EAAE;oBACnDqI,UAAUJ;gBACZ;gBACAF,cAAc9H,KAAK,CAACqC,GAAG,CAAC2F,UAAUC;YACpC,EAAE,OAAOvG,OAAO;gBACd,IAAIgC,QAAQmE,QAAQ,EAAE;oBACpBC,iCAAAA,cAAe9H,KAAK,CAACE,MAAM,CAAC8H;gBAC9B;gBACA,MAAMtG;YACR;QACF;IACF;IAEA,OAAO;QAAE,GAAGoG,aAAa;QAAEC;IAAkB;AAC/C"}