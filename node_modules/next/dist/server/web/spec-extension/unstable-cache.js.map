{"version": 3, "sources": ["../../../../src/server/web/spec-extension/unstable-cache.ts"], "names": ["unstable_cache", "cb", "keyParts", "options", "staticGenerationAsyncStorage", "fetch", "__nextGetStaticStore", "_staticGenerationAsyncStorage", "revalidate", "Error", "toString", "cachedCb", "args", "store", "getStore", "incrementalCache", "globalThis", "__incrementalCache", "joinedKey", "Array", "isArray", "join", "JSON", "stringify", "run", "fetchCache", "urlPathname", "isStaticGeneration", "tags", "validateTags", "tag", "includes", "push", "implicitTags", "addImplicitTags", "cache<PERSON>ey", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cacheEntry", "isOnDemandRevalidate", "get", "softTags", "invokeCallback", "result", "set", "kind", "data", "headers", "body", "status", "url", "CACHE_ONE_YEAR", "value", "console", "error", "cachedValue", "isStale", "resData", "parse", "pendingRevalidates", "catch", "err"], "mappings": ";;;;+BAUgBA;;;eAAAA;;;sDAN8D;2BAC/C;4BACe;AAIvC,SAASA,eACdC,EAAK,EACLC,QAAmB,EACnBC,UAGI,CAAC,CAAC;IAEN,MAAMC,+BACJ,CAAA,AAACC,MAAcC,oBAAoB,oBAAnC,AAACD,MAAcC,oBAAoB,MAAlCD,WAA0CE,kEAA6B;IAE1E,IAAIJ,QAAQK,UAAU,KAAK,GAAG;QAC5B,MAAM,IAAIC,MACR,CAAC,wFAAwF,EAAER,GAAGS,QAAQ,GAAG,CAAC;IAE9G;IAEA,MAAMC,WAAW,OAAO,GAAGC;QACzB,MAAMC,QACJT,gDAAAA,6BAA8BU,QAAQ;QAExC,MAAMC,mBAGJF,CAAAA,yBAAAA,MAAOE,gBAAgB,KAAI,AAACC,WAAmBC,kBAAkB;QAEnE,IAAI,CAACF,kBAAkB;YACrB,MAAM,IAAIN,MACR,CAAC,sDAAsD,EAAER,GAAGS,QAAQ,GAAG,CAAC;QAE5E;QAEA,MAAMQ,YAAY,CAAC,EAAEjB,GAAGS,QAAQ,GAAG,CAAC,EAClCS,MAAMC,OAAO,CAAClB,aAAaA,SAASmB,IAAI,CAAC,KAC1C,CAAC,EAAEC,KAAKC,SAAS,CAACX,MAAM,CAAC;QAE1B,6DAA6D;QAC7D,oEAAoE;QACpE,oEAAoE;QACpE,0BAA0B;QAC1B,OAAOR,6BAA6BoB,GAAG,CACrC;YACE,GAAGX,KAAK;YACRY,YAAY;YACZC,aAAab,CAAAA,yBAAAA,MAAOa,WAAW,KAAI;YACnCC,oBAAoB,CAAC,EAACd,yBAAAA,MAAOc,kBAAkB;QACjD,GACA;YACE,MAAMC,OAAOC,IAAAA,wBAAY,EACvB1B,QAAQyB,IAAI,IAAI,EAAE,EAClB,CAAC,eAAe,EAAE3B,GAAGS,QAAQ,GAAG,CAAC;YAGnC,IAAIS,MAAMC,OAAO,CAACQ,SAASf,OAAO;gBAChC,IAAI,CAACA,MAAMe,IAAI,EAAE;oBACff,MAAMe,IAAI,GAAG,EAAE;gBACjB;gBACA,KAAK,MAAME,OAAOF,KAAM;oBACtB,IAAI,CAACf,MAAMe,IAAI,CAACG,QAAQ,CAACD,MAAM;wBAC7BjB,MAAMe,IAAI,CAACI,IAAI,CAACF;oBAClB;gBACF;YACF;YACA,MAAMG,eAAeC,IAAAA,2BAAe,EAACrB;YAErC,MAAMsB,WAAW,OAAMpB,oCAAAA,iBAAkBqB,aAAa,CAAClB;YACvD,MAAMmB,aACJF,YACA,CACEtB,CAAAA,CAAAA,yBAAAA,MAAOyB,oBAAoB,KAAIvB,iBAAiBuB,oBAAoB,AAAD,KAEpE,OAAMvB,oCAAAA,iBAAkBwB,GAAG,CAACJ,UAAU;gBACrCV,YAAY;gBACZjB,YAAYL,QAAQK,UAAU;gBAC9BoB;gBACAY,UAAUP;YACZ;YAEF,MAAMQ,iBAAiB;gBACrB,MAAMC,SAAS,MAAMzC,MAAMW;gBAE3B,IAAIuB,YAAYpB,kBAAkB;oBAChC,MAAMA,iBAAiB4B,GAAG,CACxBR,UACA;wBACES,MAAM;wBACNC,MAAM;4BACJC,SAAS,CAAC;4BACV,gCAAgC;4BAChCC,MAAMzB,KAAKC,SAAS,CAACmB;4BACrBM,QAAQ;4BACRC,KAAK;wBACP;wBACAzC,YACE,OAAOL,QAAQK,UAAU,KAAK,WAC1B0C,yBAAc,GACd/C,QAAQK,UAAU;oBAC1B,GACA;wBACEA,YAAYL,QAAQK,UAAU;wBAC9BiB,YAAY;wBACZG;oBACF;gBAEJ;gBACA,OAAOc;YACT;YAEA,IAAI,CAACL,cAAc,CAACA,WAAWc,KAAK,EAAE;gBACpC,OAAOV;YACT;YAEA,IAAIJ,WAAWc,KAAK,CAACP,IAAI,KAAK,SAAS;gBACrCQ,QAAQC,KAAK,CACX,CAAC,0CAA0C,EAAEnC,UAAU,CAAC;gBAE1D,OAAOuB;YACT;YACA,IAAIa;YACJ,MAAMC,UAAUlB,WAAWkB,OAAO;YAElC,IAAIlB,YAAY;gBACd,MAAMmB,UAAUnB,WAAWc,KAAK,CAACN,IAAI;gBACrCS,cAAchC,KAAKmC,KAAK,CAACD,QAAQT,IAAI;YACvC;YAEA,IAAIQ,SAAS;gBACX,IAAI,CAAC1C,OAAO;oBACV,OAAO4B;gBACT,OAAO;oBACL,IAAI,CAAC5B,MAAM6C,kBAAkB,EAAE;wBAC7B7C,MAAM6C,kBAAkB,GAAG,EAAE;oBAC/B;oBACA7C,MAAM6C,kBAAkB,CAAC1B,IAAI,CAC3BS,iBAAiBkB,KAAK,CAAC,CAACC,MACtBR,QAAQC,KAAK,CAAC,CAAC,6BAA6B,EAAEnC,UAAU,CAAC,EAAE0C;gBAGjE;YACF;YACA,OAAON;QACT;IAEJ;IACA,yGAAyG;IACzG,OAAO3C;AACT"}