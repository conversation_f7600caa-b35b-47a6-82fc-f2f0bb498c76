{"version": 3, "sources": ["../../../../../src/server/web/spec-extension/adapters/next-request.ts"], "names": ["signalFromNodeResponse", "NextRequestAdapter", "response", "errored", "destroyed", "AbortSignal", "abort", "controller", "AbortController", "onClose", "off", "onFinish", "once", "signal", "fromBaseNextRequest", "request", "fromWebNextRequest", "fromNodeNextRequest", "body", "method", "url", "startsWith", "URL", "base", "getRequestMeta", "NextRequest", "headers", "fromNodeOutgoingHttpHeaders", "duplex"], "mappings": ";;;;;;;;;;;;;;;IAiBgBA,sBAAsB;eAAtBA;;IAsBHC,kBAAkB;eAAlBA;;;6BAlCkB;uBACa;yBAChB;AAUrB,SAASD,uBAAuBE,QAAkB;IACvD,MAAM,EAAEC,OAAO,EAAEC,SAAS,EAAE,GAAGF;IAC/B,IAAIC,WAAWC,WAAW,OAAOC,YAAYC,KAAK,CAACH;IAEnD,MAAMI,aAAa,IAAIC;IACvB,6EAA6E;IAC7E,4EAA4E;IAC5E,mDAAmD;IACnD,SAASC;QACPF,WAAWD,KAAK;QAChB,mEAAmE;QACnEJ,SAASQ,GAAG,CAAC,UAAUC;IACzB;IACA,SAASA;QACPT,SAASQ,GAAG,CAAC,SAASD;IACxB;IACAP,SAASU,IAAI,CAAC,SAASH;IACvBP,SAASU,IAAI,CAAC,UAAUD;IAExB,OAAOJ,WAAWM,MAAM;AAC1B;AAEO,MAAMZ;IACX,OAAca,oBACZC,OAAwB,EACxBF,MAAmB,EACN;QACb,oCAAoC;QACpC,IAAI,aAAaE,WAAW,AAACA,QAA2BA,OAAO,EAAE;YAC/D,OAAOd,mBAAmBe,kBAAkB,CAACD;QAC/C;QAEA,OAAOd,mBAAmBgB,mBAAmB,CAC3CF,SACAF;IAEJ;IAEA,OAAcI,oBACZF,OAAwB,EACxBF,MAAmB,EACN;QACb,6CAA6C;QAC7C,IAAIK,OAAwB;QAC5B,IAAIH,QAAQI,MAAM,KAAK,SAASJ,QAAQI,MAAM,KAAK,UAAUJ,QAAQG,IAAI,EAAE;YACzE,qFAAqF;YACrFA,OAAOH,QAAQG,IAAI;QACrB;QAEA,IAAIE;QACJ,IAAIL,QAAQK,GAAG,CAACC,UAAU,CAAC,SAAS;YAClCD,MAAM,IAAIE,IAAIP,QAAQK,GAAG;QAC3B,OAAO;YACL,+CAA+C;YAC/C,MAAMG,OAAOC,IAAAA,2BAAc,EAACT,SAAS;YACrC,IAAI,CAACQ,QAAQ,CAACA,KAAKF,UAAU,CAAC,SAAS;gBACrC,wEAAwE;gBACxE,uEAAuE;gBACvE,4DAA4D;gBAC5DD,MAAM,IAAIE,IAAIP,QAAQK,GAAG,EAAE;YAC7B,OAAO;gBACLA,MAAM,IAAIE,IAAIP,QAAQK,GAAG,EAAEG;YAC7B;QACF;QAEA,OAAO,IAAIE,oBAAW,CAACL,KAAK;YAC1BF;YACAC,QAAQJ,QAAQI,MAAM;YACtBO,SAASC,IAAAA,kCAA2B,EAACZ,QAAQW,OAAO;YACpD,mEAAmE;YACnEE,QAAQ;YACRf;QAIF;IACF;IAEA,OAAcG,mBAAmBD,OAAuB,EAAe;QACrE,6CAA6C;QAC7C,IAAIG,OAA8B;QAClC,IAAIH,QAAQI,MAAM,KAAK,SAASJ,QAAQI,MAAM,KAAK,QAAQ;YACzDD,OAAOH,QAAQG,IAAI;QACrB;QAEA,OAAO,IAAIO,oBAAW,CAACV,QAAQK,GAAG,EAAE;YAClCF;YACAC,QAAQJ,QAAQI,MAAM;YACtBO,SAASC,IAAAA,kCAA2B,EAACZ,QAAQW,OAAO;YACpD,mEAAmE;YACnEE,QAAQ;YACRf,QAAQE,QAAQA,OAAO,CAACF,MAAM;QAIhC;IACF;AACF"}