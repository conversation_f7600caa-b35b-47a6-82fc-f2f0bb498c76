{"version": 3, "sources": ["../../../src/server/web/edge-route-module-wrapper.ts"], "names": ["EdgeRouteModuleWrapper", "routeModule", "matcher", "RouteMatcher", "definition", "wrap", "options", "wrapper", "opts", "adapter", "IncrementalCache", "handler", "bind", "request", "evt", "pathname", "removeTrailingSlash", "URL", "url", "basePath", "nextUrl", "removePathPrefix", "match", "Error", "prerenderManifest", "self", "__PRERENDER_MANIFEST", "JSON", "parse", "undefined", "context", "params", "version", "routes", "dynamicRoutes", "preview", "previewModeEncryptionKey", "previewModeId", "previewModeSigningKey", "notFoundRoutes", "renderOpts", "supportsDynamicHTML", "res", "handle", "waitUntilPromises", "internal_getCurrentFunctionWaitUntil", "waitUntil", "push", "Promise", "all"], "mappings": ";;;;+BAwBaA;;;eAAAA;;;QAjBN;yBAEsC;kCACZ;8BACJ;qCACO;kCACH;uCAEoB;AAS9C,MAAMA;IAGX;;;;;GAKC,GACD,YAAqCC,YAAkC;2BAAlCA;QACnC,wEAAwE;QACxE,IAAI,CAACC,OAAO,GAAG,IAAIC,0BAAY,CAACF,YAAYG,UAAU;IACxD;IAEA;;;;;;;;GAQC,GACD,OAAcC,KACZJ,WAAgC,EAChCK,UAAuB,CAAC,CAAC,EACzB;QACA,6BAA6B;QAC7B,MAAMC,UAAU,IAAIP,uBAAuBC;QAE3C,gCAAgC;QAChC,OAAO,CAACO;YACN,OAAOC,IAAAA,gBAAO,EAAC;gBACb,GAAGD,IAAI;gBACP,GAAGF,OAAO;gBACVI,kBAAAA,kCAAgB;gBAChB,kEAAkE;gBAClEC,SAASJ,QAAQI,OAAO,CAACC,IAAI,CAACL;YAChC;QACF;IACF;IAEA,MAAcI,QACZE,OAAoB,EACpBC,GAAmB,EACA;QACnB,uEAAuE;QACvE,wBAAwB;QACxB,IAAIC,WAAWC,IAAAA,wCAAmB,EAAC,IAAIC,IAAIJ,QAAQK,GAAG,EAAEH,QAAQ;QAEhE,iEAAiE;QACjE,MAAM,EAAEI,QAAQ,EAAE,GAAGN,QAAQO,OAAO;QACpC,IAAID,UAAU;YACZ,+DAA+D;YAC/DJ,WAAWM,IAAAA,kCAAgB,EAACN,UAAUI;QACxC;QAEA,kCAAkC;QAClC,MAAMG,QAAQ,IAAI,CAACpB,OAAO,CAACoB,KAAK,CAACP;QACjC,IAAI,CAACO,OAAO;YACV,MAAM,IAAIC,MACR,CAAC,iDAAiD,EAAER,SAAS,uBAAuB,EAAE,IAAI,CAACb,OAAO,CAACE,UAAU,CAACW,QAAQ,CAAC,CAAC,CAAC;QAE7H;QAEA,MAAMS,oBACJ,OAAOC,KAAKC,oBAAoB,KAAK,WACjCC,KAAKC,KAAK,CAACH,KAAKC,oBAAoB,IACpCG;QAEN,wEAAwE;QACxE,kBAAkB;QAClB,MAAMC,UAAuC;YAC3CC,QAAQT,MAAMS,MAAM;YACpBP,mBAAmB;gBACjBQ,SAAS;gBACTC,QAAQ,CAAC;gBACTC,eAAe,CAAC;gBAChBC,SAASX,CAAAA,qCAAAA,kBAAmBW,OAAO,KAAI;oBACrCC,0BAA0B;oBAC1BC,eAAe;oBACfC,uBAAuB;gBACzB;gBACAC,gBAAgB,EAAE;YACpB;YACAC,YAAY;gBACVC,qBAAqB;YACvB;QACF;QAEA,qCAAqC;QACrC,MAAMC,MAAM,MAAM,IAAI,CAACzC,WAAW,CAAC0C,MAAM,CAAC9B,SAASiB;QAEnD,MAAMc,oBAAoB;YAACC,IAAAA,2DAAoC;SAAG;QAClE,IAAIf,QAAQU,UAAU,CAACM,SAAS,EAAE;YAChCF,kBAAkBG,IAAI,CAACjB,QAAQU,UAAU,CAACM,SAAS;QACrD;QACAhC,IAAIgC,SAAS,CAACE,QAAQC,GAAG,CAACL;QAE1B,OAAOF;IACT;AACF"}