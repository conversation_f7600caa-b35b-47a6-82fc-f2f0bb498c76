{"version": 3, "sources": ["../../../src/server/base-http/web.ts"], "names": ["WebNextRequest", "WebNextResponse", "BaseNextRequest", "constructor", "request", "url", "URL", "method", "href", "slice", "origin", "length", "clone", "body", "headers", "name", "value", "entries", "parseBody", "_limit", "Error", "BaseNextResponse", "sent", "_sent", "transformStream", "TransformStream", "writable", "Headers", "textBody", "undefined", "sendPromise", "Promise", "resolve", "sendResolve", "response", "then", "Response", "readable", "status", "statusCode", "statusText", "statusMessage", "<PERSON><PERSON><PERSON><PERSON>", "delete", "val", "Array", "isArray", "append", "removeHeader", "getHeader<PERSON><PERSON>ues", "<PERSON><PERSON><PERSON><PERSON>", "split", "map", "v", "trimStart", "get", "getHeaders", "toNodeOutgoingHttpHeaders", "<PERSON><PERSON><PERSON><PERSON>", "has", "append<PERSON><PERSON>er", "send", "toResponse"], "mappings": ";;;;;;;;;;;;;;;IAKaA,cAAc;eAAdA;;IA0BAC,eAAe;eAAfA;;;uBA7B6B;uBACQ;AAE3C,MAAMD,uBAAuBE,sBAAe;IAKjDC,YAAYC,OAAgB,CAAE;QAC5B,MAAMC,MAAM,IAAIC,IAAIF,QAAQC,GAAG;QAE/B,KAAK,CACHD,QAAQG,MAAM,EACdF,IAAIG,IAAI,CAACC,KAAK,CAACJ,IAAIK,MAAM,CAACC,MAAM,GAChCP,QAAQQ,KAAK,GAAGC,IAAI;QAEtB,IAAI,CAACT,OAAO,GAAGA;QAEf,IAAI,CAACU,OAAO,GAAG,CAAC;QAChB,KAAK,MAAM,CAACC,MAAMC,MAAM,IAAIZ,QAAQU,OAAO,CAACG,OAAO,GAAI;YACrD,IAAI,CAACH,OAAO,CAACC,KAAK,GAAGC;QACvB;IACF;IAEA,MAAME,UAAUC,MAAuB,EAAgB;QACrD,MAAM,IAAIC,MAAM;IAClB;AACF;AAEO,MAAMnB,wBAAwBoB,uBAAgB;IAoBnD,IAAIC,OAAO;QACT,OAAO,IAAI,CAACC,KAAK;IACnB;IAEApB,YAAmBqB,kBAAkB,IAAIC,iBAAiB,CAAE;QAC1D,KAAK,CAACD,gBAAgBE,QAAQ;+BADbF;aAvBXV,UAAU,IAAIa;aACdC,WAA+BC;aAC/BN,QAAQ;aAERO,cAAc,IAAIC,QAAc,CAACC;YACvC,IAAI,CAACC,WAAW,GAAGD;QACrB;aAEQE,WAAW,IAAI,CAACJ,WAAW,CAACK,IAAI,CAAC;YACvC,OAAO,IAAIC,SAAS,IAAI,CAACR,QAAQ,IAAI,IAAI,CAACJ,eAAe,CAACa,QAAQ,EAAE;gBAClEvB,SAAS,IAAI,CAACA,OAAO;gBACrBwB,QAAQ,IAAI,CAACC,UAAU;gBACvBC,YAAY,IAAI,CAACC,aAAa;YAChC;QACF;IAWA;IAEAC,UAAU3B,IAAY,EAAEC,KAAwB,EAAQ;QACtD,IAAI,CAACF,OAAO,CAAC6B,MAAM,CAAC5B;QACpB,KAAK,MAAM6B,OAAOC,MAAMC,OAAO,CAAC9B,SAASA,QAAQ;YAACA;SAAM,CAAE;YACxD,IAAI,CAACF,OAAO,CAACiC,MAAM,CAAChC,MAAM6B;QAC5B;QACA,OAAO,IAAI;IACb;IAEAI,aAAajC,IAAY,EAAQ;QAC/B,IAAI,CAACD,OAAO,CAAC6B,MAAM,CAAC5B;QACpB,OAAO,IAAI;IACb;IAEAkC,gBAAgBlC,IAAY,EAAwB;YAE3C;QADP,iEAAiE;QACjE,QAAO,kBAAA,IAAI,CAACmC,SAAS,CAACnC,0BAAf,gBACHoC,KAAK,CAAC,KACPC,GAAG,CAAC,CAACC,IAAMA,EAAEC,SAAS;IAC3B;IAEAJ,UAAUnC,IAAY,EAAsB;QAC1C,OAAO,IAAI,CAACD,OAAO,CAACyC,GAAG,CAACxC,SAASc;IACnC;IAEA2B,aAAkC;QAChC,OAAOC,IAAAA,gCAAyB,EAAC,IAAI,CAAC3C,OAAO;IAC/C;IAEA4C,UAAU3C,IAAY,EAAW;QAC/B,OAAO,IAAI,CAACD,OAAO,CAAC6C,GAAG,CAAC5C;IAC1B;IAEA6C,aAAa7C,IAAY,EAAEC,KAAa,EAAQ;QAC9C,IAAI,CAACF,OAAO,CAACiC,MAAM,CAAChC,MAAMC;QAC1B,OAAO,IAAI;IACb;IAEAH,KAAKG,KAAa,EAAE;QAClB,IAAI,CAACY,QAAQ,GAAGZ;QAChB,OAAO,IAAI;IACb;IAEA6C,OAAO;YACL,mBAAA;SAAA,oBAAA,CAAA,QAAA,IAAI,EAAC5B,WAAW,qBAAhB,uBAAA;QACA,IAAI,CAACV,KAAK,GAAG;IACf;IAEAuC,aAAa;QACX,OAAO,IAAI,CAAC5B,QAAQ;IACtB;AACF"}