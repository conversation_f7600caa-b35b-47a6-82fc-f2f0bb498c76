{"version": 3, "sources": ["../../../src/server/response-cache/index.ts"], "names": ["ResponseCache", "constructor", "minimalMode", "batcher", "<PERSON><PERSON>", "create", "cacheKeyFn", "key", "isOnDemandRevalidate", "schedulerFn", "scheduleOnNextTick", "minimalModeKey", "get", "responseGenerator", "context", "incrementalCache", "batch", "cache<PERSON>ey", "resolve", "previousCacheItem", "expiresAt", "Date", "now", "entry", "resolved", "cachedResponse", "value", "kind", "Error", "isStale", "revalidate", "curRevalidate", "html", "RenderResult", "fromStatic", "pageData", "headers", "status", "isPrefetch", "cacheEntry", "resolveValue", "isMiss", "set", "toUnchunkedString", "undefined", "err", "Math", "min", "max", "console", "error"], "mappings": ";;;;+BAaA;;;eAAqBA;;;;qEANI;yBACD;oCACW;qBAErB;;;;;;;;;;;;;;;;;;;AAEC,MAAMA;IAwBnBC,YAAYC,WAAoB,CAAE;aAvBjBC,UAAUC,gBAAO,CAACC,MAAM,CAIvC;YACA,0EAA0E;YAC1E,4EAA4E;YAC5EC,YAAY,CAAC,EAAEC,GAAG,EAAEC,oBAAoB,EAAE,GACxC,CAAC,EAAED,IAAI,CAAC,EAAEC,uBAAuB,MAAM,IAAI,CAAC;YAC9C,sEAAsE;YACtE,uEAAuE;YACvE,oDAAoD;YACpDC,aAAaC,sCAAkB;QACjC;QAWE,4EAA4E;QAC5E,qEAAqE;QACrE,MAAMC,iBAAiB;QACvB,IAAI,CAACA,eAAe,GAAGT;IACzB;IAEOU,IACLL,GAAkB,EAClBM,iBAAoC,EACpCC,OAIC,EACmC;QACpC,0EAA0E;QAC1E,6DAA6D;QAC7D,IAAI,CAACP,KAAK,OAAOM,kBAAkB,OAAO;QAE1C,MAAM,EAAEE,gBAAgB,EAAEP,uBAAuB,KAAK,EAAE,GAAGM;QAE3D,OAAO,IAAI,CAACX,OAAO,CAACa,KAAK,CACvB;YAAET;YAAKC;QAAqB,GAC5B,OAAOS,UAAUC;gBAKb;YAJF,+DAA+D;YAC/D,iDAAiD;YACjD,IACE,IAAI,CAAChB,WAAW,IAChB,EAAA,0BAAA,IAAI,CAACiB,iBAAiB,qBAAtB,wBAAwBZ,GAAG,MAAKU,YAChC,IAAI,CAACE,iBAAiB,CAACC,SAAS,GAAGC,KAAKC,GAAG,IAC3C;gBACA,OAAO,IAAI,CAACH,iBAAiB,CAACI,KAAK;YACrC;YAEA,IAAIC,WAAW;YACf,IAAIC,iBAAuC;YAC3C,IAAI;gBACFA,iBAAiB,CAAC,IAAI,CAACvB,WAAW,GAC9B,MAAMa,iBAAiBH,GAAG,CAACL,OAC3B;gBAEJ,IAAIkB,kBAAkB,CAACjB,sBAAsB;wBACvCiB,uBAUAA;oBAVJ,IAAIA,EAAAA,wBAAAA,eAAeC,KAAK,qBAApBD,sBAAsBE,IAAI,MAAK,SAAS;wBAC1C,MAAM,IAAIC,MACR,CAAC,oEAAoE,CAAC;oBAE1E;oBAEAV,QAAQ;wBACNW,SAASJ,eAAeI,OAAO;wBAC/BC,YAAYL,eAAeM,aAAa;wBACxCL,OACED,EAAAA,yBAAAA,eAAeC,KAAK,qBAApBD,uBAAsBE,IAAI,MAAK,SAC3B;4BACEA,MAAM;4BACNK,MAAMC,qBAAY,CAACC,UAAU,CAACT,eAAeC,KAAK,CAACM,IAAI;4BACvDG,UAAUV,eAAeC,KAAK,CAACS,QAAQ;4BACvCC,SAASX,eAAeC,KAAK,CAACU,OAAO;4BACrCC,QAAQZ,eAAeC,KAAK,CAACW,MAAM;wBACrC,IACAZ,eAAeC,KAAK;oBAC5B;oBACAF,WAAW;oBAEX,IAAI,CAACC,eAAeI,OAAO,IAAIf,QAAQwB,UAAU,EAAE;wBACjD,oDAAoD;wBACpD,oBAAoB;wBACpB,OAAO;oBACT;gBACF;gBAEA,MAAMC,aAAa,MAAM1B,kBAAkBW,UAAUC;gBACrD,MAAMe,eACJD,eAAe,OACX,OACA;oBACE,GAAGA,UAAU;oBACbE,QAAQ,CAAChB;gBACX;gBAEN,+DAA+D;gBAC/D,yBAAyB;gBACzB,IAAI,CAACjB,wBAAwB,CAACgB,UAAU;oBACtCN,QAAQsB;oBACRhB,WAAW;gBACb;gBAEA,IAAIe,cAAc,OAAOA,WAAWT,UAAU,KAAK,aAAa;oBAC9D,IAAI,IAAI,CAAC5B,WAAW,EAAE;wBACpB,IAAI,CAACiB,iBAAiB,GAAG;4BACvBZ,KAAKU;4BACLM,OAAOgB;4BACPnB,WAAWC,KAAKC,GAAG,KAAK;wBAC1B;oBACF,OAAO;4BAGHiB;wBAFF,MAAMxB,iBAAiB2B,GAAG,CACxBnC,KACAgC,EAAAA,oBAAAA,WAAWb,KAAK,qBAAhBa,kBAAkBZ,IAAI,MAAK,SACvB;4BACEA,MAAM;4BACNK,MAAMO,WAAWb,KAAK,CAACM,IAAI,CAACW,iBAAiB;4BAC7CR,UAAUI,WAAWb,KAAK,CAACS,QAAQ;4BACnCC,SAASG,WAAWb,KAAK,CAACU,OAAO;4BACjCC,QAAQE,WAAWb,KAAK,CAACW,MAAM;wBACjC,IACAE,WAAWb,KAAK,EACpB;4BACEI,YAAYS,WAAWT,UAAU;wBACnC;oBAEJ;gBACF,OAAO;oBACL,IAAI,CAACX,iBAAiB,GAAGyB;gBAC3B;gBAEA,OAAOJ;YACT,EAAE,OAAOK,KAAK;gBACZ,qEAAqE;gBACrE,sEAAsE;gBACtE,IAAIpB,gBAAgB;oBAClB,MAAMV,iBAAiB2B,GAAG,CAACnC,KAAKkB,eAAeC,KAAK,EAAE;wBACpDI,YAAYgB,KAAKC,GAAG,CAClBD,KAAKE,GAAG,CAACvB,eAAeK,UAAU,IAAI,GAAG,IACzC;oBAEJ;gBACF;gBAEA,qEAAqE;gBACrE,kDAAkD;gBAClD,IAAIN,UAAU;oBACZyB,QAAQC,KAAK,CAACL;oBACd,OAAO;gBACT;gBAEA,gEAAgE;gBAChE,MAAMA;YACR;QACF;IAEJ;AACF"}