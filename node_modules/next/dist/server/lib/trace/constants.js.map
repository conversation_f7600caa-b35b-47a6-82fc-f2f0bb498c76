{"version": 3, "sources": ["../../../../src/server/lib/trace/constants.ts"], "names": ["NextVanillaSpanAllowlist", "BaseServerSpan", "LoadComponentsSpan", "NextServerSpan", "NextNodeServerSpan", "StartServerSpan", "RenderSpan", "RouterSpan", "AppRenderSpan", "NodeSpan", "AppRouteRouteHandlersSpan", "ResolveMetadataSpan", "handleRequest", "run", "pipe", "getStaticHTML", "render", "renderToResponseWithComponents", "renderToResponse", "renderToHTML", "renderError", "renderErrorToResponse", "renderErrorToHTML", "render404", "loadDefaultErrorComponents", "loadComponents", "getRequestHandler", "getServer", "getServerRequestHandler", "createServer", "compression", "getBuildId", "generateStaticRoutes", "generateFsStaticRoutes", "generatePublicRoutes", "generateImageRoutes", "sendRenderResult", "proxyRequest", "run<PERSON><PERSON>", "renderHTML", "imageOptimizer", "getPagePath", "getRoutesManifest", "findPageComponents", "getFontManifest", "getServerComponentManifest", "route", "onProxyReq", "apiResolver", "internalFetch", "startServer", "getServerSideProps", "getStaticProps", "renderToString", "renderDocument", "createBodyResult", "renderToReadableStream", "getBodyResult", "fetch", "executeRoute", "<PERSON><PERSON><PERSON><PERSON>", "generateMetadata"], "mappings": "AAAA;;;;;EAKE,GAEF,4CAA4C;AAC5C,4BAA4B;;;;;;;;;;;;;;;;;;;;;;;;;IA6GfA,wBAAwB;eAAxBA;;IAaXC,cAAc;eAAdA;;IACAC,kBAAkB;eAAlBA;;IACAC,cAAc;eAAdA;;IACAC,kBAAkB;eAAlBA;;IACAC,eAAe;eAAfA;;IACAC,UAAU;eAAVA;;IACAC,UAAU;eAAVA;;IACAC,aAAa;eAAbA;;IACAC,QAAQ;eAARA;;IACAC,yBAAyB;eAAzBA;;IACAC,mBAAmB;eAAnBA;;;IAlIF;UAAKV,cAAc;IAAdA,eACHW,mBAAgB;IADbX,eAEHY,SAAM;IAFHZ,eAGHa,UAAO;IAHJb,eAIHc,mBAAgB;IAJbd,eAKHe,YAAS;IALNf,eAMHgB,oCAAiC;IAN9BhB,eAOHiB,sBAAmB;IAPhBjB,eAQHkB,kBAAe;IARZlB,eASHmB,iBAAc;IATXnB,eAUHoB,2BAAwB;IAVrBpB,eAWHqB,uBAAoB;IAXjBrB,eAYHsB,eAAY;GAZTtB,mBAAAA;IAeL;UAAKC,kBAAkB;IAAlBA,mBACHsB,gCAA6B;IAD1BtB,mBAEHuB,oBAAiB;GAFdvB,uBAAAA;IAKL;UAAKC,cAAc;IAAdA,eACHuB,uBAAoB;IADjBvB,eAEHwB,eAAY;IAFTxB,eAGHyB,6BAA0B;IAHvBzB,eAIH0B,kBAAe;GAJZ1B,mBAAAA;IAOL;UAAKC,kBAAkB;IAAlBA,mBACH0B,iBAAc;IADX1B,mBAEH2B,gBAAa;IAFV3B,mBAGH4B,0BAAuB;IAHpB5B,mBAIH6B,4BAAyB;IAJtB7B,mBAKH8B,0BAAuB;IALpB9B,mBAMH+B,yBAAsB;IANnB/B,mBAOHgC,sBAAmB;IAPhBhC,mBAQHiC,kBAAe;IARZjC,mBASHkC,YAAS;IATNlC,mBAUHY,YAAS;IAVNZ,mBAWHmC,gBAAa;IAXVnC,mBAYHoC,oBAAiB;IAZdpC,mBAaHqC,iBAAc;IAbXrC,mBAcHsC,uBAAoB;IAdjBtC,mBAeHuC,wBAAqB;IAflBvC,mBAgBHwC,qBAAkB;IAhBfxC,mBAiBHyC,gCAA6B;IAjB1BzC,mBAkBHsB,uBAAoB;IAlBjBtB,mBAmBHe,kBAAe;IAnBZf,mBAoBHgB,iBAAc;IApBXhB,mBAqBHkB,uBAAoB;IArBjBlB,mBAsBHmB,eAAY;IAtBTnB,mBAwBH,wDAAwD;IACxD0C,WAAAA;IAzBG1C,mBA0BH2C,gBAAAA;IA1BG3C,mBA2BH4C,iBAAAA;IA3BG5C,mBA4BH6C,mBAAAA;GA5BG7C,uBAAAA;IA+BL;UAAKC,eAAe;IAAfA,gBACH6C,iBAAc;GADX7C,oBAAAA;IAIL;UAAKC,UAAU;IAAVA,WACH6C,wBAAqB;IADlB7C,WAEH8C,oBAAiB;IAFd9C,WAGH+C,oBAAiB;IAHd/C,WAIHgD,oBAAiB;IAJdhD,WAKHiD,sBAAmB;GALhBjD,eAAAA;IAQL;UAAKE,aAAa;IAAbA,cACH6C,oBAAiB;IADd7C,cAEHgD,4BAAyB;IAFtBhD,cAGHiD,mBAAgB;IAHbjD,cAIHkD,WAAQ;GAJLlD,kBAAAA;IAOL;UAAKD,UAAU;IAAVA,WACHoD,kBAAe;GADZpD,eAAAA;IAIL;UAAKE,QAAQ;IAARA,SACHmD,gBAAa;GADVnD,aAAAA;IAIL;UAAKC,yBAAyB;IAAzBA,0BACHkD,gBAAa;GADVlD,8BAAAA;IAIL;UAAKC,mBAAmB;IAAnBA,oBACHkD,sBAAmB;GADhBlD,wBAAAA;AAkBE,MAAMX,2BAA2B;IA1GtB;IA8DK;IACJ;IAUT;IADQ;IAPC;IAgBJ;IAIA;IAIM;CA2BpB"}