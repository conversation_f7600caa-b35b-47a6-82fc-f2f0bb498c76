{"version": 3, "sources": ["../../../../src/server/lib/incremental-cache/file-system-cache.ts"], "names": ["FileSystemCache", "memoryCache", "tagsManifest", "constructor", "ctx", "fs", "flushToDisk", "serverDistDir", "appDir", "_appDir", "revalidatedTags", "maxMemoryCacheSize", "L<PERSON><PERSON><PERSON>", "max", "length", "value", "JSON", "kind", "stringify", "props", "Error", "data", "body", "html", "pageData", "tagsManifestPath", "path", "join", "loadTagsManifest", "parse", "readFileSync", "err", "version", "items", "revalidateTag", "tag", "revalidatedAt", "Date", "now", "mkdir", "dirname", "writeFile", "console", "warn", "get", "key", "tags", "softTags", "fetchCache", "process", "env", "NEXT_RUNTIME", "filePath", "getFsPath", "pathname", "fileData", "readFile", "mtime", "stat", "meta", "replace", "cacheEntry", "lastModified", "getTime", "headers", "status", "_", "isAppPath", "parsedData", "storedTags", "every", "includes", "set", "cacheTags", "<PERSON><PERSON><PERSON><PERSON>", "NEXT_CACHE_TAGS_HEADER", "split", "isStale", "some", "undefined", "combinedTags", "wasRevalidated", "htmlPath"], "mappings": ";;;;+BAwBA;;;eAAqBA;;;iEAnBA;6DACJ;2BACsB;;;;;;AAcvC,IAAIC;AACJ,IAAIC;AAEW,MAAMF;IAQnBG,YAAYC,GAA2B,CAAE;QACvC,IAAI,CAACC,EAAE,GAAGD,IAAIC,EAAE;QAChB,IAAI,CAACC,WAAW,GAAGF,IAAIE,WAAW;QAClC,IAAI,CAACC,aAAa,GAAGH,IAAIG,aAAa;QACtC,IAAI,CAACC,MAAM,GAAG,CAAC,CAACJ,IAAIK,OAAO;QAC3B,IAAI,CAACC,eAAe,GAAGN,IAAIM,eAAe;QAE1C,IAAIN,IAAIO,kBAAkB,IAAI,CAACV,aAAa;YAC1CA,cAAc,IAAIW,iBAAQ,CAAC;gBACzBC,KAAKT,IAAIO,kBAAkB;gBAC3BG,QAAO,EAAEC,KAAK,EAAE;wBAcSC;oBAbvB,IAAI,CAACD,OAAO;wBACV,OAAO;oBACT,OAAO,IAAIA,MAAME,IAAI,KAAK,YAAY;wBACpC,OAAOD,KAAKE,SAAS,CAACH,MAAMI,KAAK,EAAEL,MAAM;oBAC3C,OAAO,IAAIC,MAAME,IAAI,KAAK,SAAS;wBACjC,MAAM,IAAIG,MAAM;oBAClB,OAAO,IAAIL,MAAME,IAAI,KAAK,SAAS;wBACjC,OAAOD,KAAKE,SAAS,CAACH,MAAMM,IAAI,IAAI,IAAIP,MAAM;oBAChD,OAAO,IAAIC,MAAME,IAAI,KAAK,SAAS;wBACjC,OAAOF,MAAMO,IAAI,CAACR,MAAM;oBAC1B;oBACA,wCAAwC;oBACxC,OACEC,MAAMQ,IAAI,CAACT,MAAM,GAAIE,CAAAA,EAAAA,kBAAAA,KAAKE,SAAS,CAACH,MAAMS,QAAQ,sBAA7BR,gBAAgCF,MAAM,KAAI,CAAA;gBAEnE;YACF;QACF;QACA,IAAI,IAAI,CAACP,aAAa,IAAI,IAAI,CAACF,EAAE,EAAE;YACjC,IAAI,CAACoB,gBAAgB,GAAGC,aAAI,CAACC,IAAI,CAC/B,IAAI,CAACpB,aAAa,EAClB,MACA,SACA,eACA;YAEF,IAAI,CAACqB,gBAAgB;QACvB;IACF;IAEQA,mBAAmB;QACzB,IAAI,CAAC,IAAI,CAACH,gBAAgB,IAAI,CAAC,IAAI,CAACpB,EAAE,IAAIH,cAAc;QACxD,IAAI;YACFA,eAAec,KAAKa,KAAK,CACvB,IAAI,CAACxB,EAAE,CAACyB,YAAY,CAAC,IAAI,CAACL,gBAAgB,EAAE;QAEhD,EAAE,OAAOM,KAAU;YACjB7B,eAAe;gBAAE8B,SAAS;gBAAGC,OAAO,CAAC;YAAE;QACzC;IACF;IAEA,MAAaC,cAAcC,GAAW,EAAE;QACtC,kDAAkD;QAClD,wDAAwD;QACxD,2CAA2C;QAC3C,IAAI,CAACP,gBAAgB;QACrB,IAAI,CAAC1B,gBAAgB,CAAC,IAAI,CAACuB,gBAAgB,EAAE;YAC3C;QACF;QAEA,MAAMJ,OAAOnB,aAAa+B,KAAK,CAACE,IAAI,IAAI,CAAC;QACzCd,KAAKe,aAAa,GAAGC,KAAKC,GAAG;QAC7BpC,aAAa+B,KAAK,CAACE,IAAI,GAAGd;QAE1B,IAAI;YACF,MAAM,IAAI,CAAChB,EAAE,CAACkC,KAAK,CAACb,aAAI,CAACc,OAAO,CAAC,IAAI,CAACf,gBAAgB;YACtD,MAAM,IAAI,CAACpB,EAAE,CAACoC,SAAS,CACrB,IAAI,CAAChB,gBAAgB,EACrBT,KAAKE,SAAS,CAAChB,gBAAgB,CAAC;QAEpC,EAAE,OAAO6B,KAAU;YACjBW,QAAQC,IAAI,CAAC,mCAAmCZ;QAClD;IACF;IAEA,MAAaa,IACXC,GAAW,EACX,EACEC,IAAI,EACJC,QAAQ,EACRC,UAAU,EAKX,GAAG,CAAC,CAAC,EACN;YAiHI3B,aA4BQA;QA5IZ,IAAIA,OAAOpB,+BAAAA,YAAa2C,GAAG,CAACC;QAE5B,qCAAqC;QACrC,IAAI,CAACxB,QAAQ4B,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;YAChD,IAAI;gBACF,MAAM,EAAEC,QAAQ,EAAE,GAAG,MAAM,IAAI,CAACC,SAAS,CAAC;oBACxCC,UAAU,CAAC,EAAET,IAAI,KAAK,CAAC;oBACvBrC,QAAQ;gBACV;gBACA,MAAM+C,WAAW,MAAM,IAAI,CAAClD,EAAE,CAACmD,QAAQ,CAACJ;gBACxC,MAAM,EAAEK,KAAK,EAAE,GAAG,MAAM,IAAI,CAACpD,EAAE,CAACqD,IAAI,CAACN;gBAErC,MAAMO,OAAO3C,KAAKa,KAAK,CACrB,MAAM,IAAI,CAACxB,EAAE,CAACmD,QAAQ,CAACJ,SAASQ,OAAO,CAAC,WAAW,UAAU;gBAG/D,MAAMC,aAAgC;oBACpCC,cAAcL,MAAMM,OAAO;oBAC3BhD,OAAO;wBACLE,MAAM;wBACNK,MAAMiC;wBACNS,SAASL,KAAKK,OAAO;wBACrBC,QAAQN,KAAKM,MAAM;oBACrB;gBACF;gBACA,OAAOJ;YACT,EAAE,OAAOK,GAAG;YACV,oCAAoC;YACtC;YAEA,IAAI;gBACF,MAAM,EAAEd,QAAQ,EAAEe,SAAS,EAAE,GAAG,MAAM,IAAI,CAACd,SAAS,CAAC;oBACnDC,UAAUN,aAAaH,MAAM,CAAC,EAAEA,IAAI,KAAK,CAAC;oBAC1CG;gBACF;gBACA,MAAMO,WAAW,MAAM,IAAI,CAAClD,EAAE,CAACmD,QAAQ,CAACJ,UAAU;gBAClD,MAAM,EAAEK,KAAK,EAAE,GAAG,MAAM,IAAI,CAACpD,EAAE,CAACqD,IAAI,CAACN;gBAErC,IAAIJ,YAAY;wBAQV3B;oBAPJ,MAAMyC,eAAeL,MAAMM,OAAO;oBAClC,MAAMK,aAA+BpD,KAAKa,KAAK,CAAC0B;oBAChDlC,OAAO;wBACLyC;wBACA/C,OAAOqD;oBACT;oBAEA,IAAI/C,EAAAA,eAAAA,KAAKN,KAAK,qBAAVM,aAAYJ,IAAI,MAAK,SAAS;4BACbI,kBAAAA;wBAAnB,MAAMgD,cAAahD,eAAAA,KAAKN,KAAK,sBAAVM,mBAAAA,aAAYA,IAAI,qBAAhBA,iBAAkByB,IAAI;wBAEzC,iDAAiD;wBACjD,8CAA8C;wBAC9C,gCAAgC;wBAChC,IAAI,EAACA,wBAAAA,KAAMwB,KAAK,CAAC,CAACnC,MAAQkC,8BAAAA,WAAYE,QAAQ,CAACpC,QAAO;4BACpD,MAAM,IAAI,CAACqC,GAAG,CAAC3B,KAAKxB,KAAKN,KAAK,EAAE;gCAAE+B;4BAAK;wBACzC;oBACF;gBACF,OAAO;oBACL,MAAMtB,WAAW2C,YACb,MAAM,IAAI,CAAC9D,EAAE,CAACmD,QAAQ,CACpB,AACE,CAAA,MAAM,IAAI,CAACH,SAAS,CAAC;wBACnBC,UAAU,CAAC,EAAET,IAAI,IAAI,CAAC;wBACtBrC,QAAQ;oBACV,EAAC,EACD4C,QAAQ,EACV,UAEFpC,KAAKa,KAAK,CACR,MAAM,IAAI,CAACxB,EAAE,CAACmD,QAAQ,CACpB,AACE,CAAA,MAAM,IAAI,CAACH,SAAS,CAAC;wBACnBC,UAAU,CAAC,EAAET,IAAI,KAAK,CAAC;wBACvBrC,QAAQ;oBACV,EAAC,EACD4C,QAAQ,EACV;oBAIR,IAAIO,OAA2D,CAAC;oBAEhE,IAAIQ,WAAW;wBACb,IAAI;4BACFR,OAAO3C,KAAKa,KAAK,CACf,MAAM,IAAI,CAACxB,EAAE,CAACmD,QAAQ,CACpBJ,SAASQ,OAAO,CAAC,WAAW,UAC5B;wBAGN,EAAE,OAAM,CAAC;oBACX;oBAEAvC,OAAO;wBACLyC,cAAcL,MAAMM,OAAO;wBAC3BhD,OAAO;4BACLE,MAAM;4BACNM,MAAMgC;4BACN/B;4BACAwC,SAASL,KAAKK,OAAO;4BACrBC,QAAQN,KAAKM,MAAM;wBACrB;oBACF;gBACF;gBAEA,IAAI5C,MAAM;oBACRpB,+BAAAA,YAAauE,GAAG,CAAC3B,KAAKxB;gBACxB;YACF,EAAE,OAAO6C,GAAG;YACV,+BAA+B;YACjC;QACF;QAEA,IAAI7C,CAAAA,yBAAAA,cAAAA,KAAMN,KAAK,qBAAXM,YAAaJ,IAAI,MAAK,QAAQ;gBAEbI;YADnB,IAAIoD;YACJ,MAAMC,cAAarD,sBAAAA,KAAKN,KAAK,CAACiD,OAAO,qBAAlB3C,mBAAoB,CAACsD,iCAAsB,CAAC;YAE/D,IAAI,OAAOD,eAAe,UAAU;gBAClCD,YAAYC,WAAWE,KAAK,CAAC;YAC/B;YAEA,IAAIH,6BAAAA,UAAW3D,MAAM,EAAE;gBACrB,IAAI,CAACc,gBAAgB;gBAErB,MAAMiD,UAAUJ,UAAUK,IAAI,CAAC,CAAC3C;wBAE5BjC;oBADF,OACEA,CAAAA,iCAAAA,0BAAAA,aAAc+B,KAAK,CAACE,IAAI,qBAAxBjC,wBAA0BkC,aAAa,KACvClC,CAAAA,gCAAAA,aAAc+B,KAAK,CAACE,IAAI,CAACC,aAAa,KACnCf,CAAAA,CAAAA,wBAAAA,KAAMyC,YAAY,KAAIzB,KAAKC,GAAG,EAAC;gBAEtC;gBAEA,kDAAkD;gBAClD,uDAAuD;gBACvD,wDAAwD;gBACxD,IAAIuC,SAAS;oBACXxD,OAAO0D;gBACT;YACF;QACF;QAEA,IAAI1D,QAAQA,CAAAA,yBAAAA,eAAAA,KAAMN,KAAK,qBAAXM,aAAaJ,IAAI,MAAK,SAAS;YACzC,IAAI,CAACW,gBAAgB;YAErB,MAAMoD,eAAe;mBAAKlC,QAAQ,EAAE;mBAAOC,YAAY,EAAE;aAAE;YAE3D,MAAMkC,iBAAiBD,aAAaF,IAAI,CAAC,CAAC3C;oBAMtCjC;gBALF,IAAI,IAAI,CAACQ,eAAe,CAAC6D,QAAQ,CAACpC,MAAM;oBACtC,OAAO;gBACT;gBAEA,OACEjC,CAAAA,iCAAAA,0BAAAA,aAAc+B,KAAK,CAACE,IAAI,qBAAxBjC,wBAA0BkC,aAAa,KACvClC,CAAAA,gCAAAA,aAAc+B,KAAK,CAACE,IAAI,CAACC,aAAa,KACnCf,CAAAA,CAAAA,wBAAAA,KAAMyC,YAAY,KAAIzB,KAAKC,GAAG,EAAC;YAEtC;YACA,gDAAgD;YAChD,wCAAwC;YACxC,IAAI2C,gBAAgB;gBAClB5D,OAAO0D;YACT;QACF;QAEA,OAAO1D,QAAQ;IACjB;IAEA,MAAamD,IACX3B,GAAW,EACXxB,IAAgC,EAChCjB,GAEC,EACD;QACAH,+BAAAA,YAAauE,GAAG,CAAC3B,KAAK;YACpB9B,OAAOM;YACPyC,cAAczB,KAAKC,GAAG;QACxB;QACA,IAAI,CAAC,IAAI,CAAChC,WAAW,EAAE;QAEvB,IAAIe,CAAAA,wBAAAA,KAAMJ,IAAI,MAAK,SAAS;YAC1B,MAAM,EAAEmC,QAAQ,EAAE,GAAG,MAAM,IAAI,CAACC,SAAS,CAAC;gBACxCC,UAAU,CAAC,EAAET,IAAI,KAAK,CAAC;gBACvBrC,QAAQ;YACV;YACA,MAAM,IAAI,CAACH,EAAE,CAACkC,KAAK,CAACb,aAAI,CAACc,OAAO,CAACY;YACjC,MAAM,IAAI,CAAC/C,EAAE,CAACoC,SAAS,CAACW,UAAU/B,KAAKC,IAAI;YAC3C,MAAM,IAAI,CAACjB,EAAE,CAACoC,SAAS,CACrBW,SAASQ,OAAO,CAAC,WAAW,UAC5B5C,KAAKE,SAAS,CAAC;gBAAE8C,SAAS3C,KAAK2C,OAAO;gBAAEC,QAAQ5C,KAAK4C,MAAM;YAAC;YAE9D;QACF;QAEA,IAAI5C,CAAAA,wBAAAA,KAAMJ,IAAI,MAAK,QAAQ;YACzB,MAAMkD,YAAY,OAAO9C,KAAKG,QAAQ,KAAK;YAC3C,MAAM,EAAE4B,UAAU8B,QAAQ,EAAE,GAAG,MAAM,IAAI,CAAC7B,SAAS,CAAC;gBAClDC,UAAU,CAAC,EAAET,IAAI,KAAK,CAAC;gBACvBrC,QAAQ2D;YACV;YACA,MAAM,IAAI,CAAC9D,EAAE,CAACkC,KAAK,CAACb,aAAI,CAACc,OAAO,CAAC0C;YACjC,MAAM,IAAI,CAAC7E,EAAE,CAACoC,SAAS,CAACyC,UAAU7D,KAAKE,IAAI;YAE3C,MAAM,IAAI,CAAClB,EAAE,CAACoC,SAAS,CACrB,AACE,CAAA,MAAM,IAAI,CAACY,SAAS,CAAC;gBACnBC,UAAU,CAAC,EAAET,IAAI,CAAC,EAAEsB,YAAY,QAAQ,OAAO,CAAC;gBAChD3D,QAAQ2D;YACV,EAAC,EACDf,QAAQ,EACVe,YAAY9C,KAAKG,QAAQ,GAAGR,KAAKE,SAAS,CAACG,KAAKG,QAAQ;YAG1D,IAAIH,KAAK2C,OAAO,IAAI3C,KAAK4C,MAAM,EAAE;gBAC/B,MAAM,IAAI,CAAC5D,EAAE,CAACoC,SAAS,CACrByC,SAAStB,OAAO,CAAC,WAAW,UAC5B5C,KAAKE,SAAS,CAAC;oBACb8C,SAAS3C,KAAK2C,OAAO;oBACrBC,QAAQ5C,KAAK4C,MAAM;gBACrB;YAEJ;QACF,OAAO,IAAI5C,CAAAA,wBAAAA,KAAMJ,IAAI,MAAK,SAAS;YACjC,MAAM,EAAEmC,QAAQ,EAAE,GAAG,MAAM,IAAI,CAACC,SAAS,CAAC;gBACxCC,UAAUT;gBACVG,YAAY;YACd;YACA,MAAM,IAAI,CAAC3C,EAAE,CAACkC,KAAK,CAACb,aAAI,CAACc,OAAO,CAACY;YACjC,MAAM,IAAI,CAAC/C,EAAE,CAACoC,SAAS,CACrBW,UACApC,KAAKE,SAAS,CAAC;gBACb,GAAGG,IAAI;gBACPyB,MAAM1C,IAAI0C,IAAI;YAChB;QAEJ;IACF;IAEA,MAAcO,UAAU,EACtBC,QAAQ,EACR9C,MAAM,EACNwC,UAAU,EAKX,EAGE;QACD,IAAIA,YAAY;YACd,6DAA6D;YAC7D,iBAAiB;YACjB,OAAO;gBACLI,UAAU1B,aAAI,CAACC,IAAI,CACjB,IAAI,CAACpB,aAAa,EAClB,MACA,SACA,eACA+C;gBAEFa,WAAW;YACb;QACF;QACA,IAAIA,YAAY;QAChB,IAAIf,WAAW1B,aAAI,CAACC,IAAI,CAAC,IAAI,CAACpB,aAAa,EAAE,SAAS+C;QAEtD,IAAI,CAAC,IAAI,CAAC9C,MAAM,IAAIA,WAAW,OAC7B,OAAO;YACL4C;YACAe;QACF;QACF,IAAI;YACF,MAAM,IAAI,CAAC9D,EAAE,CAACmD,QAAQ,CAACJ;YACvB,OAAO;gBACLA;gBACAe;YACF;QACF,EAAE,OAAOpC,KAAK;YACZ,OAAO;gBACLqB,UAAU1B,aAAI,CAACC,IAAI,CAAC,IAAI,CAACpB,aAAa,EAAE,OAAO+C;gBAC/Ca,WAAW;YACb;QACF;IACF;AACF"}