{"version": 3, "sources": ["../../../src/server/lib/utils.ts"], "names": ["printAndExit", "getDebugPort", "getNodeOptionsWithoutInspect", "getPort", "RESTART_EXIT_CODE", "message", "code", "console", "log", "error", "process", "exit", "debugPortStr", "execArgv", "find", "localArg", "startsWith", "split", "env", "NODE_OPTIONS", "match", "parseInt", "NODE_INSPECT_RE", "replace", "args", "parsed", "PORT", "Number", "isNaN"], "mappings": ";;;;;;;;;;;;;;;;;;IAEgBA,YAAY;eAAZA;;IAUHC,YAAY;eAAZA;;IAcGC,4BAA4B;eAA5BA;;IAIAC,OAAO;eAAPA;;IAaHC,iBAAiB;eAAjBA;;;AAzCN,SAASJ,aAAaK,OAAe,EAAEC,OAAO,CAAC;IACpD,IAAIA,SAAS,GAAG;QACdC,QAAQC,GAAG,CAACH;IACd,OAAO;QACLE,QAAQE,KAAK,CAACJ;IAChB;IAEAK,QAAQC,IAAI,CAACL;AACf;AAEO,MAAML,eAAe;QAExBS,wBAOAA,iCAAAA,kCAAAA;IARF,MAAME,eACJF,EAAAA,yBAAAA,QAAQG,QAAQ,CACbC,IAAI,CACH,CAACC,WACCA,SAASC,UAAU,CAAC,gBACpBD,SAASC,UAAU,CAAC,sCAJ1BN,uBAMIO,KAAK,CAAC,IAAI,CAAC,EAAE,OACjBP,4BAAAA,QAAQQ,GAAG,CAACC,YAAY,sBAAxBT,mCAAAA,0BAA0BU,KAAK,sBAA/BV,kCAAAA,sCAAAA,2BAAkC,sDAAlCA,+BAAqE,CAAC,EAAE;IAC1E,OAAOE,eAAeS,SAAST,cAAc,MAAM;AACrD;AAEA,MAAMU,kBAAkB;AACjB,SAASpB;IACd,OAAO,AAACQ,CAAAA,QAAQQ,GAAG,CAACC,YAAY,IAAI,EAAC,EAAGI,OAAO,CAACD,iBAAiB;AACnE;AAEO,SAASnB,QAAQqB,IAA0B;IAChD,IAAI,OAAOA,IAAI,CAAC,SAAS,KAAK,UAAU;QACtC,OAAOA,IAAI,CAAC,SAAS;IACvB;IAEA,MAAMC,SAASf,QAAQQ,GAAG,CAACQ,IAAI,IAAIL,SAASX,QAAQQ,GAAG,CAACQ,IAAI,EAAE;IAC9D,IAAI,OAAOD,WAAW,YAAY,CAACE,OAAOC,KAAK,CAACH,SAAS;QACvD,OAAOA;IACT;IAEA,OAAO;AACT;AAEO,MAAMrB,oBAAoB"}