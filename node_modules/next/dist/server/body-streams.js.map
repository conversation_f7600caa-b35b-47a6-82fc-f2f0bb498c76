{"version": 3, "sources": ["../../src/server/body-streams.ts"], "names": ["requestToBodyStream", "getCloneableBody", "context", "KUint8Array", "stream", "ReadableStream", "start", "controller", "on", "chunk", "enqueue", "Uint8Array", "close", "err", "error", "replaceRequestBody", "base", "key", "v", "bind", "readable", "buffered", "endPromise", "Promise", "resolve", "reject", "catch", "finalize", "res", "cloneBodyStream", "input", "p1", "PassThrough", "p2", "push"], "mappings": ";;;;;;;;;;;;;;;IAIgBA,mBAAmB;eAAnBA;;IAoCAC,gBAAgB;eAAhBA;;;wBAtCY;AAErB,SAASD,oBACdE,OAAkD,EAClDC,WAA8B,EAC9BC,MAAgB;IAEhB,OAAO,IAAIF,QAAQG,cAAc,CAAC;QAChCC,OAAMC,UAAU;YACdH,OAAOI,EAAE,CAAC,QAAQ,CAACC,QACjBF,WAAWG,OAAO,CAAC,IAAIP,YAAY;uBAAI,IAAIQ,WAAWF;iBAAO;YAE/DL,OAAOI,EAAE,CAAC,OAAO,IAAMD,WAAWK,KAAK;YACvCR,OAAOI,EAAE,CAAC,SAAS,CAACK,MAAQN,WAAWO,KAAK,CAACD;QAC/C;IACF;AACF;AAEA,SAASE,mBACPC,IAAO,EACPZ,MAAgB;IAEhB,IAAK,MAAMa,OAAOb,OAAQ;QACxB,IAAIc,IAAId,MAAM,CAACa,IAAsB;QACrC,IAAI,OAAOC,MAAM,YAAY;YAC3BA,IAAIA,EAAEC,IAAI,CAACH;QACb;QACAA,IAAI,CAACC,IAAe,GAAGC;IACzB;IAEA,OAAOF;AACT;AAOO,SAASf,iBACdmB,QAAW;IAEX,IAAIC,WAA4B;IAEhC,MAAMC,aAAa,IAAIC,QACrB,CAACC,SAASC;QACRL,SAASZ,EAAE,CAAC,OAAOgB;QACnBJ,SAASZ,EAAE,CAAC,SAASiB;IACvB,GACAC,KAAK,CAAC,CAACZ;QACP,OAAO;YAAEA;QAAM;IACjB;IAEA,OAAO;QACL;;;;KAIC,GACD,MAAMa;YACJ,IAAIN,UAAU;gBACZ,MAAMO,MAAM,MAAMN;gBAElB,IAAIM,OAAO,OAAOA,QAAQ,YAAYA,IAAId,KAAK,EAAE;oBAC/C,MAAMc,IAAId,KAAK;gBACjB;gBACAC,mBAAmBK,UAAUC;gBAC7BA,WAAWD;YACb;QACF;QACA;;;KAGC,GACDS;YACE,MAAMC,QAAQT,YAAYD;YAC1B,MAAMW,KAAK,IAAIC,mBAAW;YAC1B,MAAMC,KAAK,IAAID,mBAAW;YAC1BF,MAAMtB,EAAE,CAAC,QAAQ,CAACC;gBAChBsB,GAAGG,IAAI,CAACzB;gBACRwB,GAAGC,IAAI,CAACzB;YACV;YACAqB,MAAMtB,EAAE,CAAC,OAAO;gBACduB,GAAGG,IAAI,CAAC;gBACRD,GAAGC,IAAI,CAAC;YACV;YACAb,WAAWY;YACX,OAAOF;QACT;IACF;AACF"}