{"version": 3, "sources": ["../../src/telemetry/post-payload.ts"], "names": ["_postPayload", "fetch", "require", "endpoint", "body", "signal", "AbortSignal", "timeout", "retry", "method", "JSON", "stringify", "headers", "then", "res", "ok", "err", "Error", "statusText", "response", "minTimeout", "retries", "factor", "catch"], "mappings": ";;;;+BAKgBA;;;eAAAA;;;mEALE;;;;;;AAClB,MAAM,EAAEC,KAAK,EAAE,GAAGC,QAAQ;AAInB,SAASF,aAAaG,QAAgB,EAAEC,IAAY,EAAEC,MAAY;IACvE,IAAI,CAACA,UAAU,aAAaC,aAAa;QACvCD,SAASC,YAAYC,OAAO,CAAC;IAC/B;IACA,OACEC,IAAAA,mBAAK,EACH,IACEP,MAAME,UAAU;YACdM,QAAQ;YACRL,MAAMM,KAAKC,SAAS,CAACP;YACrBQ,SAAS;gBAAE,gBAAgB;YAAmB;YAC9CP;QACF,GAAGQ,IAAI,CAAC,CAACC;YACP,IAAI,CAACA,IAAIC,EAAE,EAAE;gBACX,MAAMC,MAAM,IAAIC,MAAMH,IAAII,UAAU;gBAClCF,IAAYG,QAAQ,GAAGL;gBACzB,MAAME;YACR;QACF,IACF;QAAEI,YAAY;QAAKC,SAAS;QAAGC,QAAQ;IAAE,GAExCC,KAAK,CAAC;IACL,kDAAkD;IACpD,EACA,2BAA2B;KAC1BV,IAAI,CACH,KAAO,GACP,KAAO;AAGf"}