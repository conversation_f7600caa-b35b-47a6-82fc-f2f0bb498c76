{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-swc-loader.ts"], "names": ["pitch", "sw<PERSON><PERSON><PERSON><PERSON>", "raw", "loaderTransform", "parentTrace", "source", "inputSourceMap", "nextConfig", "filename", "resourcePath", "loaderOptions", "getOptions", "isServer", "rootDir", "pagesDir", "appDir", "hasReactRefresh", "jsConfig", "supportedBrowsers", "swcCacheDir", "hasServerComponents", "isServerLayer", "bundleTarget", "isPageFile", "startsWith", "relativeFilePathFromRoot", "path", "relative", "swcOptions", "getLoaderSWCOptions", "development", "mode", "modularizeImports", "optimizePackageImports", "experimental", "swcPlugins", "compilerOptions", "compiler", "optimizeServerReact", "isServerActionsEnabled", "serverActions", "programmaticOptions", "JSON", "stringify", "undefined", "sourceMaps", "sourceMap", "inlineSourcesContent", "sourceFileName", "jsc", "transform", "react", "Object", "prototype", "hasOwnProperty", "call", "swcSpan", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "then", "output", "eliminatedPackages", "pkg", "parse", "add", "code", "map", "EXCLUDED_PATHS", "callback", "async", "process", "versions", "pnp", "test", "loaders", "length", "loaderIndex", "isAbsolute", "isWasm", "loaderSpan", "currentTraceSpan", "addDependency", "r", "inputSource", "transformedSource", "outputSourceMap", "err"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA;;;;;;;;;;;;;;;;IA4HgBA,KAAK;eAALA;;IAuBhB,OAmBC;eAnBuBC;;IAsBXC,GAAG;eAAHA;;;qBAtKqB;yBACmB;8DACpB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBjC,eAAeC,gBAEbC,WAAgB,EAChBC,MAAe,EACfC,cAAoB;QAiCMC,0BACZA,2BAESA,2BAMGA;IAxC1B,wBAAwB;IACxB,MAAMC,WAAW,IAAI,CAACC,YAAY;IAElC,IAAIC,gBAAkC,IAAI,CAACC,UAAU,MAAM,CAAC;IAE5D,MAAM,EACJC,QAAQ,EACRC,OAAO,EACPC,QAAQ,EACRC,MAAM,EACNC,eAAe,EACfT,UAAU,EACVU,QAAQ,EACRC,iBAAiB,EACjBC,WAAW,EACXC,mBAAmB,EACnBC,aAAa,EACbC,YAAY,EACb,GAAGZ;IACJ,MAAMa,aAAaf,SAASgB,UAAU,CAACV;IACvC,MAAMW,2BAA2BC,aAAI,CAACC,QAAQ,CAACd,SAASL;IAExD,MAAMoB,aAAaC,IAAAA,4BAAmB,EAAC;QACrCf;QACAC;QACAP;QACAI;QACAW;QACAO,aAAa,IAAI,CAACC,IAAI,KAAK;QAC3Bf;QACAgB,iBAAiB,EAAEzB,8BAAAA,WAAYyB,iBAAiB;QAChDC,sBAAsB,EAAE1B,+BAAAA,2BAAAA,WAAY2B,YAAY,qBAAxB3B,yBAA0B0B,sBAAsB;QACxEE,UAAU,EAAE5B,+BAAAA,4BAAAA,WAAY2B,YAAY,qBAAxB3B,0BAA0B4B,UAAU;QAChDC,eAAe,EAAE7B,8BAAAA,WAAY8B,QAAQ;QACrCC,mBAAmB,EAAE/B,+BAAAA,4BAAAA,WAAY2B,YAAY,qBAAxB3B,0BAA0B+B,mBAAmB;QAClErB;QACAC;QACAC;QACAM;QACAL;QACAmB,sBAAsB,EAAEhC,+BAAAA,4BAAAA,WAAY2B,YAAY,qBAAxB3B,0BAA0BiC,aAAa;QAC/DnB;QACAC;IACF;IAEA,MAAMmB,sBAAsB;QAC1B,GAAGb,UAAU;QACbpB;QACAF,gBAAgBA,iBAAiBoC,KAAKC,SAAS,CAACrC,kBAAkBsC;QAElE,sEAAsE;QACtEC,YAAY,IAAI,CAACC,SAAS;QAC1BC,sBAAsB,IAAI,CAACD,SAAS;QAEpC,qEAAqE;QACrE,qEAAqE;QACrE,WAAW;QACXE,gBAAgBxC;IAClB;IAEA,IAAI,CAACiC,oBAAoBnC,cAAc,EAAE;QACvC,OAAOmC,oBAAoBnC,cAAc;IAC3C;IAEA,+BAA+B;IAC/B,IACE,IAAI,CAACyB,IAAI,IACTU,oBAAoBQ,GAAG,IACvBR,oBAAoBQ,GAAG,CAACC,SAAS,IACjCT,oBAAoBQ,GAAG,CAACC,SAAS,CAACC,KAAK,IACvC,CAACC,OAAOC,SAAS,CAACC,cAAc,CAACC,IAAI,CACnCd,oBAAoBQ,GAAG,CAACC,SAAS,CAACC,KAAK,EACvC,gBAEF;QACAV,oBAAoBQ,GAAG,CAACC,SAAS,CAACC,KAAK,CAACrB,WAAW,GACjD,IAAI,CAACC,IAAI,KAAK;IAClB;IAEA,MAAMyB,UAAUpD,YAAYqD,UAAU,CAAC;IACvC,OAAOD,QAAQE,YAAY,CAAC,IAC1BR,IAAAA,cAAS,EAAC7C,QAAeoC,qBAAqBkB,IAAI,CAAC,CAACC;YAClD,IAAIA,OAAOC,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,EAAE;gBACxD,KAAK,MAAMC,OAAOpB,KAAKqB,KAAK,CAACH,OAAOC,kBAAkB,EAAG;oBACvD,IAAI,CAACA,kBAAkB,CAACG,GAAG,CAACF;gBAC9B;YACF;YACA,OAAO;gBAACF,OAAOK,IAAI;gBAAEL,OAAOM,GAAG,GAAGxB,KAAKqB,KAAK,CAACH,OAAOM,GAAG,IAAItB;aAAU;QACvE;AAEJ;AAEA,MAAMuB,iBACJ;AAEK,SAASnE;IACd,MAAMoE,WAAW,IAAI,CAACC,KAAK;IACzB,CAAA;QACA,IACE,kDAAkD;QAClD,CAACC,QAAQC,QAAQ,CAACC,GAAG,IACrB,CAACL,eAAeM,IAAI,CAAC,IAAI,CAAChE,YAAY,KACtC,IAAI,CAACiE,OAAO,CAACC,MAAM,GAAG,MAAM,IAAI,CAACC,WAAW,IAC5CC,IAAAA,gBAAU,EAAC,IAAI,CAACpE,YAAY,KAC5B,CAAE,MAAMqE,IAAAA,WAAM,KACd;YACA,MAAMC,aAAa,IAAI,CAACC,gBAAgB,CAACvB,UAAU,CAAC;YACpD,IAAI,CAACwB,aAAa,CAAC,IAAI,CAACxE,YAAY;YACpC,OAAOsE,WAAWrB,YAAY,CAAC,IAC7BvD,gBAAgBoD,IAAI,CAAC,IAAI,EAAEwB;QAE/B;IACF,CAAA,IAAKpB,IAAI,CAAC,CAACuB;QACT,IAAIA,GAAG,OAAOd,SAAS,SAASc;QAChCd;IACF,GAAGA;AACL;AAEe,SAASnE,UAEtBkF,WAAmB,EACnB7E,cAAmB;IAEnB,MAAMyE,aAAa,IAAI,CAACC,gBAAgB,CAACvB,UAAU,CAAC;IACpD,MAAMW,WAAW,IAAI,CAACC,KAAK;IAC3BU,WACGrB,YAAY,CAAC,IACZvD,gBAAgBoD,IAAI,CAAC,IAAI,EAAEwB,YAAYI,aAAa7E,iBAErDqD,IAAI,CACH,CAAC,CAACyB,mBAAmBC,gBAAqB;QACxCjB,SAAS,MAAMgB,mBAAmBC,mBAAmB/E;IACvD,GACA,CAACgF;QACClB,SAASkB;IACX;AAEN;AAGO,MAAMpF,MAAM"}