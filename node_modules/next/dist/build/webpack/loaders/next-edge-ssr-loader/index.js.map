{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-edge-ssr-loader/index.ts"], "names": ["swapDistFolderWithEsmDistFolder", "path", "replace", "getRouteModuleOptions", "page", "options", "definition", "kind", "RouteKind", "PAGES", "normalizePagePath", "pathname", "bundlePath", "filename", "edgeSSRLoader", "dev", "buildId", "absolutePagePath", "absoluteAppPath", "absoluteDocumentPath", "absolute500Path", "absoluteErrorPath", "isServerComponent", "stringifiedConfig", "stringifiedConfigBase64", "appDirLoader", "appDirLoaderBase64", "pagesType", "sriEnabled", "incremental<PERSON>ache<PERSON>andlerPath", "preferredRegion", "middlewareConfig", "middlewareConfigBase64", "serverActionsBodySizeLimit", "getOptions", "JSON", "parse", "<PERSON><PERSON><PERSON>", "from", "toString", "isAppDir", "buildInfo", "getModuleBuildInfo", "_module", "nextEdgeSSR", "route", "stringifiedPagePath", "stringifyRequest", "stringifiedAppPath", "stringifiedErrorPath", "stringifiedDocumentPath", "stringified500Path", "pageModPath", "substring", "length", "WEBPACK_RESOURCE_QUERIES", "edgeSSREntry", "transformed", "stringify"], "mappings": ";;;;+BA2QA;;;eAAA;;;oCAtQmC;2BACM;kCACR;2BACP;mCACQ;AAsBlC;;;;;;;AAOA,GACA,SAASA,gCAAgCC,IAAY;IACnD,OAAOA,KAAKC,OAAO,CAAC,mBAAmB;AACzC;AAEA,SAASC,sBAAsBC,IAAY;IACzC,MAAMC,UAAoE;QACxEC,YAAY;YACVC,MAAMC,oBAAS,CAACC,KAAK;YACrBL,MAAMM,IAAAA,oCAAiB,EAACN;YACxBO,UAAUP;YACV,2CAA2C;YAC3CQ,YAAY;YACZC,UAAU;QACZ;IACF;IAEA,OAAOR;AACT;AAEA,MAAMS,gBACJ,SAASA;IACP,MAAM,EACJC,GAAG,EACHX,IAAI,EACJY,OAAO,EACPC,gBAAgB,EAChBC,eAAe,EACfC,oBAAoB,EACpBC,eAAe,EACfC,iBAAiB,EACjBC,iBAAiB,EACjBC,mBAAmBC,uBAAuB,EAC1CC,cAAcC,kBAAkB,EAChCC,SAAS,EACTC,UAAU,EACVC,2BAA2B,EAC3BC,eAAe,EACfC,kBAAkBC,sBAAsB,EACxCC,0BAA0B,EAC3B,GAAG,IAAI,CAACC,UAAU;IAEnB,MAAMH,mBAAqCI,KAAKC,KAAK,CACnDC,OAAOC,IAAI,CAACN,wBAAwB,UAAUO,QAAQ;IAGxD,MAAMhB,oBAAoBc,OAAOC,IAAI,CACnCd,2BAA2B,IAC3B,UACAe,QAAQ;IACV,MAAMd,eAAeY,OAAOC,IAAI,CAC9BZ,sBAAsB,IACtB,UACAa,QAAQ;IACV,MAAMC,WAAWb,cAAc;IAE/B,MAAMc,YAAYC,IAAAA,sCAAkB,EAAC,IAAI,CAACC,OAAO;IACjDF,UAAUG,WAAW,GAAG;QACtB,gIAAgI;QAChItB,mBAAmBA,sBAAsB;QACzClB,MAAMA;QACNoC;IACF;IACAC,UAAUI,KAAK,GAAG;QAChBzC;QACAa;QACAa;QACAC;IACF;IAEA,MAAMe,sBAAsBC,IAAAA,kCAAgB,EAAC,IAAI,EAAE9B;IACnD,MAAM+B,qBAAqBD,IAAAA,kCAAgB,EACzC,IAAI,EACJ/C,gCAAgCkB;IAElC,MAAM+B,uBAAuBF,IAAAA,kCAAgB,EAC3C,IAAI,EACJ/C,gCAAgCqB;IAElC,MAAM6B,0BAA0BH,IAAAA,kCAAgB,EAC9C,IAAI,EACJ/C,gCAAgCmB;IAElC,MAAMgC,qBAAqB/B,kBACvB2B,IAAAA,kCAAgB,EAAC,IAAI,EAAE3B,mBACvB;IAEJ,MAAMgC,cAAc,CAAC,EAAE3B,aAAa,EAAEqB,oBAAoBO,SAAS,CACjE,GACAP,oBAAoBQ,MAAM,GAAG,GAC7B,EAAEd,WAAW,CAAC,CAAC,EAAEe,mCAAwB,CAACC,YAAY,CAAC,CAAC,GAAG,GAAG,CAAC;IAEjE,MAAMC,cAAc,CAAC;;;;;;sBAMH,EAAEtB,KAAKuB,SAAS,CAAC/B,WAAW;IAC9C,EACEa,WACI,CAAC;;+BAEoB,EAAEL,KAAKuB,SAAS,CAACN,aAAa;;;;;IAKzD,CAAC,GACK,CAAC;2BACgB,EAAEF,wBAAwB;8BACvB,EAAEF,mBAAmB;oCACf,EAAEF,oBAAoB;yCACjB,EAAEG,qBAAqB;MAC1D,EACEE,qBACI,CAAC,iCAAiC,EAAEA,mBAAmB,CAAC,GACxD,GACL;;;;;;;;;;;aAWM,EAAEhB,KAAKuB,SAAS,CAACvD,sBAAsBC,OAAO;;;;;;;;;;;;aAY9C,EAAE+B,KAAKuB,SAAS,CAACvD,sBAAsB,YAAY;;;;;;;;;0BAStC,EAClBgD,qBACI,CAAC;;;aAGA,EAAEhB,KAAKuB,SAAS,CAACvD,sBAAsB,SAAS;;;;;;;OAOtD,CAAC,GACI,OACL,CAAC,CACH;;IAED,EACE0B,8BACI,CAAC,oCAAoC,EAAEM,KAAKuB,SAAS,CACnD7B,6BACA,CAAC,GACH,uCACL;;;;;;;8CAOyC,EAAEM,KAAKuB,SAAS,CAACtD,MAAM;;yCAE5B,EACnCwB,aACI,0DACA,YACL;;;;;WAKM,EAAEb,IAAI;YACL,EAAEoB,KAAKuB,SAAS,CAACtD,MAAM;;;;;;;iBAOlB,EAAE,CAAC,CAACoC,SAAS;;;;+BAIC,EAAElB,kBAAkB;6BACtB,EAAEA,kBAAkB;kCACf,EAAEA,kBAAkB,GAAG,EACnD,OAAOW,+BAA+B,cAClC,cACAE,KAAKuB,SAAS,CAACzB,4BACpB;;cAES,EAAEV,kBAAkB;eACnB,EAAEY,KAAKuB,SAAS,CAAC1C,SAAS;;;;;;;;;;;;;KAapC,CAAC;IAEF,OAAOyC;AACT;MACF,WAAe3C"}