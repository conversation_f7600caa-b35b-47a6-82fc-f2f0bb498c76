{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-barrel-loader.ts"], "names": ["barrelTransformMappingCache", "Map", "getBarrelMapping", "resourcePath", "swcCacheDir", "resolve", "fs", "has", "get", "transpileSource", "filename", "source", "isWildcard", "isTypeScript", "endsWith", "Promise", "res", "transform", "inputSourceMap", "undefined", "sourceFileName", "optimizeBarrelExports", "wildcard", "jsc", "parser", "syntax", "experimental", "cacheRoot", "then", "output", "code", "visited", "Set", "getMatches", "file", "add", "rej", "readFile", "err", "data", "toString", "matches", "match", "prefix", "exportList", "JSON", "parse", "slice", "wildcardExports", "matchAll", "map", "decl", "length", "all", "req", "targetPath", "path", "dirname", "replace", "targetMatches", "concat", "set", "NextBarrelLoader", "async", "cacheable", "names", "getOptions", "getResolve", "mainFields", "mapping", "clearDependencies", "callback", "stringify", "exportMap", "name", "filePath", "orig", "missedNames", "push", "join"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAoFC;;;;+BAoOD;;;eAAA;;;6DAhOiB;qBACS;;;;;;AAE1B,iFAAiF;AACjF,mFAAmF;AACnF,+DAA+D;AAC/D,kEAAkE;AAClE,MAAMA,8BAA8B,IAAIC;AASxC,eAAeC,iBACbC,YAAoB,EACpBC,WAAmB,EACnBC,OAA8D,EAC9DC,EAKC;IAED,IAAIN,4BAA4BO,GAAG,CAACJ,eAAe;QACjD,OAAOH,4BAA4BQ,GAAG,CAACL;IACzC;IAEA,6EAA6E;IAC7E,mDAAmD;IACnD,eAAeM,gBACbC,QAAgB,EAChBC,MAAc,EACdC,UAAmB;QAEnB,MAAMC,eAAeH,SAASI,QAAQ,CAAC,UAAUJ,SAASI,QAAQ,CAAC;QACnE,OAAO,IAAIC,QAAgB,CAACC,MAC1BC,IAAAA,cAAS,EAACN,QAAQ;gBAChBD;gBACAQ,gBAAgBC;gBAChBC,gBAAgBV;gBAChBW,uBAAuB;oBACrBC,UAAUV;gBACZ;gBACAW,KAAK;oBACHC,QAAQ;wBACNC,QAAQZ,eAAe,eAAe;wBACtC,CAACA,eAAe,QAAQ,MAAM,EAAE;oBAClC;oBACAa,cAAc;wBACZC,WAAWvB;oBACb;gBACF;YACF,GAAGwB,IAAI,CAAC,CAACC;gBACPb,IAAIa,OAAOC,IAAI;YACjB;IAEJ;IAEA,yCAAyC;IACzC,MAAMC,UAAU,IAAIC;IACpB,eAAeC,WAAWC,IAAY,EAAEtB,UAAmB;QACzD,IAAImB,QAAQxB,GAAG,CAAC2B,OAAO;YACrB,OAAO;QACT;QACAH,QAAQI,GAAG,CAACD;QAEZ,MAAMvB,SAAS,MAAM,IAAII,QAAgB,CAACC,KAAKoB;YAC7C9B,GAAG+B,QAAQ,CAACH,MAAM,CAACI,KAAKC;gBACtB,IAAID,OAAOC,SAASpB,WAAW;oBAC7BiB,IAAIE;gBACN,OAAO;oBACLtB,IAAIuB,KAAKC,QAAQ;gBACnB;YACF;QACF;QAEA,MAAMX,SAAS,MAAMpB,gBAAgByB,MAAMvB,QAAQC;QAEnD,MAAM6B,UAAUZ,OAAOa,KAAK,CAC1B;QAEF,IAAI,CAACD,SAAS;YACZ,OAAO;QACT;QAEA,MAAME,SAASF,OAAO,CAAC,EAAE;QACzB,IAAIG,aAAaC,KAAKC,KAAK,CAACL,OAAO,CAAC,EAAE,CAACM,KAAK,CAAC,GAAG,CAAC;QAKjD,MAAMC,kBAAkB;eACnBnB,OAAOoB,QAAQ,CAAC;SACpB,CAACC,GAAG,CAAC,CAACR,QAAUA,KAAK,CAAC,EAAE;QAEzB,uEAAuE;QACvE,sEAAsE;QACtE,eAAe;QACf,IAAI9B,YAAY;YACd,KAAK,MAAMuC,QAAQP,WAAY;gBAC7BO,IAAI,CAAC,EAAE,GAAGjB;gBACViB,IAAI,CAAC,EAAE,GAAGA,IAAI,CAAC,EAAE;YACnB;QACF;QAEA,6EAA6E;QAC7E,IAAIH,gBAAgBI,MAAM,EAAE;YAC1B,MAAMrC,QAAQsC,GAAG,CACfL,gBAAgBE,GAAG,CAAC,OAAOI;gBACzB,MAAMC,aAAa,MAAMlD,QACvBmD,aAAI,CAACC,OAAO,CAACvB,OACboB,IAAII,OAAO,CAAC,gDAAgD;gBAG9D,MAAMC,gBAAgB,MAAM1B,WAAWsB,YAAY;gBACnD,IAAII,eAAe;oBACjB,wBAAwB;oBACxBf,aAAaA,WAAWgB,MAAM,CAACD,cAAcf,UAAU;gBACzD;YACF;QAEJ;QAEA,OAAO;YACLD;YACAC;YACAI;QACF;IACF;IAEA,MAAMhC,MAAM,MAAMiB,WAAW9B,cAAc;IAC3CH,4BAA4B6D,GAAG,CAAC1D,cAAca;IAE9C,OAAOA;AACT;AAEA,MAAM8C,mBAAmB;IAMvB,IAAI,CAACC,KAAK;IACV,IAAI,CAACC,SAAS,CAAC;IAEf,MAAM,EAAEC,KAAK,EAAE7D,WAAW,EAAE,GAAG,IAAI,CAAC8D,UAAU;IAE9C,yEAAyE;IACzE,6EAA6E;IAC7E,MAAM7D,UAAU,IAAI,CAAC8D,UAAU,CAAC;QAC9BC,YAAY;YAAC;YAAU;SAAO;IAChC;IAEA,MAAMC,UAAU,MAAMnE,iBACpB,IAAI,CAACC,YAAY,EACjBC,aACAC,SACA,IAAI,CAACC,EAAE;IAGT,4EAA4E;IAC5E,yEAAyE;IACzE,6EAA6E;IAC7E,wBAAwB;IACxB,IAAI,CAACgE,iBAAiB;IAEtB,IAAI,CAACD,SAAS;QACZ,6FAA6F;QAC7F,2FAA2F;QAC3F,0FAA0F;QAC1F,+BAA+B;QAC/B,IAAI,CAACE,QAAQ,CAAC,MAAM,CAAC,cAAc,EAAE1B,KAAK2B,SAAS,CAAC,IAAI,CAACrE,YAAY,EAAE,CAAC;QACxE;IACF;IAEA,6EAA6E;IAC7E,MAAMwC,SAAS0B,QAAQ1B,MAAM;IAC7B,MAAMC,aAAayB,QAAQzB,UAAU;IACrC,MAAM6B,YAAY,IAAIxE;IACtB,KAAK,MAAM,CAACyE,MAAMC,UAAUC,KAAK,IAAIhC,WAAY;QAC/C6B,UAAUZ,GAAG,CAACa,MAAM;YAACC;YAAUC;SAAK;IACtC;IAEA,IAAI/C,SAASc;IACb,IAAIkC,cAAwB,EAAE;IAC9B,KAAK,MAAMH,QAAQT,MAAO;QACxB,sBAAsB;QACtB,IAAIQ,UAAUlE,GAAG,CAACmE,OAAO;YACvB,MAAMvB,OAAOsB,UAAUjE,GAAG,CAACkE;YAE3B,IAAIvB,IAAI,CAAC,EAAE,KAAK,KAAK;gBACnBtB,UAAU,CAAC,cAAc,EAAE6C,KAAK,MAAM,EAAE7B,KAAK2B,SAAS,CAACrB,IAAI,CAAC,EAAE,EAAE,CAAC;YACnE,OAAO,IAAIA,IAAI,CAAC,EAAE,KAAK,WAAW;gBAChCtB,UAAU,CAAC,sBAAsB,EAAE6C,KAAK,QAAQ,EAAE7B,KAAK2B,SAAS,CAC9DrB,IAAI,CAAC,EAAE,EACP,CAAC;YACL,OAAO,IAAIA,IAAI,CAAC,EAAE,KAAKuB,MAAM;gBAC3B7C,UAAU,CAAC,WAAW,EAAE6C,KAAK,QAAQ,EAAE7B,KAAK2B,SAAS,CAACrB,IAAI,CAAC,EAAE,EAAE,CAAC;YAClE,OAAO;gBACLtB,UAAU,CAAC,WAAW,EAAEsB,IAAI,CAAC,EAAE,CAAC,IAAI,EAAEuB,KAAK,QAAQ,EAAE7B,KAAK2B,SAAS,CACjErB,IAAI,CAAC,EAAE,EACP,CAAC;YACL;QACF,OAAO;YACL0B,YAAYC,IAAI,CAACJ;QACnB;IACF;IAEA,mCAAmC;IACnC,IAAIG,YAAYzB,MAAM,GAAG,GAAG;QAC1B,KAAK,MAAME,OAAOe,QAAQrB,eAAe,CAAE;YACzCnB,UAAU,CAAC,gBAAgB,EAAEgB,KAAK2B,SAAS,CACzClB,IAAII,OAAO,CAAC,mBAAmBmB,YAAYE,IAAI,CAAC,OAAO,cACvD,CAAC;QACL;IACF;IAEA,IAAI,CAACR,QAAQ,CAAC,MAAM1C;AACtB;MAEA,WAAeiC"}