{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/index.ts"], "names": ["WellKnownErrorsPlugin", "NAME", "apply", "compiler", "hooks", "compilation", "tap", "afterSeal", "tapPromise", "errors", "length", "Promise", "all", "map", "err", "i", "moduleError", "getModuleBuildError", "e", "console", "log"], "mappings": ";;;;+BAKaA;;;eAAAA;;;oCAHuB;AAEpC,MAAMC,OAAO;AACN,MAAMD;IACXE,MAAMC,QAA0B,EAAE;QAChCA,SAASC,KAAK,CAACC,WAAW,CAACC,GAAG,CAACL,MAAM,CAACI;YACpCA,YAAYD,KAAK,CAACG,SAAS,CAACC,UAAU,CAACP,MAAM;oBACvCI;gBAAJ,KAAIA,sBAAAA,YAAYI,MAAM,qBAAlBJ,oBAAoBK,MAAM,EAAE;oBAC9B,MAAMC,QAAQC,GAAG,CACfP,YAAYI,MAAM,CAACI,GAAG,CAAC,OAAOC,KAAKC;wBACjC,IAAI;4BACF,MAAMC,cAAc,MAAMC,IAAAA,uCAAmB,EAC3Cd,UACAE,aACAS;4BAEF,IAAIE,gBAAgB,OAAO;gCACzBX,YAAYI,MAAM,CAACM,EAAE,GAAGC;4BAC1B;wBACF,EAAE,OAAOE,GAAG;4BACVC,QAAQC,GAAG,CAACF;wBACd;oBACF;gBAEJ;YACF;QACF;IACF;AACF"}