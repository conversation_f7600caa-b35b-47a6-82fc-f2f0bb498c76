{"version": 3, "sources": ["../../src/cli/next-dev.ts"], "names": ["nextDev", "dir", "child", "config", "isTurboSession", "traceUploadUrl", "sessionStopHandled", "sessionStarted", "Date", "now", "handleSessionStop", "signal", "kill", "eventCliSessionStopped", "require", "loadConfig", "PHASE_DEVELOPMENT_SERVER", "telemetry", "traceGlobals", "get", "Telemetry", "distDir", "path", "join", "pagesDir", "appDir", "pagesResult", "findPagesDir", "record", "cliCommand", "turboFlag", "durationMilliseconds", "flushDetached", "_", "uploadTrace", "mode", "projectDir", "process", "stdout", "write", "exit", "on", "args", "console", "log", "getProjectDir", "env", "NEXT_PRIVATE_DEV_DIR", "fileExists", "FileType", "Directory", "printAndExit", "preflight", "skipOnReb<PERSON>", "getPackageVersion", "getDependencies", "Promise", "resolve", "sassVersion", "nodeSassVersion", "all", "cwd", "name", "Log", "warn", "dependencies", "devDependencies", "command", "getNpxCommand", "port", "getPort", "isPortIsReserved", "getReservedPortExplanation", "allowRetry", "undefined", "PORT", "host", "loadedEnvFiles", "loadEnvConfig", "expFeatureInfo", "onLoadUserConfig", "userConfig", "userNextConfigExperimental", "getEnabledExperimentalFeatures", "experimental", "sort", "a", "b", "length", "envInfo", "map", "f", "isExperimentalTestProxy", "devServerOptions", "isDev", "hostname", "TURBOPACK", "validateTurboNextConfig", "setGlobal", "startServerPath", "startServer", "options", "resolved", "defaultEnv", "initialEnv", "fork", "stdio", "NEXT_PRIVATE_WORKER", "NODE_EXTRA_CA_CERTS", "selfSignedCertificate", "rootCA", "msg", "nextWorkerReady", "send", "nextWorkerOptions", "nextServerReady", "code", "RESTART_EXIT_CODE", "runDevServer", "reboot", "certificate", "key", "cert", "createSelfSignedCertificate", "err", "error", "trace", "traceAsyncFn", "cleanup"], "mappings": ";;;;;+BAiVSA;;;eAAAA;;;QA/UF;uBAEkD;6DACpC;+BAES;2BACW;6DACxB;wBAEuB;yBACd;gEACiC;8BAC9B;4BACQ;+BACP;wBACc;oEAEpB;qBACkB;uBACpB;kCACkB;+BACnB;iCAId;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEP,IAAIC;AACJ,IAAIC;AACJ,IAAIC;AACJ,IAAIC,iBAAiB;AACrB,IAAIC;AACJ,IAAIC,qBAAqB;AACzB,IAAIC,iBAAiBC,KAAKC,GAAG;AAE7B,MAAMC,oBAAoB,OAAOC;IAC/B,IAAIT,OAAO;QACTA,MAAMU,IAAI,CAAC,AAACD,UAAkB;IAChC;IACA,IAAIL,oBAAoB;IACxBA,qBAAqB;IAErB,IAAI;QACF,MAAM,EAAEO,sBAAsB,EAAE,GAC9BC,QAAQ;QAEVX,SAASA,UAAW,MAAMY,IAAAA,eAAU,EAACC,mCAAwB,EAAEf;QAE/D,IAAIgB,YACF,AAACC,oBAAY,CAACC,GAAG,CAAC,gBAGlB,IAAIC,kBAAS,CAAC;YACZC,SAASC,aAAI,CAACC,IAAI,CAACtB,KAAKE,OAAOkB,OAAO;QACxC;QAEF,IAAIG,WAAoB,CAAC,CAACN,oBAAY,CAACC,GAAG,CAAC;QAC3C,IAAIM,SAAkB,CAAC,CAACP,oBAAY,CAACC,GAAG,CAAC;QAEzC,IACE,OAAOD,oBAAY,CAACC,GAAG,CAAC,gBAAgB,eACxC,OAAOD,oBAAY,CAACC,GAAG,CAAC,cAAc,aACtC;YACA,MAAMO,cAAcC,IAAAA,0BAAY,EAAC1B;YACjCwB,SAAS,CAAC,CAACC,YAAYD,MAAM;YAC7BD,WAAW,CAAC,CAACE,YAAYF,QAAQ;QACnC;QAEAP,UAAUW,MAAM,CACdf,uBAAuB;YACrBgB,YAAY;YACZC,WAAW1B;YACX2B,sBAAsBvB,KAAKC,GAAG,KAAKF;YACnCiB;YACAC;QACF,IACA;QAEFR,UAAUe,aAAa,CAAC,OAAO/B;IACjC,EAAE,OAAOgC,GAAG;IACV,6CAA6C;IAC7C,sBAAsB;IACxB;IAEA,IAAI5B,gBAAgB;QAClB6B,IAAAA,oBAAW,EAAC;YACV7B;YACA8B,MAAM;YACN/B;YACAgC,YAAYnC;YACZoB,SAASlB,OAAOkB,OAAO;QACzB;IACF;IAEA,yDAAyD;IACzD,iDAAiD;IACjDgB,QAAQC,MAAM,CAACC,KAAK,CAAC;IACrBF,QAAQC,MAAM,CAACC,KAAK,CAAC;IACrBF,QAAQG,IAAI,CAAC;AACf;AAEAH,QAAQI,EAAE,CAAC,UAAU,IAAM/B,kBAAkB;AAC7C2B,QAAQI,EAAE,CAAC,WAAW,IAAM/B,kBAAkB;AAE9C,MAAMV,UAAsB,OAAO0C;IACjC,IAAIA,IAAI,CAAC,SAAS,EAAE;QAClBC,QAAQC,GAAG,CAAC,CAAC;;;;;;;;;;;;;;;;IAgBb,CAAC;QACDP,QAAQG,IAAI,CAAC;IACf;IACAvC,MAAM4C,IAAAA,4BAAa,EAACR,QAAQS,GAAG,CAACC,oBAAoB,IAAIL,KAAKT,CAAC,CAAC,EAAE;IAEjE,4CAA4C;IAC5C,IAAI,CAAE,MAAMe,IAAAA,sBAAU,EAAC/C,KAAKgD,oBAAQ,CAACC,SAAS,GAAI;QAChDC,IAAAA,mBAAY,EAAC,CAAC,gDAAgD,EAAElD,IAAI,CAAC;IACvE;IAEA,eAAemD,UAAUC,YAAqB;QAC5C,MAAM,EAAEC,iBAAiB,EAAEC,eAAe,EAAE,GAAI,MAAMC,QAAQC,OAAO,CACnE3C,QAAQ;QAGV,MAAM,CAAC4C,aAAaC,gBAAgB,GAAG,MAAMH,QAAQI,GAAG,CAAC;YACvDN,kBAAkB;gBAAEO,KAAK5D;gBAAK6D,MAAM;YAAO;YAC3CR,kBAAkB;gBAAEO,KAAK5D;gBAAK6D,MAAM;YAAY;SACjD;QACD,IAAIJ,eAAeC,iBAAiB;YAClCI,KAAIC,IAAI,CACN,mHACE,iEACA;QAEN;QAEA,IAAI,CAACX,cAAc;YACjB,MAAM,EAAEY,YAAY,EAAEC,eAAe,EAAE,GAAG,MAAMX,gBAAgB;gBAC9DM,KAAK5D;YACP;YAEA,6GAA6G;YAC7G,IACEgE,YAAY,CAAC,aAAa,IACzBC,eAAe,CAAC,aAAa,IAC5BA,eAAe,CAAC,aAAa,KAAK,eACpC;gBACA,MAAMC,UAAUC,IAAAA,4BAAa,EAACnE;gBAC9B8D,KAAIC,IAAI,CACN,2GACE,6DACA,CAAC,6BAA6B,EAAEG,QAAQ,4GAA4G,CAAC;YAE3J;QACF;IACF;IAEA,MAAME,OAAOC,IAAAA,cAAO,EAAC5B;IAErB,IAAI6B,IAAAA,iCAAgB,EAACF,OAAO;QAC1BlB,IAAAA,mBAAY,EAACqB,IAAAA,2CAA0B,EAACH,OAAO;IACjD;IAEA,2EAA2E;IAC3E,MAAMI,aACJ/B,IAAI,CAAC,SAAS,KAAKgC,aAAarC,QAAQS,GAAG,CAAC6B,IAAI,KAAKD;IAEvD,8DAA8D;IAC9D,0DAA0D;IAC1D,MAAME,OAAOlC,IAAI,CAAC,aAAa;IAE/B,MAAM,EAAEmC,cAAc,EAAE,GAAGC,IAAAA,kBAAa,EAAC7E,KAAK,MAAM0C,SAAS;IAE7D,IAAIoC,iBAA2B,EAAE;IACjC5E,SAAS,MAAMY,IAAAA,eAAU,EAACC,mCAAwB,EAAEf,KAAK;QACvD+E,kBAAiBC,UAAU;YACzB,MAAMC,6BAA6BC,IAAAA,sCAA8B,EAC/DF,WAAWG,YAAY;YAEzBL,iBAAiBG,2BAA2BG,IAAI,CAC9C,CAACC,GAAGC,IAAMD,EAAEE,MAAM,GAAGD,EAAEC,MAAM;QAEjC;IACF;IAEA,iDAAiD;IACjD,qDAAqD;IACrD,+BAA+B;IAC/B,IAAIC,UAAoB,EAAE;IAC1B,IAAIZ,eAAeW,MAAM,GAAG,GAAG;QAC7BC,UAAUZ,eAAea,GAAG,CAAC,CAACC,IAAMA,EAAErE,IAAI;IAC5C;IAEA,MAAMsE,0BAA0BlD,IAAI,CAAC,4BAA4B;IAEjE,IAAIA,IAAI,CAAC,8BAA8B,EAAE;QACvCrC,iBAAiBqC,IAAI,CAAC,8BAA8B;IACtD;IAEA,MAAMmD,mBAAuC;QAC3C5F;QACAoE;QACAI;QACAqB,OAAO;QACPC,UAAUnB;QACVgB;QACAH;QACAV;IACF;IAEA,IAAIrC,IAAI,CAAC,UAAU,EAAE;QACnBL,QAAQS,GAAG,CAACkD,SAAS,GAAG;IAC1B;IAEA,IAAI3D,QAAQS,GAAG,CAACkD,SAAS,EAAE;QACzB5F,iBAAiB;QACjB,MAAM6F,IAAAA,yCAAuB,EAAC;YAC5B,GAAGJ,gBAAgB;YACnBC,OAAO;QACT;IACF;IAEA,MAAMzE,UAAUC,aAAI,CAACC,IAAI,CAACtB,KAAKE,OAAOkB,OAAO,IAAI;IACjD6E,IAAAA,iBAAS,EAAC,SAASlF,mCAAwB;IAC3CkF,IAAAA,iBAAS,EAAC,WAAW7E;IAErB,MAAM8E,kBAAkBrF,QAAQ2C,OAAO,CAAC;IACxC,eAAe2C,YAAYC,OAA2B;QACpD,OAAO,IAAI7C,QAAc,CAACC;YACxB,IAAI6C,WAAW;YACf,MAAMC,aAAcC,eAAU,IAAInE,QAAQS,GAAG;YAE7C5C,QAAQuG,IAAAA,mBAAI,EAACN,iBAAiB;gBAC5BO,OAAO;gBACP5D,KAAK;oBACH,GAAGyD,UAAU;oBACbP,WAAW3D,QAAQS,GAAG,CAACkD,SAAS;oBAChCW,qBAAqB;oBACrBC,qBAAqBP,QAAQQ,qBAAqB,GAC9CR,QAAQQ,qBAAqB,CAACC,MAAM,GACpCP,WAAWK,mBAAmB;gBACpC;YACF;YAEA1G,MAAMuC,EAAE,CAAC,WAAW,CAACsE;gBACnB,IAAIA,OAAO,OAAOA,QAAQ,UAAU;oBAClC,IAAIA,IAAIC,eAAe,EAAE;wBACvB9G,yBAAAA,MAAO+G,IAAI,CAAC;4BAAEC,mBAAmBb;wBAAQ;oBAC3C,OAAO,IAAIU,IAAII,eAAe,IAAI,CAACb,UAAU;wBAC3CA,WAAW;wBACX7C;oBACF;gBACF;YACF;YAEAvD,MAAMuC,EAAE,CAAC,QAAQ,OAAO2E,MAAMzG;gBAC5B,IAAIL,sBAAsBK,QAAQ;oBAChC;gBACF;gBACA,IAAIyG,SAASC,wBAAiB,EAAE;oBAC9B,OAAOjB,YAAYC;gBACrB;gBACA,MAAM3F,kBAAkBC;YAC1B;QACF;IACF;IAEA,MAAM2G,eAAe,OAAOC;QAC1B,IAAI;YACF,IAAI,CAAC,CAAC7E,IAAI,CAAC,uBAAuB,EAAE;gBAClCqB,KAAIC,IAAI,CACN;gBAGF,IAAIwD;gBAEJ,MAAMC,MAAM/E,IAAI,CAAC,2BAA2B;gBAC5C,MAAMgF,OAAOhF,IAAI,CAAC,4BAA4B;gBAC9C,MAAMoE,SAASpE,IAAI,CAAC,0BAA0B;gBAE9C,IAAI+E,OAAOC,MAAM;oBACfF,cAAc;wBACZC,KAAKnG,aAAI,CAACmC,OAAO,CAACgE;wBAClBC,MAAMpG,aAAI,CAACmC,OAAO,CAACiE;wBACnBZ,QAAQA,SAASxF,aAAI,CAACmC,OAAO,CAACqD,UAAUpC;oBAC1C;gBACF,OAAO;oBACL8C,cAAc,MAAMG,IAAAA,mCAA2B,EAAC/C;gBAClD;gBAEA,MAAMwB,YAAY;oBAChB,GAAGP,gBAAgB;oBACnBgB,uBAAuBW;gBACzB;YACF,OAAO;gBACL,MAAMpB,YAAYP;YACpB;YAEA,MAAMzC,UAAUmE;QAClB,EAAE,OAAOK,KAAK;YACZjF,QAAQkF,KAAK,CAACD;YACdvF,QAAQG,IAAI,CAAC;QACf;IACF;IAEA,MAAMsF,IAAAA,YAAK,EAAC,oBAAoBC,YAAY,CAAC,OAAO9F;QAClD,MAAMqF,aAAa;IACrB;AACF;AAEA,SAASU;IACP,IAAI,CAAC9H,OAAO;QACV;IACF;IAEAA,MAAMU,IAAI,CAAC;AACb;AAEAyB,QAAQI,EAAE,CAAC,QAAQuF;AACnB3F,QAAQI,EAAE,CAAC,UAAUuF;AACrB3F,QAAQI,EAAE,CAAC,WAAWuF"}