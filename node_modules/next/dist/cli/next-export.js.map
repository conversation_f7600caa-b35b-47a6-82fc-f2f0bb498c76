{"version": 3, "sources": ["../../src/cli/next-export.ts"], "names": ["nextExport", "args", "nextExportCliSpan", "trace", "console", "log", "cyan", "process", "exit", "dir", "getProjectDir", "_", "existsSync", "printAndExit", "options", "silent", "threads", "outdir", "resolve", "join", "hasOutdirFromCli", "Boolean", "isInvokedFromCli", "hasAppDir", "buildExport", "exportApp", "then", "stop", "catch", "err", "ExportError", "code", "Log", "error", "message"], "mappings": ";;;;;+BAyESA;;;eAAAA;;;sBAtEqB;oBACH;4BACN;gEACkB;6DAClB;uBACQ;uBAEP;+BACQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE9B,MAAMA,aAAyB,CAACC;IAC9B,MAAMC,oBAAoBC,IAAAA,YAAK,EAAC;IAChC,IAAIF,IAAI,CAAC,SAAS,EAAE;QAClBG,QAAQC,GAAG,CAAC,CAAC;;;;;;;;;;;;;;;;;kBAiBC,EAAEC,IAAAA,gBAAI,EAChB,gEACA;IACJ,CAAC;QACDC,QAAQC,IAAI,CAAC;IACf;IAEA,MAAMC,MAAMC,IAAAA,4BAAa,EAACT,KAAKU,CAAC,CAAC,EAAE;IAEnC,4CAA4C;IAC5C,IAAI,CAACC,IAAAA,cAAU,EAACH,MAAM;QACpBI,IAAAA,mBAAY,EAAC,CAAC,gDAAgD,EAAEJ,IAAI,CAAC;IACvE;IAEA,MAAMK,UAA4B;QAChCC,QAAQd,IAAI,CAAC,WAAW,IAAI;QAC5Be,SAASf,IAAI,CAAC,YAAY;QAC1BgB,QAAQhB,IAAI,CAAC,WAAW,GAAGiB,IAAAA,aAAO,EAACjB,IAAI,CAAC,WAAW,IAAIkB,IAAAA,UAAI,EAACV,KAAK;QACjEW,kBAAkBC,QAAQpB,IAAI,CAAC,WAAW;QAC1CqB,kBAAkB;QAClBC,WAAW;QACXC,aAAa;IACf;IAEAC,IAAAA,eAAS,EAAChB,KAAKK,SAASZ,mBACrBwB,IAAI,CAAC;QACJxB,kBAAkByB,IAAI;QACtBd,IAAAA,mBAAY,EAAC,CAAC,oCAAoC,EAAEC,QAAQG,MAAM,CAAC,CAAC,EAAE;IACxE,GACCW,KAAK,CAAC,CAACC;QACN3B,kBAAkByB,IAAI;QACtB,IAAIE,eAAeC,mBAAW,IAAID,IAAIE,IAAI,KAAK,qBAAqB;YAClEC,KAAIC,KAAK,CAACJ,IAAIK,OAAO;QACvB,OAAO;YACL9B,QAAQ6B,KAAK,CAACJ;QAChB;QACAtB,QAAQC,IAAI,CAAC;IACf;AACJ"}