{"version": 3, "sources": ["../../src/cli/next-info.ts"], "names": ["nextInfo", "fetch", "require", "dir", "process", "cwd", "getPackageVersion", "packageName", "version", "getNextConfig", "config", "loadConfig", "PHASE_INFO", "output", "getBinaryVersion", "binaryName", "childProcess", "execFileSync", "toString", "trim", "printHelp", "console", "log", "cyan", "printDefaultInfo", "installedRelease", "nextConfig", "os", "platform", "arch", "versions", "node", "res", "releases", "json", "newestRelease", "tag_name", "replace", "warn", "yellow", "bold", "e", "message", "runSharedDependencyCheck", "tools", "skipMessage", "getSupportedArchTriples", "currentPlatform", "spawn", "triples", "availableTools", "tool", "check", "sync", "bin", "checkArgs", "status", "push", "length", "messages", "result", "outputs", "triple", "triplePkgName", "platformArchABI", "resolved", "resolve", "proc", "args", "procPromise", "Promise", "stdout", "on", "data", "stderr", "c", "code", "join", "printVerbose", "fs", "tasks", "title", "scripts", "default", "isWsl", "ciInfo", "is<PERSON>ock<PERSON>", "isCI", "name", "report", "getReport", "header", "javascriptHeap", "sharedObjects", "commandLine", "host", "cpus", "networkInterfaces", "reportSummary", "JSON", "stringify", "platformArchTriples", "loadBindings", "bindings", "target", "getTargetTriple", "path", "fallbackBindingsDirectory", "nextPath", "dirname", "tryResolve", "pkgName", "fileExists", "existsSync", "loadError", "loadSuccess", "linux", "win32", "darwin", "task", "targetPlatform", "undefined", "taskScript", "taskResult"], "mappings": ";;;;;+BA+kBSA;;;eAAAA;;;2DA7kBM;sEACU;4BAEU;2BAKR;+DACJ;;;;;;AALvB,MAAM,EAAEC,KAAK,EAAE,GAAGC,QAAQ;AAO1B,MAAMC,MAAMC,QAAQC,GAAG;AAsCvB,SAASC,kBAAkBC,WAAmB;IAC5C,IAAI;QACF,OAAOL,QAAQ,CAAC,EAAEK,YAAY,aAAa,CAAC,EAAEC,OAAO;IACvD,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEA,eAAeC;IACb,MAAMC,SAAS,MAAMC,IAAAA,eAAU,EAACC,qBAAU,EAAET;IAE5C,OAAO;QACLU,QAAQH,OAAOG,MAAM,IAAI;IAC3B;AACF;AAEA;;;CAGC,GACD,SAASC,iBAAiBC,UAAkB;IAC1C,IAAI;QACF,OAAOC,sBAAY,CAChBC,YAAY,CAACF,YAAY;YAAC;SAAY,EACtCG,QAAQ,GACRC,IAAI;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEA,SAASC;IACPC,QAAQC,GAAG,CACT,CAAC;;;;;;;;;;;YAWO,EAAEC,IAAAA,gBAAI,EAAC,kDAAkD,CAAC;AAEtE;AAEA;;CAEC,GACD,eAAeC;IACb,MAAMC,mBAAmBnB,kBAAkB;IAC3C,MAAMoB,aAAa,MAAMjB;IAEzBY,QAAQC,GAAG,CAAC,CAAC;;YAEH,EAAEK,WAAE,CAACC,QAAQ,GAAG;QACpB,EAAED,WAAE,CAACE,IAAI,GAAG;WACT,EAAEF,WAAE,CAACnB,OAAO,GAAG;;QAElB,EAAEJ,QAAQ0B,QAAQ,CAACC,IAAI,CAAC;OACzB,EAAEjB,iBAAiB,OAAO;QACzB,EAAEA,iBAAiB,QAAQ;QAC3B,EAAEA,iBAAiB,QAAQ;;QAE3B,EAAEW,iBAAiB;sBACL,EAAEnB,kBAAkB,sBAAsB;SACvD,EAAEA,kBAAkB,SAAS;aACzB,EAAEA,kBAAkB,aAAa;cAChC,EAAEA,kBAAkB,cAAc;;UAEtC,EAAEoB,WAAWb,MAAM,CAAC;;AAE9B,CAAC;IAEC,IAAI;QACF,MAAMmB,MAAM,MAAM/B,MAChB;QAEF,MAAMgC,WAAW,MAAMD,IAAIE,IAAI;QAC/B,MAAMC,gBAAgBF,QAAQ,CAAC,EAAE,CAACG,QAAQ,CAACC,OAAO,CAAC,MAAM;QAEzD,IAAIZ,qBAAqBU,eAAe;YACtCd,QAAQiB,IAAI,CACV,CAAC,EAAEC,IAAAA,kBAAM,EACPC,IAAAA,gBAAI,EAAC,SACL,mDAAmD,EAAEf,iBAAiB,YAAY,EAAEU,cAAc;;qEAEvC,CAAC;QAElE;IACF,EAAE,OAAOM,GAAG;QACVpB,QAAQiB,IAAI,CACV,CAAC,EAAEC,IAAAA,kBAAM,EACPC,IAAAA,gBAAI,EAAC,SACL,oDAAoD,EACpD,AAACC,EAAYC,OAAO,CACrB;gBACS,EAAEjB,iBAAiB;;mEAEgC,CAAC;IAElE;AACF;AAEA;;;;;CAKC,GACD,eAAekB,yBACbC,KAA4E,EAC5EC,WAAmB;QAKHC;IAHhB,MAAMC,kBAAkBpB,WAAE,CAACC,QAAQ;IACnC,MAAMoB,QAAQ9C,QAAQ;IACtB,MAAM,EAAE4C,uBAAuB,EAAE,GAAG5C,QAAQ;IAC5C,MAAM+C,UAAUH,EAAAA,2CAAAA,yBAAyB,CAACC,gBAAgB,qBAA1CD,wCAA4C,CAACnB,WAAE,CAACE,IAAI,GAAG,KAAI,EAAE;IAC7E,mFAAmF;IAEnF,MAAMqB,iBAAiB,EAAE;IACzB,KAAK,MAAMC,QAAQP,MAAO;QACxB,IAAI;YACF,MAAMQ,QAAQJ,MAAMK,IAAI,CAACF,KAAKG,GAAG,EAAEH,KAAKI,SAAS;YACjD,IAAIH,MAAMI,MAAM,KAAK,GAAG;gBACtBN,eAAeO,IAAI,CAACN;YACtB;QACF,EAAE,OAAM;QACN,kCAAkC;QACpC;IACF;IAEA,IAAID,eAAeQ,MAAM,KAAK,GAAG;QAC/B,OAAO;YACLC,UAAUd;YACVe,QAAQ;QACV;IACF;IAEA,MAAMC,UAAyB,EAAE;IACjC,IAAID,SAA0B;IAE9B,KAAK,MAAME,UAAUb,QAAS;QAC5B,MAAMc,gBAAgB,CAAC,UAAU,EAAED,OAAOE,eAAe,CAAC,CAAC;QAC3D,IAAIC;QACJ,IAAI;YACFA,WAAW/D,QAAQgE,OAAO,CAACH;QAC7B,EAAE,OAAOtB,GAAG;YACV,OAAO;gBACLkB,UACE;gBACFC,QAAQ;YACV;QACF;QAEA,KAAK,MAAMT,QAAQD,eAAgB;YACjC,MAAMiB,OAAOnB,MAAMG,KAAKG,GAAG,EAAE;mBAAIH,KAAKiB,IAAI;gBAAEH;aAAS;YACrDJ,QAAQJ,IAAI,CAAC,CAAC,QAAQ,EAAEN,KAAKG,GAAG,CAAC,eAAe,CAAC;YACjD,yFAAyF;YACzF,MAAMe,cAAc,IAAIC,QAAQ,CAACJ;gBAC/BC,KAAKI,MAAM,CAACC,EAAE,CAAC,QAAQ,SAAUC,IAAY;oBAC3CZ,QAAQJ,IAAI,CAACgB;gBACf;gBACAN,KAAKO,MAAM,CAACF,EAAE,CAAC,QAAQ,SAAUC,IAAY;oBAC3CZ,QAAQJ,IAAI,CAACgB;gBACf;gBACAN,KAAKK,EAAE,CAAC,SAAS,CAACG,IAAWT,QAAQS;YACvC;YAEA,IAAIC,OAAO,MAAMP;YACjB,IAAIO,SAAS,GAAG;gBACdhB,SAAS;YACX;QACF;IACF;IAEA,OAAO;QACL/C,QAAQgD,QAAQgB,IAAI,CAAC;QACrBjB;IACF;AACF;AAEA;;CAEC,GACD,eAAekB;IACb,MAAMC,KAAK7E,QAAQ;IACnB,MAAM6C,kBAAkBpB,WAAE,CAACC,QAAQ;IAEnC,IACEmB,oBAAoB,WACpBA,oBAAoB,WACpBA,oBAAoB,UACpB;QACA1B,QAAQC,GAAG,CACT;QAEF;IACF;IAEA,wBAAwB;IACxB,MAAM0D,QAKD;QACH;YACEC,OAAO;YACPC,SAAS;gBACPC,SAAS;oBACP,+FAA+F;oBAC/F,2CAA2C;oBAC3C,MAAMC,QAAQlF,QAAQ;oBACtB,MAAMmF,SAASnF,QAAQ;oBACvB,MAAMoF,WAAWpF,QAAQ;oBAEzB,MAAMW,SAAS,CAAC;OACnB,EAAEuE,MAAM;UACL,EAAEE,WAAW;MACjB,EAAED,OAAOE,IAAI,GAAGF,OAAOG,IAAI,IAAI,YAAY,QAAQ;AACzD,CAAC;oBAES,OAAO;wBACL3E;wBACA+C,QAAQ;oBACV;gBACF;YACF;QACF;QACA;YACEqB,OAAO;YACPC,SAAS;gBACPC,SAAS;oBACP,MAAM1D,mBAAmBnB,kBAAkB;oBAC3C,MAAMoB,aAAa,MAAMjB;oBACzB,MAAMI,SAAS,CAAC;;UAEhB,EAAET,QAAQ0B,QAAQ,CAACC,IAAI,CAAC;SACzB,EAAEjB,iBAAiB,OAAO;UACzB,EAAEA,iBAAiB,QAAQ;UAC3B,EAAEA,iBAAiB,QAAQ;;UAE3B,EAAEW,iBAAiB;wBACL,EAAEnB,kBAAkB,sBAAsB;WACvD,EAAEA,kBAAkB,SAAS;eACzB,EAAEA,kBAAkB,aAAa;gBAChC,EAAEA,kBAAkB,cAAc;;YAEtC,EAAEoB,WAAWb,MAAM,CAAC;;AAEhC,CAAC;oBACS,OAAO;wBACLA;wBACA+C,QAAQ;oBACV;gBACF;YACF;QACF;QACA;YACEqB,OAAO;YACPC,SAAS;gBACPC,SAAS;wBACQ/E;oBAAf,MAAMqF,UAASrF,kBAAAA,QAAQqF,MAAM,qBAAdrF,gBAAgBsF,SAAS;oBAExC,IAAI,CAACD,QAAQ;wBACX,OAAO;4BACL9B,UAAU;4BACVC,QAAQ;wBACV;oBACF;oBAEA,MAAM,EAAE+B,MAAM,EAAEC,cAAc,EAAEC,aAAa,EAAE,GAC7CJ;oBACF,mEAAmE;oBAC5DE,+BAAAA,OAAQtF,GAAG;oBACXsF,+BAAAA,OAAQG,WAAW;oBACnBH,+BAAAA,OAAQI,IAAI;oBACZJ,+BAAAA,OAAQK,IAAI;oBACZL,+BAAAA,OAAQM,iBAAiB;oBAEhC,MAAMC,gBAAgB;wBACpBP;wBACAC;wBACAC;oBACF;oBAEA,OAAO;wBACLhF,QAAQsF,KAAKC,SAAS,CAACF,eAAe,MAAM;wBAC5CtC,QAAQ;oBACV;gBACF;YACF;QACF;QACA;YACEqB,OAAO;YACPC,SAAS;gBACPC,SAAS;wBAsBSkB;oBArBhB,MAAMxF,SAAS,EAAE;oBAEjB,gDAAgD;oBAChD,IAAI;wBACF,MAAM,EAAEyF,YAAY,EAAE,GAAGpG,QAAQ;wBACjC,MAAMqG,WAAW,MAAMD;wBACvB,qEAAqE;wBACrE,MAAME,SAASD,SAASE,eAAe;wBAEvC,uEAAuE;wBACvE,OAAO;4BACL5F,QAAQ,CAAC,oCAAoC,EAAE2F,OAAO,CAAC;4BACvD5C,QAAQ;wBACV;oBACF,EAAE,OAAOnB,GAAG;wBACV5B,OAAO4C,IAAI,CAAC,CAAC,uBAAuB,EAAE,AAAChB,EAAYC,OAAO,CAAC,CAAC;oBAC9D;oBAEA,MAAM,EACJ2D,mBAAmB,EACpB,GAAGnG,QAAQ;oBACZ,MAAM+C,WAAUoD,uCAAAA,mBAAmB,CAACtD,gBAAgB,qBAApCsD,oCAAsC,CAAC1E,WAAE,CAACE,IAAI,GAAG;oBAEjE,IAAI,CAACoB,WAAWA,QAAQS,MAAM,KAAK,GAAG;wBACpC,OAAO;4BACLC,UAAU,CAAC,4BAA4B,EAAEZ,gBAAgB,GAAG,EAAEpB,WAAE,CAACE,IAAI,GAAG,CAAC;4BACzE+B,QAAQ;wBACV;oBACF;oBAEA,qGAAqG;oBACrG,MAAM8C,OAAOxG,QAAQ;oBACrB,IAAIyG;oBACJ,IAAI;wBACF,MAAMC,WAAWF,KAAKG,OAAO,CAAC3G,QAAQgE,OAAO,CAAC;wBAC9CyC,4BAA4BD,KAAK7B,IAAI,CAAC+B,UAAU;oBAClD,EAAE,OAAOnE,GAAG;oBACV,mGAAmG;oBACrG;oBAEA,MAAMqE,aAAa,CAACC;wBAClB,IAAI;4BACF,MAAM9C,WAAW/D,QAAQgE,OAAO,CAAC6C;4BACjC,MAAMC,aAAajC,GAAGkC,UAAU,CAAChD;4BACjC,IAAIiD;4BACJ,IAAIC;4BAEJ,IAAI;gCACFA,cAAc,CAAC,CAACjH,QAAQ+D,UAAUwC,eAAe;4BACnD,EAAE,OAAOhE,GAAG;gCACVyE,YAAY,AAACzE,EAAYC,OAAO;4BAClC;4BAEA7B,OAAO4C,IAAI,CACT,CAAC,EAAEsD,QAAQ,SAAS,EAAEC,WAAW,gBAAgB,EAAEG,YAAY,CAAC;4BAElE,IAAID,WAAW;gCACbrG,OAAO4C,IAAI,CAAC,CAAC,EAAEsD,QAAQ,cAAc,EAAEG,aAAa,UAAU,CAAC;4BACjE;4BAEA,IAAIC,aAAa;gCACf,OAAO;4BACT;wBACF,EAAE,OAAO1E,GAAG;4BACV5B,OAAO4C,IAAI,CACT,CAAC,EAAEsD,QAAQ,iBAAiB,EAC1B,AAACtE,EAAYC,OAAO,IAAI,UACzB,CAAC;wBAEN;wBACA,OAAO;oBACT;oBAEA,KAAK,MAAMoB,UAAUb,QAAS;wBAC5B,MAAMc,gBAAgB,CAAC,UAAU,EAAED,OAAOE,eAAe,CAAC,CAAC;wBAC3D,yFAAyF;wBACzF,mHAAmH;wBACnH,IAAI8C,WAAW/C,gBAAgB;4BAC7B;wBACF;wBAEA,4CAA4C;wBAC5C,IAAI,CAAC4C,2BAA2B;4BAC9B;wBACF;wBAEAG,WAAWJ,KAAK7B,IAAI,CAAC8B,2BAA2B5C;oBAClD;oBAEA,OAAO;wBACLlD,QAAQA,OAAOgE,IAAI,CAAC;wBACpBjB,QAAQ;oBACV;gBACF;YACF;QACF;QACA;YACE,oFAAoF;YACpF,+EAA+E;YAC/E,mEAAmE;YACnEqB,OAAO;YACPC,SAAS;gBACPkC,OAAO;oBACL,MAAMvE,cACJ;oBAEF,OAAO,MAAMF,yBACX;wBACE;4BACEW,KAAK;4BACLC,WAAW;gCAAC;6BAAS;4BACrBa,MAAM;gCAAC;6BAAY;wBACrB;qBACD,EACDvB;gBAEJ;gBACAwE,OAAO;oBACL,MAAMxE,cAAc,CAAC;;;;;UAKrB,CAAC;oBAED,OAAO,MAAMF,yBACX;wBACE;4BACEW,KAAK;4BACLC,WAAW;gCAAC;6BAAW;4BACvBa,MAAM;gCAAC;6BAAW;wBACpB;qBACD,EACDvB;gBAEJ;gBACAyE,QAAQ;oBACN,MAAMzE,cACJ;oBAEF,OAAO,MAAMF,yBACX;wBACE;4BACEW,KAAK;4BACLC,WAAW;gCAAC;6BAAY;4BACxBa,MAAM;gCAAC;6BAAK;wBACd;wBACA;4BACEd,KAAK;4BACLC,WAAW,EAAE;4BACba,MAAM,EAAE;wBACV;qBACD,EACDvB;gBAEJ;YACF;QACF;KACD;IAED,4CAA4C;IAC5C,MAAM4C,SAGD,EAAE;IAEPpE,QAAQC,GAAG,CAAC;IACZ,KAAK,MAAMiG,QAAQvC,MAAO;QACxB,IAAIuC,KAAKC,cAAc,IAAID,KAAKC,cAAc,KAAKzE,iBAAiB;YAClE0C,OAAOhC,IAAI,CAAC;gBACVwB,OAAOsC,KAAKtC,KAAK;gBACjBrB,QAAQ;oBACND,UAAU8D;oBACV5G,QAAQ,CAAC,UAAU,EAAEc,WAAE,CAACC,QAAQ,GAAG,GAAG,EAAE2F,KAAKC,cAAc,CAAC,GAAG,EAC7DD,KAAKtC,KAAK,CACX,CAAC;oBACFrB,QAAQ;gBACV;YACF;YACA;QACF;QAEA,MAAM8D,aAAaH,KAAKrC,OAAO,CAACnC,gBAAgB,IAAIwE,KAAKrC,OAAO,CAACC,OAAO;QACxE,IAAIwC;QACJ,IAAI;YACFA,aAAa,MAAMD;QACrB,EAAE,OAAOjF,GAAG;YACVkF,aAAa;gBACXhE,UAAU,CAAC,8CAA8C,EACvD,AAAClB,EAAYC,OAAO,CACrB,CAAC;gBACFkB,QAAQ;YACV;QACF;QAEAvC,QAAQC,GAAG,CAAC,CAAC,EAAE,EAAEiG,KAAKtC,KAAK,CAAC,EAAE,EAAE0C,WAAW/D,MAAM,CAAC,CAAC;QACnD,IAAI+D,WAAWhE,QAAQ,EAAE;YACvBtC,QAAQC,GAAG,CAAC,CAAC,EAAE,EAAEqG,WAAWhE,QAAQ,CAAC,CAAC;QACxC;QAEA8B,OAAOhC,IAAI,CAAC;YACVwB,OAAOsC,KAAKtC,KAAK;YACjBrB,QAAQ+D;QACV;IACF;IAEAtG,QAAQC,GAAG,CAAC,CAAC,EAAE,EAAEkB,IAAAA,gBAAI,EAAC,gCAAgC,CAAC;IAEvDnB,QAAQC,GAAG,CAAC,CAAC,wDAAwD,CAAC;IACtE,KAAK,MAAM,EAAE2D,KAAK,EAAErB,MAAM,EAAE,IAAI6B,OAAQ;QACtCpE,QAAQC,GAAG,CAAC,CAAC,MAAM,EAAE2D,MAAM,CAAC;QAE5B,IAAIrB,OAAOD,QAAQ,EAAE;YACnBtC,QAAQC,GAAG,CAACsC,OAAOD,QAAQ;QAC7B;QAEA,IAAIC,OAAO/C,MAAM,EAAE;YACjBQ,QAAQC,GAAG,CAACsC,OAAO/C,MAAM;QAC3B;IACF;AACF;AAEA;;;;CAIC,GACD,MAAMb,WAAuB,OAAOoE;IAClC,IAAIA,IAAI,CAAC,SAAS,EAAE;QAClBhD;QACA;IACF;IAEA,IAAI,CAACgD,IAAI,CAAC,YAAY,EAAE;QACtB,MAAM5C;IACR,OAAO;QACL,MAAMsD;IACR;AACF"}