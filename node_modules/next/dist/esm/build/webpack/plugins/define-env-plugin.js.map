{"version": 3, "sources": ["../../../../src/build/webpack/plugins/define-env-plugin.ts"], "names": ["webpack", "needsExperimentalReact", "errorIfEnvConflicted", "config", "key", "isPrivateKey", "test", "hasNextRuntimeKey", "Error", "configFileName", "getDefineEnv", "allowedRevalidateHeaderKeys", "clientRouterFilters", "dev", "distDir", "fetchCacheKeyPrefix", "hasRewrites", "isClient", "isEdgeServer", "isNodeOrEdgeCompilation", "isNodeServer", "middlewareMatchers", "previewModeId", "__NEXT_DEFINE_ENV", "Object", "keys", "process", "env", "reduce", "prev", "startsWith", "JSON", "stringify", "acc", "EdgeRuntime", "NEXT_EDGE_RUNTIME_PROVIDER", "undefined", "experimental", "useDeploymentIdServerActions", "deploymentId", "manualClientBasePath", "clientRouterFilter", "staticFilter", "dynamicFilter", "optimisticClientCache", "middlewarePrefetch", "crossOrigin", "__NEXT_TEST_MODE", "trailingSlash", "devIndicators", "buildActivity", "buildActivityPosition", "reactStrictMode", "optimizeFonts", "optimizeCss", "nextScriptWorkers", "scrollRestoration", "deviceSizes", "images", "imageSizes", "path", "loader", "dangerouslyAllowSVG", "unoptimized", "domains", "remotePatterns", "output", "basePath", "strictNextHead", "i18n", "analyticsId", "skipMiddlewareUrlNormalize", "externalMiddlewareRewritesResolve", "skipTrailingSlashRedirect", "webVitalsAttribution", "length", "assetPrefix", "getDefineEnvPlugin", "options", "DefinePlugin"], "mappings": "AAEA,SAASA,OAAO,QAAQ,qCAAoC;AAC5D,SAASC,sBAAsB,QAAQ,wCAAuC;AAE9E,SAASC,qBAAqBC,MAA0B,EAAEC,GAAW;IACnE,MAAMC,eAAe,2BAA2BC,IAAI,CAACF;IACrD,MAAMG,oBAAoBH,QAAQ;IAElC,IAAIC,gBAAgBE,mBAAmB;QACrC,MAAM,IAAIC,MACR,CAAC,SAAS,EAAEJ,IAAI,iBAAiB,EAAED,OAAOM,cAAc,CAAC,qEAAqE,CAAC;IAEnI;AACF;AAyBA,OAAO,SAASC,aAAa,EAC3BC,2BAA2B,EAC3BC,mBAAmB,EACnBT,MAAM,EACNU,GAAG,EACHC,OAAO,EACPC,mBAAmB,EACnBC,WAAW,EACXC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,EACvBC,YAAY,EACZC,kBAAkB,EAClBC,aAAa,EACU;QAqHNnB,gBAKSA,iBAY0BA;IArIpD,OAAO;QACL,+CAA+C;QAC/CoB,mBAAmB;QAEnB,GAAGC,OAAOC,IAAI,CAACC,QAAQC,GAAG,EAAEC,MAAM,CAChC,CAACC,MAAiCzB;YAChC,IAAIA,IAAI0B,UAAU,CAAC,iBAAiB;gBAClCD,IAAI,CAAC,CAAC,YAAY,EAAEzB,IAAI,CAAC,CAAC,GAAG2B,KAAKC,SAAS,CAACN,QAAQC,GAAG,CAACvB,IAAI;YAC9D;YACA,OAAOyB;QACT,GACA,CAAC,EACF;QACD,GAAGL,OAAOC,IAAI,CAACtB,OAAOwB,GAAG,EAAEC,MAAM,CAAC,CAACK,KAAK7B;YACtCF,qBAAqBC,QAAQC;YAE7B,OAAO;gBACL,GAAG6B,GAAG;gBACN,CAAC,CAAC,YAAY,EAAE7B,IAAI,CAAC,CAAC,EAAE2B,KAAKC,SAAS,CAAC7B,OAAOwB,GAAG,CAACvB,IAAI;YACxD;QACF,GAAG,CAAC,EAAE;QACN,GAAI,CAACc,eACD,CAAC,IACD;YACEgB,aAAaH,KAAKC,SAAS,CACzB;;;;aAIC,GACDN,QAAQC,GAAG,CAACQ,0BAA0B,IAAI;QAE9C,CAAC;QACL,qBAAqBJ,KAAKC,SAAS,CAAC;QACpC,6DAA6D;QAC7D,wBAAwBD,KAAKC,SAAS,CAACnB,MAAM,gBAAgB;QAC7D,4BAA4BkB,KAAKC,SAAS,CACxCd,eAAe,SAASE,eAAe,WAAWgB;QAEpD,4BAA4BL,KAAKC,SAAS,CAAC;QAC3C,4CAA4CD,KAAKC,SAAS,CACxD7B,OAAOkC,YAAY,CAACC,4BAA4B;QAElD,kCAAkCP,KAAKC,SAAS,CAC9C7B,OAAOkC,YAAY,CAACE,YAAY;QAElC,6CACER,KAAKC,SAAS,CAACjB;QACjB,sCAAsCgB,KAAKC,SAAS,CAACV;QACrD,iDAAiDS,KAAKC,SAAS,CAC7DrB;QAEF,0CAA0CoB,KAAKC,SAAS,CACtDX,sBAAsB,EAAE;QAE1B,8CAA8CU,KAAKC,SAAS,CAC1D7B,OAAOkC,YAAY,CAACG,oBAAoB;QAE1C,mDAAmDT,KAAKC,SAAS,CAC/D7B,OAAOkC,YAAY,CAACI,kBAAkB;QAExC,6CAA6CV,KAAKC,SAAS,CACzDpB,uCAAAA,oBAAqB8B,YAAY;QAEnC,6CAA6CX,KAAKC,SAAS,CACzDpB,uCAAAA,oBAAqB+B,aAAa;QAEpC,8CAA8CZ,KAAKC,SAAS,CAC1D7B,OAAOkC,YAAY,CAACO,qBAAqB;QAE3C,0CAA0Cb,KAAKC,SAAS,CACtD7B,OAAOkC,YAAY,CAACQ,kBAAkB;QAExC,mCAAmCd,KAAKC,SAAS,CAAC7B,OAAO2C,WAAW;QACpE,mBAAmBf,KAAKC,SAAS,CAACf;QAClC,gCAAgCc,KAAKC,SAAS,CAC5CN,QAAQC,GAAG,CAACoB,gBAAgB;QAE9B,2FAA2F;QAC3F,GAAIlC,OAAQI,CAAAA,YAAYC,YAAW,IAC/B;YACE,+BAA+Ba,KAAKC,SAAS,CAAClB;QAChD,IACA,CAAC,CAAC;QACN,qCAAqCiB,KAAKC,SAAS,CAAC7B,OAAO6C,aAAa;QACxE,sCAAsCjB,KAAKC,SAAS,CAClD7B,OAAO8C,aAAa,CAACC,aAAa;QAEpC,+CAA+CnB,KAAKC,SAAS,CAC3D7B,OAAO8C,aAAa,CAACE,qBAAqB;QAE5C,kCAAkCpB,KAAKC,SAAS,CAC9C7B,OAAOiD,eAAe,KAAK,OAAO,QAAQjD,OAAOiD,eAAe;QAElE,sCAAsCrB,KAAKC,SAAS,CAClD,6EAA6E;QAC7E7B,OAAOiD,eAAe,KAAK,OAAO,OAAOjD,OAAOiD,eAAe;QAEjE,qCAAqCrB,KAAKC,SAAS,CACjD,CAACnB,OAAOV,OAAOkD,aAAa;QAE9B,mCAAmCtB,KAAKC,SAAS,CAC/C7B,OAAOkC,YAAY,CAACiB,WAAW,IAAI,CAACzC;QAEtC,qCAAqCkB,KAAKC,SAAS,CACjD7B,OAAOkC,YAAY,CAACkB,iBAAiB,IAAI,CAAC1C;QAE5C,yCAAyCkB,KAAKC,SAAS,CACrD7B,OAAOkC,YAAY,CAACmB,iBAAiB;QAEvC,iCAAiCzB,KAAKC,SAAS,CAAC;YAC9CyB,aAAatD,OAAOuD,MAAM,CAACD,WAAW;YACtCE,YAAYxD,OAAOuD,MAAM,CAACC,UAAU;YACpCC,MAAMzD,OAAOuD,MAAM,CAACE,IAAI;YACxBC,QAAQ1D,OAAOuD,MAAM,CAACG,MAAM;YAC5BC,qBAAqB3D,OAAOuD,MAAM,CAACI,mBAAmB;YACtDC,WAAW,EAAE5D,2BAAAA,iBAAAA,OAAQuD,MAAM,qBAAdvD,eAAgB4D,WAAW;YACxC,GAAIlD,MACA;gBACE,gEAAgE;gBAChEmD,SAAS7D,OAAOuD,MAAM,CAACM,OAAO;gBAC9BC,cAAc,GAAE9D,kBAAAA,OAAOuD,MAAM,qBAAbvD,gBAAe8D,cAAc;gBAC7CC,QAAQ/D,OAAO+D,MAAM;YACvB,IACA,CAAC,CAAC;QACR;QACA,sCAAsCnC,KAAKC,SAAS,CAAC7B,OAAOgE,QAAQ;QACpE,uCAAuCpC,KAAKC,SAAS,CACnD7B,OAAOkC,YAAY,CAAC+B,cAAc;QAEpC,mCAAmCrC,KAAKC,SAAS,CAAChB;QAClD,oCAAoCe,KAAKC,SAAS,CAAC7B,OAAO+D,MAAM;QAChE,mCAAmCnC,KAAKC,SAAS,CAAC,CAAC,CAAC7B,OAAOkE,IAAI;QAC/D,mCAAmCtC,KAAKC,SAAS,EAAC7B,eAAAA,OAAOkE,IAAI,qBAAXlE,aAAa6D,OAAO;QACtE,mCAAmCjC,KAAKC,SAAS,CAAC7B,OAAOmE,WAAW;QACpE,kDAAkDvC,KAAKC,SAAS,CAC9D7B,OAAOoE,0BAA0B;QAEnC,0DAA0DxC,KAAKC,SAAS,CACtE7B,OAAOkC,YAAY,CAACmC,iCAAiC;QAEvD,4CAA4CzC,KAAKC,SAAS,CACxD7B,OAAOsE,yBAAyB;QAElC,iDAAiD1C,KAAKC,SAAS,CAC7D7B,OAAOkC,YAAY,CAACqC,oBAAoB,IACtCvE,OAAOkC,YAAY,CAACqC,oBAAoB,CAACC,MAAM,GAAG;QAEtD,6CAA6C5C,KAAKC,SAAS,CACzD7B,OAAOkC,YAAY,CAACqC,oBAAoB;QAE1C,mCAAmC3C,KAAKC,SAAS,CAAC7B,OAAOyE,WAAW;QACpE,GAAIzD,0BACA;YACE,+DAA+D;YAC/D,2DAA2D;YAC3D,+CAA+C;YAC/C,iBAAiBY,KAAKC,SAAS,CAAC;QAClC,IACAI,SAAS;QACb,yBAAyBL,KAAKC,SAAS,CAAC;QACxC,GAAIZ,eACA;YACE,yCAAyCW,KAAKC,SAAS,CACrD/B,uBAAuBE;QAE3B,IACAiC,SAAS;IACf;AACF;AAEA,OAAO,SAASyC,mBAAmBC,OAA+B;IAChE,OAAO,IAAI9E,QAAQ+E,YAAY,CAACrE,aAAaoE;AAC/C"}