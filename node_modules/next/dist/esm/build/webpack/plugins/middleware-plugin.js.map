{"version": 3, "sources": ["../../../../src/build/webpack/plugins/middleware-plugin.ts"], "names": ["getNamedMiddlewareRegex", "getModuleBuildInfo", "getSortedRoutes", "webpack", "sources", "isMatch", "path", "EDGE_RUNTIME_WEBPACK", "EDGE_UNSUPPORTED_NODE_APIS", "MIDDLEWARE_BUILD_MANIFEST", "CLIENT_REFERENCE_MANIFEST", "MIDDLEWARE_MANIFEST", "MIDDLEWARE_REACT_LOADABLE_MANIFEST", "SUBRESOURCE_INTEGRITY_MANIFEST", "NEXT_FONT_MANIFEST", "SERVER_REFERENCE_MANIFEST", "PRERENDER_MANIFEST", "traceGlobals", "EVENT_BUILD_FEATURE_USAGE", "normalizeAppPath", "INSTRUMENTATION_HOOK_FILENAME", "NextBuildContext", "KNOWN_SAFE_DYNAMIC_PACKAGES", "require", "NAME", "isUsingIndirectEvalAndUsedByExports", "args", "moduleGraph", "runtime", "module", "usingIndirectEval", "wp", "exportsInfo", "getExportsInfo", "exportName", "getUsed", "UsageState", "Unused", "getEntryFiles", "entryFiles", "meta", "opts", "files", "edgeSSR", "isServerComponent", "push", "sriEnabled", "filter", "file", "startsWith", "endsWith", "map", "replace", "hasInstrumentationHook", "process", "env", "NODE_ENV", "getCreateAssets", "params", "compilation", "metadataByEntry", "assets", "middlewareManifest", "sortedMiddleware", "middleware", "functions", "version", "entrypoint", "entrypoints", "values", "metadata", "name", "get", "page", "edgeMiddleware", "edgeApiFunction", "matcherSource", "isAppDir", "catchAll", "namedRegex", "matchers", "regexp", "originalSource", "edgeFunctionDefinition", "getFiles", "wasm", "Array", "from", "wasmBindings", "filePath", "assetBindings", "regions", "Object", "keys", "RawSource", "JSON", "stringify", "buildWebpackError", "message", "loc", "entryModule", "parser", "error", "compiler", "WebpackError", "state", "current", "isInMiddlewareLayer", "layer", "isNodeJsModule", "moduleName", "builtinModules", "includes", "isDynamicCodeEvaluationAllowed", "fileName", "middlewareConfig", "rootDir", "some", "pkg", "sep", "unstable_allowDynamicGlobs", "buildUnsupportedApiError", "apiName", "rest", "start", "line", "registerUnsupportedApiHooks", "expression", "warnForUnsupportedApi", "node", "warnings", "hooks", "call", "for", "tap", "callMemberChain", "expression<PERSON>ember<PERSON>hain", "warnForUnsupportedProcessApi", "callee", "getCodeAnalyzer", "dev", "handleExpression", "optimize", "InnerGraph", "onUsage", "used", "buildInfo", "Set", "handleWrapExpression", "expr", "ConstDependency", "dependencies", "dep1", "range", "addPresentationalDependency", "dep2", "handleWrapWasmCompileExpression", "handleWrapWasmInstantiateExpression", "handleImport", "source", "value", "importLocByPath", "Map", "importedModule", "toString", "set", "sourcePosition", "identifier", "sourceContent", "skip", "undefined", "prefix", "new", "importCall", "import", "getExtractMetadata", "clear", "telemetry", "entryName", "entry", "entries", "route", "options", "entryDependency", "getResolvedModule", "modules", "addEntriesFromDependency", "dependency", "getModule", "add", "for<PERSON>ach", "includeDependencies", "entryMetadata", "preferredRegion", "ogImageGenerationCount", "resource", "hasOGImageGeneration", "test", "util", "getEntryRuntime", "id", "record", "eventName", "payload", "absolutePagePath", "config", "fileWithDynamicCode", "userRequest", "errors", "join", "nextEdgeSSR", "nextEdgeMiddleware", "nextEdgeApiFunction", "nextWasmMiddlewareBinding", "nextAssetMiddlewareBinding", "conn", "getOutgoingConnections", "featureName", "invocationCount", "MiddlewarePlugin", "constructor", "apply", "normalModuleFactory", "codeAnalyzer", "finishModules", "tapPromise", "processAssets", "stage", "Compilation", "PROCESS_ASSETS_STAGE_ADDITIONS", "SUPPORTED_NATIVE_MODULES", "supportedEdgePolyfills", "getEdgePolyfilledModules", "records", "mod", "handleWebpackExternalForEdgeRuntime", "request", "context", "contextInfo", "getResolve", "issuer<PERSON><PERSON>er", "has"], "mappings": "AAMA,SAASA,uBAAuB,QAAQ,+CAA8C;AACtF,SAASC,kBAAkB,QAAQ,mCAAkC;AACrE,SAASC,eAAe,QAAQ,mCAAkC;AAClE,SAASC,OAAO,EAAEC,OAAO,QAAQ,qCAAoC;AACrE,SAASC,OAAO,QAAQ,gCAA+B;AACvD,OAAOC,UAAU,OAAM;AACvB,SACEC,oBAAoB,EACpBC,0BAA0B,EAC1BC,yBAAyB,EACzBC,yBAAyB,EACzBC,mBAAmB,EACnBC,kCAAkC,EAClCC,8BAA8B,EAC9BC,kBAAkB,EAClBC,yBAAyB,EACzBC,kBAAkB,QACb,gCAA+B;AAGtC,SAASC,YAAY,QAAQ,wBAAuB;AACpD,SAASC,yBAAyB,QAAQ,4BAA2B;AACrE,SAASC,gBAAgB,QAAQ,6CAA4C;AAC7E,SAASC,6BAA6B,QAAQ,yBAAwB;AACtE,SAASC,gBAAgB,QAAQ,sBAAqB;AAEtD,MAAMC,8BACJC,QAAQ;AA4BV,MAAMC,OAAO;AAEb;;;;CAIC,GACD,SAASC,oCAAoCC,IAM5C;IACC,MAAM,EAAEC,WAAW,EAAEC,OAAO,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,EAAE,EAAE,GAAGL;IAChE,IAAI,OAAOI,sBAAsB,WAAW;QAC1C,OAAOA;IACT;IAEA,MAAME,cAAcL,YAAYM,cAAc,CAACJ;IAC/C,KAAK,MAAMK,cAAcJ,kBAAmB;QAC1C,IAAIE,YAAYG,OAAO,CAACD,YAAYN,aAAaG,GAAGK,UAAU,CAACC,MAAM,EAAE;YACrE,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA,SAASC,cACPC,UAAoB,EACpBC,IAAmB,EACnBC,IAEC;IAED,MAAMC,QAAkB,EAAE;IAC1B,IAAIF,KAAKG,OAAO,EAAE;QAChB,IAAIH,KAAKG,OAAO,CAACC,iBAAiB,EAAE;YAClCF,MAAMG,IAAI,CAAC,CAAC,OAAO,EAAE9B,0BAA0B,GAAG,CAAC;YACnD,IAAI0B,KAAKK,UAAU,EAAE;gBACnBJ,MAAMG,IAAI,CAAC,CAAC,OAAO,EAAEhC,+BAA+B,GAAG,CAAC;YAC1D;YACA6B,MAAMG,IAAI,IACLN,WACAQ,MAAM,CACL,CAACC,OACCA,KAAKC,UAAU,CAAC,WAAW,CAACD,KAAKE,QAAQ,CAAC,mBAE7CC,GAAG,CACF,CAACH,OACC,YACAA,KAAKI,OAAO,CAAC,OAAO,MAAM1C,4BAA4B;QAGhE;QAEAgC,MAAMG,IAAI,CACR,CAAC,OAAO,EAAEpC,0BAA0B,GAAG,CAAC,EACxC,CAAC,OAAO,EAAEG,mCAAmC,GAAG,CAAC;QAGnD8B,MAAMG,IAAI,CAAC,CAAC,OAAO,EAAE/B,mBAAmB,GAAG,CAAC;IAC9C;IAEA,IAAIO,iBAAkBgC,sBAAsB,EAAE;QAC5CX,MAAMG,IAAI,CAAC,CAAC,YAAY,EAAEzB,8BAA8B,GAAG,CAAC;IAC9D;IAEA,IAAIkC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzCd,MAAMG,IAAI,CAAC7B,mBAAmBoC,OAAO,CAAC,QAAQ;IAChD;IAEAV,MAAMG,IAAI,IACLN,WACAQ,MAAM,CAAC,CAACC,OAAS,CAACA,KAAKE,QAAQ,CAAC,mBAChCC,GAAG,CAAC,CAACH,OAAS,YAAYA;IAG/B,OAAON;AACT;AAEA,SAASe,gBAAgBC,MAMxB;IACC,MAAM,EAAEC,WAAW,EAAEC,eAAe,EAAEnB,IAAI,EAAE,GAAGiB;IAC/C,OAAO,CAACG;QACN,MAAMC,qBAAyC;YAC7CC,kBAAkB,EAAE;YACpBC,YAAY,CAAC;YACbC,WAAW,CAAC;YACZC,SAAS;QACX;QACA,KAAK,MAAMC,cAAcR,YAAYS,WAAW,CAACC,MAAM,GAAI;gBAQvDC,0BACAA,mBACAA,2BAKoBA,oBASLA;YAvBjB,IAAI,CAACH,WAAWI,IAAI,EAAE;gBACpB;YACF;YAEA,sDAAsD;YACtD,MAAMD,WAAWV,gBAAgBY,GAAG,CAACL,WAAWI,IAAI;YACpD,MAAME,OACJH,CAAAA,6BAAAA,2BAAAA,SAAUI,cAAc,qBAAxBJ,yBAA0BG,IAAI,MAC9BH,6BAAAA,oBAAAA,SAAU3B,OAAO,qBAAjB2B,kBAAmBG,IAAI,MACvBH,6BAAAA,4BAAAA,SAAUK,eAAe,qBAAzBL,0BAA2BG,IAAI;YACjC,IAAI,CAACA,MAAM;gBACT;YACF;YAEA,MAAMG,gBAAgBN,EAAAA,qBAAAA,SAAS3B,OAAO,qBAAhB2B,mBAAkBO,QAAQ,IAC5C1D,iBAAiBsD,QACjBA;YAEJ,MAAMK,WAAW,CAACR,SAAS3B,OAAO,IAAI,CAAC2B,SAASK,eAAe;YAE/D,MAAM,EAAEI,UAAU,EAAE,GAAG/E,wBAAwB4E,eAAe;gBAC5DE;YACF;YACA,MAAME,WAAWV,CAAAA,6BAAAA,4BAAAA,SAAUI,cAAc,qBAAxBJ,0BAA0BU,QAAQ,KAAI;gBACrD;oBACEC,QAAQF;oBACRG,gBAAgBT,SAAS,OAAOK,WAAW,YAAYF;gBACzD;aACD;YAED,MAAMO,yBAAiD;gBACrDzC,OAAOJ,cAAc6B,WAAWiB,QAAQ,IAAId,UAAU7B;gBACtD8B,MAAMJ,WAAWI,IAAI;gBACrBE,MAAMA;gBACNO;gBACAK,MAAMC,MAAMC,IAAI,CAACjB,SAASkB,YAAY,EAAE,CAAC,CAACjB,MAAMkB,SAAS,GAAM,CAAA;wBAC7DlB;wBACAkB;oBACF,CAAA;gBACA5B,QAAQyB,MAAMC,IAAI,CAACjB,SAASoB,aAAa,EAAE,CAAC,CAACnB,MAAMkB,SAAS,GAAM,CAAA;wBAChElB;wBACAkB;oBACF,CAAA;gBACA,GAAInB,SAASqB,OAAO,IAAI;oBAAEA,SAASrB,SAASqB,OAAO;gBAAC,CAAC;YACvD;YAEA,IAAIrB,SAASK,eAAe,IAAIL,SAAS3B,OAAO,EAAE;gBAChDmB,mBAAmBG,SAAS,CAACQ,KAAK,GAAGU;YACvC,OAAO;gBACLrB,mBAAmBE,UAAU,CAACS,KAAK,GAAGU;YACxC;QACF;QAEArB,mBAAmBC,gBAAgB,GAAG7D,gBACpC0F,OAAOC,IAAI,CAAC/B,mBAAmBE,UAAU;QAG3CH,MAAM,CAAClD,oBAAoB,GAAG,IAAIP,QAAQ0F,SAAS,CACjDC,KAAKC,SAAS,CAAClC,oBAAoB,MAAM;IAE7C;AACF;AAEA,SAASmC,kBAAkB,EACzBC,OAAO,EACPC,GAAG,EACHxC,WAAW,EACXyC,WAAW,EACXC,MAAM,EAOP;IACC,MAAMC,QAAQ,IAAI3C,YAAY4C,QAAQ,CAACpG,OAAO,CAACqG,YAAY,CAACN;IAC5DI,MAAM/B,IAAI,GAAG/C;IACb,MAAMK,SAASuE,gBAAeC,0BAAAA,OAAQI,KAAK,CAACC,OAAO;IACnD,IAAI7E,QAAQ;QACVyE,MAAMzE,MAAM,GAAGA;IACjB;IACAyE,MAAMH,GAAG,GAAGA;IACZ,OAAOG;AACT;AAEA,SAASK,oBAAoBN,MAA2C;QAC/DA;IAAP,OAAOA,EAAAA,uBAAAA,OAAOI,KAAK,CAAC5E,MAAM,qBAAnBwE,qBAAqBO,KAAK,MAAK;AACxC;AAEA,SAASC,eAAeC,UAAkB;IACxC,OAAOvF,QAAQ,UAAUwF,cAAc,CAACC,QAAQ,CAACF;AACnD;AAEA,SAASG,+BACPC,QAAgB,EAChBC,gBAAmC,EACnCC,OAAgB;IAEhB,wEAAwE;IACxE,2DAA2D;IAC3D,IACE9F,4BAA4B+F,IAAI,CAAC,CAACC,MAChCJ,SAASF,QAAQ,CAAC,CAAC,cAAc,EAAEM,IAAI,CAAC,CAAC,CAAClE,OAAO,CAAC,OAAO9C,KAAKiH,GAAG,KAEnE;QACA,OAAO;IACT;IAEA,MAAMhD,OAAO2C,SAAS9D,OAAO,CAACgE,WAAW,IAAI;IAC7C,OAAO/G,QAAQkE,MAAM4C,CAAAA,oCAAAA,iBAAkBK,0BAA0B,KAAI,EAAE;AACzE;AAEA,SAASC,yBAAyB,EAChCC,OAAO,EACPvB,GAAG,EACH,GAAGwB,MAMJ;IACC,OAAO1B,kBAAkB;QACvBC,SAAS,CAAC,uBAAuB,EAAEwB,QAAQ,UAAU,EAAEvB,IAAIyB,KAAK,CAACC,IAAI,CAAC;8DACZ,CAAC;QAC3D1B;QACA,GAAGwB,IAAI;IACT;AACF;AAEA,SAASG,4BACPzB,MAA2C,EAC3C1C,WAAgC;IAEhC,KAAK,MAAMoE,cAAcvH,2BAA4B;QACnD,MAAMwH,wBAAwB,CAACC;YAC7B,IAAI,CAACtB,oBAAoBN,SAAS;gBAChC;YACF;YACA1C,YAAYuE,QAAQ,CAACrF,IAAI,CACvB4E,yBAAyB;gBACvB9D;gBACA0C;gBACAqB,SAASK;gBACT,GAAGE,IAAI;YACT;YAEF,OAAO;QACT;QACA5B,OAAO8B,KAAK,CAACC,IAAI,CAACC,GAAG,CAACN,YAAYO,GAAG,CAAC9G,MAAMwG;QAC5C3B,OAAO8B,KAAK,CAACJ,UAAU,CAACM,GAAG,CAACN,YAAYO,GAAG,CAAC9G,MAAMwG;QAClD3B,OAAO8B,KAAK,CAACI,eAAe,CACzBF,GAAG,CAACN,YACJO,GAAG,CAAC9G,MAAMwG;QACb3B,OAAO8B,KAAK,CAACK,qBAAqB,CAC/BH,GAAG,CAACN,YACJO,GAAG,CAAC9G,MAAMwG;IACf;IAEA,MAAMS,+BAA+B,CAACR,MAAW,CAACS,OAAiB;QACjE,IAAI,CAAC/B,oBAAoBN,WAAWqC,WAAW,OAAO;YACpD;QACF;QACA/E,YAAYuE,QAAQ,CAACrF,IAAI,CACvB4E,yBAAyB;YACvB9D;YACA0C;YACAqB,SAAS,CAAC,QAAQ,EAAEgB,OAAO,CAAC;YAC5B,GAAGT,IAAI;QACT;QAEF,OAAO;IACT;IAEA5B,OAAO8B,KAAK,CAACI,eAAe,CACzBF,GAAG,CAAC,WACJC,GAAG,CAAC9G,MAAMiH;IACbpC,OAAO8B,KAAK,CAACK,qBAAqB,CAC/BH,GAAG,CAAC,WACJC,GAAG,CAAC9G,MAAMiH;AACf;AAEA,SAASE,gBAAgBjF,MAIxB;IACC,OAAO,CAAC2C;QACN,MAAM,EACJuC,GAAG,EACHrC,UAAU,EAAEpG,SAAS4B,EAAE,EAAE,EACzB4B,WAAW,EACZ,GAAGD;QACJ,MAAM,EAAEyE,KAAK,EAAE,GAAG9B;QAElB;;;;;KAKC,GACD,MAAMwC,mBAAmB;YACvB,IAAI,CAAClC,oBAAoBN,SAAS;gBAChC;YACF;YAEAtE,GAAG+G,QAAQ,CAACC,UAAU,CAACC,OAAO,CAAC3C,OAAOI,KAAK,EAAE,CAACwC,OAAO,IAAI;gBACvD,MAAMC,YAAYjJ,mBAAmBoG,OAAOI,KAAK,CAAC5E,MAAM;gBACxD,IAAIqH,UAAUpH,iBAAiB,KAAK,QAAQmH,SAAS,OAAO;oBAC1D;gBACF;gBAEA,IAAI,CAACC,UAAUpH,iBAAiB,IAAImH,SAAS,MAAM;oBACjDC,UAAUpH,iBAAiB,GAAGmH;oBAC9B;gBACF;gBAEAC,UAAUpH,iBAAiB,GAAG,IAAIqH,IAAI;uBACjC7D,MAAMC,IAAI,CAAC2D,UAAUpH,iBAAiB;uBACtCwD,MAAMC,IAAI,CAAC0D;iBACf;YACH;QACF;QAEA;;;;KAIC,GACD,MAAMG,uBAAuB,CAACC;YAC5B,IAAI,CAAC1C,oBAAoBN,SAAS;gBAChC;YACF;YAEA,MAAM,EAAEiD,eAAe,EAAE,GAAGvH,GAAGwH,YAAY;YAC3C,MAAMC,OAAO,IAAIF,gBACf,sCACAD,KAAKI,KAAK,CAAC,EAAE;YAEfD,KAAKrD,GAAG,GAAGkD,KAAKlD,GAAG;YACnBE,OAAOI,KAAK,CAAC5E,MAAM,CAAC6H,2BAA2B,CAACF;YAChD,MAAMG,OAAO,IAAIL,gBAAgB,MAAMD,KAAKI,KAAK,CAAC,EAAE;YACpDE,KAAKxD,GAAG,GAAGkD,KAAKlD,GAAG;YACnBE,OAAOI,KAAK,CAAC5E,MAAM,CAAC6H,2BAA2B,CAACC;YAEhDd;YACA,OAAO;QACT;QAEA;;;;KAIC,GACD,MAAMe,kCAAkC,CAACP;YACvC,IAAI,CAAC1C,oBAAoBN,SAAS;gBAChC;YACF;YAEA,MAAM,EAAEiD,eAAe,EAAE,GAAGvH,GAAGwH,YAAY;YAC3C,MAAMC,OAAO,IAAIF,gBACf,qDACAD,KAAKI,KAAK,CAAC,EAAE;YAEfD,KAAKrD,GAAG,GAAGkD,KAAKlD,GAAG;YACnBE,OAAOI,KAAK,CAAC5E,MAAM,CAAC6H,2BAA2B,CAACF;YAChD,MAAMG,OAAO,IAAIL,gBAAgB,MAAMD,KAAKI,KAAK,CAAC,EAAE;YACpDE,KAAKxD,GAAG,GAAGkD,KAAKlD,GAAG;YACnBE,OAAOI,KAAK,CAAC5E,MAAM,CAAC6H,2BAA2B,CAACC;YAEhDd;QACF;QAEA;;;;;;;;KAQC,GACD,MAAMgB,sCAAsC,CAACR;YAC3C,IAAI,CAAC1C,oBAAoBN,SAAS;gBAChC;YACF;YAEA,IAAIuC,KAAK;gBACP,MAAM,EAAEU,eAAe,EAAE,GAAGvH,GAAGwH,YAAY;gBAC3C,MAAMC,OAAO,IAAIF,gBACf,yDACAD,KAAKI,KAAK,CAAC,EAAE;gBAEfD,KAAKrD,GAAG,GAAGkD,KAAKlD,GAAG;gBACnBE,OAAOI,KAAK,CAAC5E,MAAM,CAAC6H,2BAA2B,CAACF;gBAChD,MAAMG,OAAO,IAAIL,gBAAgB,MAAMD,KAAKI,KAAK,CAAC,EAAE;gBACpDE,KAAKxD,GAAG,GAAGkD,KAAKlD,GAAG;gBACnBE,OAAOI,KAAK,CAAC5E,MAAM,CAAC6H,2BAA2B,CAACC;YAClD;QACF;QAEA;;KAEC,GACD,MAAMG,eAAe,CAAC7B;gBACeA;YAAnC,IAAItB,oBAAoBN,aAAW4B,eAAAA,KAAK8B,MAAM,qBAAX9B,aAAa+B,KAAK,MAAI/B,wBAAAA,KAAM9B,GAAG,GAAE;oBAO3C8B;gBANvB,MAAM,EAAEpG,MAAM,EAAEkI,MAAM,EAAE,GAAG1D,OAAOI,KAAK;gBACvC,MAAMyC,YAAYjJ,mBAAmB4B;gBACrC,IAAI,CAACqH,UAAUe,eAAe,EAAE;oBAC9Bf,UAAUe,eAAe,GAAG,IAAIC;gBAClC;gBAEA,MAAMC,kBAAiBlC,qBAAAA,KAAK8B,MAAM,CAACC,KAAK,qBAAjB/B,mBAAmBmC,QAAQ;gBAClDlB,UAAUe,eAAe,CAACI,GAAG,CAACF,gBAAgB;oBAC5CG,gBAAgB;wBACd,GAAGrC,KAAK9B,GAAG,CAACyB,KAAK;wBACjBmC,QAAQlI,OAAO0I,UAAU;oBAC3B;oBACAC,eAAeT,OAAOK,QAAQ;gBAChC;gBAEA,IAAI,CAACxB,OAAO/B,eAAesD,iBAAiB;oBAC1CxG,YAAYuE,QAAQ,CAACrF,IAAI,CACvBoD,kBAAkB;wBAChBC,SAAS,CAAC,6BAA6B,EAAEiE,eAAe,UAAU,EAAElC,KAAK9B,GAAG,CAACyB,KAAK,CAACC,IAAI,CAAC;wEAC9B,CAAC;wBAC3DlE;wBACA0C;wBACA,GAAG4B,IAAI;oBACT;gBAEJ;YACF;QACF;QAEA;;;KAGC,GACD,MAAMwC,OAAO,IAAO9D,oBAAoBN,UAAU,OAAOqE;QAEzD,KAAK,MAAMC,UAAU;YAAC;YAAI;SAAU,CAAE;YACpCxC,MAAMJ,UAAU,CAACM,GAAG,CAAC,CAAC,EAAEsC,OAAO,kBAAkB,CAAC,EAAErC,GAAG,CAAC9G,MAAMiJ;YAC9DtC,MAAMJ,UAAU,CAACM,GAAG,CAAC,CAAC,EAAEsC,OAAO,aAAa,CAAC,EAAErC,GAAG,CAAC9G,MAAMiJ;YACzDtC,MAAMC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEsC,OAAO,IAAI,CAAC,EAAErC,GAAG,CAAC9G,MAAM4H;YAC1CjB,MAAMC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEsC,OAAO,QAAQ,CAAC,EAAErC,GAAG,CAAC9G,MAAM4H;YAC9CjB,MAAMyC,GAAG,CAACvC,GAAG,CAAC,CAAC,EAAEsC,OAAO,QAAQ,CAAC,EAAErC,GAAG,CAAC9G,MAAM4H;YAC7CjB,MAAMC,IAAI,CACPC,GAAG,CAAC,CAAC,EAAEsC,OAAO,mBAAmB,CAAC,EAClCrC,GAAG,CAAC9G,MAAMoI;YACbzB,MAAMC,IAAI,CACPC,GAAG,CAAC,CAAC,EAAEsC,OAAO,uBAAuB,CAAC,EACtCrC,GAAG,CAAC9G,MAAMqI;QACf;QAEA1B,MAAM0C,UAAU,CAACvC,GAAG,CAAC9G,MAAMsI;QAC3B3B,MAAM2C,MAAM,CAACxC,GAAG,CAAC9G,MAAMsI;QAEvB,IAAI,CAAClB,KAAK;YACR,8EAA8E;YAC9Ed,4BAA4BzB,QAAQ1C;QACtC;IACF;AACF;AAEA,SAASoH,mBAAmBrH,MAK3B;IACC,MAAM,EAAEkF,GAAG,EAAEjF,WAAW,EAAEC,eAAe,EAAE2C,QAAQ,EAAE,GAAG7C;IACxD,MAAM,EAAEvD,SAAS4B,EAAE,EAAE,GAAGwE;IACxB,OAAO;QACL3C,gBAAgBoH,KAAK;QACrB,MAAMC,YAAmChK,aAAauD,GAAG,CAAC;QAE1D,KAAK,MAAM,CAAC0G,WAAWC,MAAM,IAAIxH,YAAYyH,OAAO,CAAE;gBAK5BD,qBAsBpBE;YA1BJ,IAAIF,MAAMG,OAAO,CAAC1J,OAAO,KAAKrB,sBAAsB;gBAElD;YACF;YACA,MAAMgL,mBAAkBJ,sBAAAA,MAAM5B,YAAY,qBAAlB4B,mBAAoB,CAAC,EAAE;YAC/C,MAAM,EAAE/D,OAAO,EAAEiE,KAAK,EAAE,GAAGpL,mBACzB0D,YAAYhC,WAAW,CAAC6J,iBAAiB,CAACD;YAG5C,MAAM,EAAE5J,WAAW,EAAE,GAAGgC;YACxB,MAAM8H,UAAU,IAAItC;YACpB,MAAMuC,2BAA2B,CAACC;gBAChC,MAAM9J,SAASF,YAAYiK,SAAS,CAACD;gBACrC,IAAI9J,QAAQ;oBACV4J,QAAQI,GAAG,CAAChK;gBACd;YACF;YAEAsJ,MAAM5B,YAAY,CAACuC,OAAO,CAACJ;YAC3BP,MAAMY,mBAAmB,CAACD,OAAO,CAACJ;YAElC,MAAMM,gBAA+B;gBACnCxG,cAAc,IAAI0E;gBAClBxE,eAAe,IAAIwE;YACrB;YAEA,IAAImB,0BAAAA,0BAAAA,MAAOlE,gBAAgB,qBAAvBkE,wBAAyB1F,OAAO,EAAE;gBACpCqG,cAAcrG,OAAO,GAAG0F,MAAMlE,gBAAgB,CAACxB,OAAO;YACxD;YAEA,IAAI0F,yBAAAA,MAAOY,eAAe,EAAE;gBAC1B,MAAMA,kBAAkBZ,MAAMY,eAAe;gBAC7CD,cAAcrG,OAAO,GACnB,8DAA8D;gBAC9D,OAAOsG,oBAAoB,WACvB;oBAACA;iBAAgB,GACjBA;YACR;YAEA,IAAIC,yBAAyB;YAE7B,KAAK,MAAMrK,UAAU4J,QAAS;gBAC5B,MAAMvC,YAAYjJ,mBAAmB4B;gBAErC;;SAEC,GACD,IAAI,CAAC+G,KAAK;oBACR,MAAMuD,WAAWtK,OAAOsK,QAAQ;oBAChC,MAAMC,uBACJD,YACA,wKAAwKE,IAAI,CAC1KF;oBAGJ,IAAIC,sBAAsB;wBACxBF;oBACF;gBACF;gBAEA;;;;SAIC,GACD,IACE,CAACtD,OACDM,UAAUpH,iBAAiB,IAC3BL,oCAAoC;oBAClCI;oBACAF;oBACAC,SAASG,GAAGuK,IAAI,CAAC1K,OAAO,CAAC2K,eAAe,CAAC5I,aAAauH;oBACtDpJ,mBAAmBoH,UAAUpH,iBAAiB;oBAC9CC;gBACF,IACA;wBAKIsJ;oBAJJ,MAAMmB,KAAK3K,OAAO0I,UAAU;oBAC5B,IAAI,uDAAuD8B,IAAI,CAACG,KAAK;wBACnE;oBACF;oBACA,IAAInB,0BAAAA,2BAAAA,MAAOlE,gBAAgB,qBAAvBkE,yBAAyB7D,0BAA0B,EAAE;wBACvDyD,6BAAAA,UAAWwB,MAAM,CAAC;4BAChBC,WAAW;4BACXC,SAAS;gCACP3J,IAAI,EAAEqI,yBAAAA,MAAOuB,gBAAgB,CAACxJ,OAAO,CAACgE,WAAW,IAAI;gCACrDyF,MAAM,EAAExB,yBAAAA,MAAOlE,gBAAgB;gCAC/B2F,qBAAqBjL,OAAOkL,WAAW,CAAC3J,OAAO,CAC7CgE,WAAW,IACX;4BAEJ;wBACF;oBACF;oBACA,IACE,CAACH,+BACCpF,OAAOkL,WAAW,EAClB1B,yBAAAA,MAAOlE,gBAAgB,EACvBC,UAEF;wBACAzD,YAAYqJ,MAAM,CAACnK,IAAI,CACrBoD,kBAAkB;4BAChBC,SAAS,CAAC,0GAA0G,EAClH,OAAOgD,UAAUpH,iBAAiB,KAAK,YACnC,CAAC,UAAU,EAAEwD,MAAMC,IAAI,CAAC2D,UAAUpH,iBAAiB,EAAEmL,IAAI,CACvD,MACA,CAAC,GACH,GACL,2EAA2E,CAAC;4BAC7E7G,aAAavE;4BACb8B;wBACF;oBAEJ;gBACF;gBAEA;;;SAGC,GACD,IAAIuF,6BAAAA,UAAWgE,WAAW,EAAE;oBAC1BlB,cAAcrJ,OAAO,GAAGuG,UAAUgE,WAAW;gBAC/C,OAAO,IAAIhE,6BAAAA,UAAWiE,kBAAkB,EAAE;oBACxCnB,cAActH,cAAc,GAAGwE,UAAUiE,kBAAkB;gBAC7D,OAAO,IAAIjE,6BAAAA,UAAWkE,mBAAmB,EAAE;oBACzCpB,cAAcrH,eAAe,GAAGuE,UAAUkE,mBAAmB;gBAC/D;gBAEA;;;SAGC,GACD,IAAIlE,6BAAAA,UAAWmE,yBAAyB,EAAE;oBACxCrB,cAAcxG,YAAY,CAAC6E,GAAG,CAC5BnB,UAAUmE,yBAAyB,CAAC9I,IAAI,EACxC2E,UAAUmE,yBAAyB,CAAC5H,QAAQ;gBAEhD;gBAEA,IAAIyD,6BAAAA,UAAWoE,0BAA0B,EAAE;oBACzCtB,cAActG,aAAa,CAAC2E,GAAG,CAC7BnB,UAAUoE,0BAA0B,CAAC/I,IAAI,EACzC2E,UAAUoE,0BAA0B,CAAC7H,QAAQ;gBAEjD;gBAEA;;;SAGC,GACD,KAAK,MAAM8H,QAAQ5L,YAAY6L,sBAAsB,CAAC3L,QAAS;oBAC7D,IAAI0L,KAAK1L,MAAM,EAAE;wBACf4J,QAAQI,GAAG,CAAC0B,KAAK1L,MAAM;oBACzB;gBACF;YACF;YAEAoJ,6BAAAA,UAAWwB,MAAM,CAAC;gBAChBC,WAAWxL;gBACXyL,SAAS;oBACPc,aAAa;oBACbC,iBAAiBxB;gBACnB;YACF;YACAtI,gBAAgByG,GAAG,CAACa,WAAWc;QACjC;IACF;AACF;AACA,eAAe,MAAM2B;IAInBC,YAAY,EAAEhF,GAAG,EAAE9F,UAAU,EAAyC,CAAE;QACtE,IAAI,CAAC8F,GAAG,GAAGA;QACX,IAAI,CAAC9F,UAAU,GAAGA;IACpB;IAEO+K,MAAMtH,QAA0B,EAAE;QACvCA,SAAS4B,KAAK,CAACxE,WAAW,CAAC2E,GAAG,CAAC9G,MAAM,CAACmC,aAAaD;YACjD,MAAM,EAAEyE,KAAK,EAAE,GAAGzE,OAAOoK,mBAAmB;YAC5C;;OAEC,GACD,MAAMC,eAAepF,gBAAgB;gBACnCC,KAAK,IAAI,CAACA,GAAG;gBACbrC;gBACA5C;YACF;YACAwE,MAAM9B,MAAM,CAACgC,GAAG,CAAC,mBAAmBC,GAAG,CAAC9G,MAAMuM;YAC9C5F,MAAM9B,MAAM,CAACgC,GAAG,CAAC,sBAAsBC,GAAG,CAAC9G,MAAMuM;YACjD5F,MAAM9B,MAAM,CAACgC,GAAG,CAAC,kBAAkBC,GAAG,CAAC9G,MAAMuM;YAE7C;;OAEC,GACD,MAAMnK,kBAAkB,IAAIsG;YAC5BvG,YAAYwE,KAAK,CAAC6F,aAAa,CAACC,UAAU,CACxCzM,MACAuJ,mBAAmB;gBACjBpH;gBACA4C;gBACAqC,KAAK,IAAI,CAACA,GAAG;gBACbhF;YACF;YAGF;;OAEC,GACDD,YAAYwE,KAAK,CAAC+F,aAAa,CAAC5F,GAAG,CACjC;gBACE/D,MAAM;gBACN4J,OAAOhO,QAAQiO,WAAW,CAACC,8BAA8B;YAC3D,GACA5K,gBAAgB;gBACdE;gBACAC;gBACAnB,MAAM;oBACJK,YAAY,IAAI,CAACA,UAAU;gBAC7B;YACF;QAEJ;IACF;AACF;AAEA,OAAO,MAAMwL,2BAA2B;IACtC;IACA;IACA;IACA;IACA;CACD,CAAS;AAEV,MAAMC,yBAAyB,IAAIpF,IAAYmF;AAE/C,OAAO,SAASE;IACd,MAAMC,UAAkC,CAAC;IACzC,KAAK,MAAMC,OAAOJ,yBAA0B;QAC1CG,OAAO,CAACC,IAAI,GAAG,CAAC,cAAc,EAAEA,IAAI,CAAC;QACrCD,OAAO,CAAC,CAAC,KAAK,EAAEC,IAAI,CAAC,CAAC,GAAG,CAAC,cAAc,EAAEA,IAAI,CAAC;IACjD;IACA,OAAOD;AACT;AAEA,OAAO,eAAeE,oCAAoC,EACxDC,OAAO,EACPC,OAAO,EACPC,WAAW,EACXC,UAAU,EAMX;IACC,IACED,YAAYE,WAAW,KAAK,gBAC5BnI,eAAe+H,YACf,CAACL,uBAAuBU,GAAG,CAACL,UAC5B;QACA,wEAAwE;QACxE,IAAI;YACF,MAAMG,aAAaF,SAASD;QAC9B,EAAE,OAAM;YACN,OAAO,CAAC,uCAAuC,EAAEA,QAAQ,EAAE,CAAC;QAC9D;IACF;AACF"}