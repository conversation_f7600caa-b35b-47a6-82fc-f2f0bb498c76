{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/index.ts"], "names": ["getModuleBuildError", "NAME", "WellKnownErrorsPlugin", "apply", "compiler", "hooks", "compilation", "tap", "afterSeal", "tapPromise", "errors", "length", "Promise", "all", "map", "err", "i", "moduleError", "e", "console", "log"], "mappings": "AAEA,SAASA,mBAAmB,QAAQ,uBAAsB;AAE1D,MAAMC,OAAO;AACb,OAAO,MAAMC;IACXC,MAAMC,QAA0B,EAAE;QAChCA,SAASC,KAAK,CAACC,WAAW,CAACC,GAAG,CAACN,MAAM,CAACK;YACpCA,YAAYD,KAAK,CAACG,SAAS,CAACC,UAAU,CAACR,MAAM;oBACvCK;gBAAJ,KAAIA,sBAAAA,YAAYI,MAAM,qBAAlBJ,oBAAoBK,MAAM,EAAE;oBAC9B,MAAMC,QAAQC,GAAG,CACfP,YAAYI,MAAM,CAACI,GAAG,CAAC,OAAOC,KAAKC;wBACjC,IAAI;4BACF,MAAMC,cAAc,MAAMjB,oBACxBI,UACAE,aACAS;4BAEF,IAAIE,gBAAgB,OAAO;gCACzBX,YAAYI,MAAM,CAACM,EAAE,GAAGC;4BAC1B;wBACF,EAAE,OAAOC,GAAG;4BACVC,QAAQC,GAAG,CAACF;wBACd;oBACF;gBAEJ;YACF;QACF;IACF;AACF"}