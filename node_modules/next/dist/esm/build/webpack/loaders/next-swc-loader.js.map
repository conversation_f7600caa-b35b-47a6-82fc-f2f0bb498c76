{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-swc-loader.ts"], "names": ["isWasm", "transform", "getLoaderSWCOptions", "path", "isAbsolute", "loaderTransform", "parentTrace", "source", "inputSourceMap", "nextConfig", "filename", "resourcePath", "loaderOptions", "getOptions", "isServer", "rootDir", "pagesDir", "appDir", "hasReactRefresh", "jsConfig", "supportedBrowsers", "swcCacheDir", "hasServerComponents", "isServerLayer", "bundleTarget", "isPageFile", "startsWith", "relativeFilePathFromRoot", "relative", "swcOptions", "development", "mode", "modularizeImports", "optimizePackageImports", "experimental", "swcPlugins", "compilerOptions", "compiler", "optimizeServerReact", "isServerActionsEnabled", "serverActions", "programmaticOptions", "JSON", "stringify", "undefined", "sourceMaps", "sourceMap", "inlineSourcesContent", "sourceFileName", "jsc", "react", "Object", "prototype", "hasOwnProperty", "call", "swcSpan", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "then", "output", "eliminatedPackages", "pkg", "parse", "add", "code", "map", "EXCLUDED_PATHS", "pitch", "callback", "async", "process", "versions", "pnp", "test", "loaders", "length", "loaderIndex", "loaderSpan", "currentTraceSpan", "addDependency", "r", "sw<PERSON><PERSON><PERSON><PERSON>", "inputSource", "transformedSource", "outputSourceMap", "err", "raw"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,GAGA,SAASA,MAAM,EAAEC,SAAS,QAAQ,YAAW;AAC7C,SAA0BC,mBAAmB,QAAQ,oBAAmB;AACxE,OAAOC,QAAQC,UAAU,QAAQ,OAAM;AAkBvC,eAAeC,gBAEbC,WAAgB,EAChBC,MAAe,EACfC,cAAoB;QAiCMC,0BACZA,2BAESA,2BAMGA;IAxC1B,wBAAwB;IACxB,MAAMC,WAAW,IAAI,CAACC,YAAY;IAElC,IAAIC,gBAAkC,IAAI,CAACC,UAAU,MAAM,CAAC;IAE5D,MAAM,EACJC,QAAQ,EACRC,OAAO,EACPC,QAAQ,EACRC,MAAM,EACNC,eAAe,EACfT,UAAU,EACVU,QAAQ,EACRC,iBAAiB,EACjBC,WAAW,EACXC,mBAAmB,EACnBC,aAAa,EACbC,YAAY,EACb,GAAGZ;IACJ,MAAMa,aAAaf,SAASgB,UAAU,CAACV;IACvC,MAAMW,2BAA2BxB,KAAKyB,QAAQ,CAACb,SAASL;IAExD,MAAMmB,aAAa3B,oBAAoB;QACrCc;QACAC;QACAP;QACAI;QACAW;QACAK,aAAa,IAAI,CAACC,IAAI,KAAK;QAC3Bb;QACAc,iBAAiB,EAAEvB,8BAAAA,WAAYuB,iBAAiB;QAChDC,sBAAsB,EAAExB,+BAAAA,2BAAAA,WAAYyB,YAAY,qBAAxBzB,yBAA0BwB,sBAAsB;QACxEE,UAAU,EAAE1B,+BAAAA,4BAAAA,WAAYyB,YAAY,qBAAxBzB,0BAA0B0B,UAAU;QAChDC,eAAe,EAAE3B,8BAAAA,WAAY4B,QAAQ;QACrCC,mBAAmB,EAAE7B,+BAAAA,4BAAAA,WAAYyB,YAAY,qBAAxBzB,0BAA0B6B,mBAAmB;QAClEnB;QACAC;QACAC;QACAM;QACAL;QACAiB,sBAAsB,EAAE9B,+BAAAA,4BAAAA,WAAYyB,YAAY,qBAAxBzB,0BAA0B+B,aAAa;QAC/DjB;QACAC;IACF;IAEA,MAAMiB,sBAAsB;QAC1B,GAAGZ,UAAU;QACbnB;QACAF,gBAAgBA,iBAAiBkC,KAAKC,SAAS,CAACnC,kBAAkBoC;QAElE,sEAAsE;QACtEC,YAAY,IAAI,CAACC,SAAS;QAC1BC,sBAAsB,IAAI,CAACD,SAAS;QAEpC,qEAAqE;QACrE,qEAAqE;QACrE,WAAW;QACXE,gBAAgBtC;IAClB;IAEA,IAAI,CAAC+B,oBAAoBjC,cAAc,EAAE;QACvC,OAAOiC,oBAAoBjC,cAAc;IAC3C;IAEA,+BAA+B;IAC/B,IACE,IAAI,CAACuB,IAAI,IACTU,oBAAoBQ,GAAG,IACvBR,oBAAoBQ,GAAG,CAAChD,SAAS,IACjCwC,oBAAoBQ,GAAG,CAAChD,SAAS,CAACiD,KAAK,IACvC,CAACC,OAAOC,SAAS,CAACC,cAAc,CAACC,IAAI,CACnCb,oBAAoBQ,GAAG,CAAChD,SAAS,CAACiD,KAAK,EACvC,gBAEF;QACAT,oBAAoBQ,GAAG,CAAChD,SAAS,CAACiD,KAAK,CAACpB,WAAW,GACjD,IAAI,CAACC,IAAI,KAAK;IAClB;IAEA,MAAMwB,UAAUjD,YAAYkD,UAAU,CAAC;IACvC,OAAOD,QAAQE,YAAY,CAAC,IAC1BxD,UAAUM,QAAekC,qBAAqBiB,IAAI,CAAC,CAACC;YAClD,IAAIA,OAAOC,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,EAAE;gBACxD,KAAK,MAAMC,OAAOnB,KAAKoB,KAAK,CAACH,OAAOC,kBAAkB,EAAG;oBACvD,IAAI,CAACA,kBAAkB,CAACG,GAAG,CAACF;gBAC9B;YACF;YACA,OAAO;gBAACF,OAAOK,IAAI;gBAAEL,OAAOM,GAAG,GAAGvB,KAAKoB,KAAK,CAACH,OAAOM,GAAG,IAAIrB;aAAU;QACvE;AAEJ;AAEA,MAAMsB,iBACJ;AAEF,OAAO,SAASC;IACd,MAAMC,WAAW,IAAI,CAACC,KAAK;IACzB,CAAA;QACA,IACE,kDAAkD;QAClD,CAACC,QAAQC,QAAQ,CAACC,GAAG,IACrB,CAACN,eAAeO,IAAI,CAAC,IAAI,CAAC9D,YAAY,KACtC,IAAI,CAAC+D,OAAO,CAACC,MAAM,GAAG,MAAM,IAAI,CAACC,WAAW,IAC5CxE,WAAW,IAAI,CAACO,YAAY,KAC5B,CAAE,MAAMX,UACR;YACA,MAAM6E,aAAa,IAAI,CAACC,gBAAgB,CAACtB,UAAU,CAAC;YACpD,IAAI,CAACuB,aAAa,CAAC,IAAI,CAACpE,YAAY;YACpC,OAAOkE,WAAWpB,YAAY,CAAC,IAC7BpD,gBAAgBiD,IAAI,CAAC,IAAI,EAAEuB;QAE/B;IACF,CAAA,IAAKnB,IAAI,CAAC,CAACsB;QACT,IAAIA,GAAG,OAAOZ,SAAS,SAASY;QAChCZ;IACF,GAAGA;AACL;AAEA,eAAe,SAASa,UAEtBC,WAAmB,EACnB1E,cAAmB;IAEnB,MAAMqE,aAAa,IAAI,CAACC,gBAAgB,CAACtB,UAAU,CAAC;IACpD,MAAMY,WAAW,IAAI,CAACC,KAAK;IAC3BQ,WACGpB,YAAY,CAAC,IACZpD,gBAAgBiD,IAAI,CAAC,IAAI,EAAEuB,YAAYK,aAAa1E,iBAErDkD,IAAI,CACH,CAAC,CAACyB,mBAAmBC,gBAAqB;QACxChB,SAAS,MAAMe,mBAAmBC,mBAAmB5E;IACvD,GACA,CAAC6E;QACCjB,SAASiB;IACX;AAEN;AAEA,oCAAoC;AACpC,OAAO,MAAMC,MAAM,KAAI"}