{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-edge-ssr-loader/index.ts"], "names": ["getModuleBuildInfo", "WEBPACK_RESOURCE_QUERIES", "stringifyRequest", "RouteKind", "normalizePagePath", "swapDistFolderWithEsmDistFolder", "path", "replace", "getRouteModuleOptions", "page", "options", "definition", "kind", "PAGES", "pathname", "bundlePath", "filename", "edgeSSRLoader", "dev", "buildId", "absolutePagePath", "absoluteAppPath", "absoluteDocumentPath", "absolute500Path", "absoluteErrorPath", "isServerComponent", "stringifiedConfig", "stringifiedConfigBase64", "appDirLoader", "appDirLoaderBase64", "pagesType", "sriEnabled", "incremental<PERSON>ache<PERSON>andlerPath", "preferredRegion", "middlewareConfig", "middlewareConfigBase64", "serverActionsBodySizeLimit", "getOptions", "JSON", "parse", "<PERSON><PERSON><PERSON>", "from", "toString", "isAppDir", "buildInfo", "_module", "nextEdgeSSR", "route", "stringifiedPagePath", "stringifiedAppPath", "stringifiedErrorPath", "stringifiedDocumentPath", "stringified500Path", "pageModPath", "substring", "length", "edgeSSREntry", "transformed", "stringify"], "mappings": "AAKA,SAASA,kBAAkB,QAAQ,2BAA0B;AAC7D,SAASC,wBAAwB,QAAQ,4BAA2B;AACpE,SAASC,gBAAgB,QAAQ,0BAAyB;AAC1D,SAASC,SAAS,QAAQ,uCAAsC;AAChE,SAASC,iBAAiB,QAAQ,uDAAsD;AAsBxF;;;;;;;AAOA,GACA,SAASC,gCAAgCC,IAAY;IACnD,OAAOA,KAAKC,OAAO,CAAC,mBAAmB;AACzC;AAEA,SAASC,sBAAsBC,IAAY;IACzC,MAAMC,UAAoE;QACxEC,YAAY;YACVC,MAAMT,UAAUU,KAAK;YACrBJ,MAAML,kBAAkBK;YACxBK,UAAUL;YACV,2CAA2C;YAC3CM,YAAY;YACZC,UAAU;QACZ;IACF;IAEA,OAAON;AACT;AAEA,MAAMO,gBACJ,SAASA;IACP,MAAM,EACJC,GAAG,EACHT,IAAI,EACJU,OAAO,EACPC,gBAAgB,EAChBC,eAAe,EACfC,oBAAoB,EACpBC,eAAe,EACfC,iBAAiB,EACjBC,iBAAiB,EACjBC,mBAAmBC,uBAAuB,EAC1CC,cAAcC,kBAAkB,EAChCC,SAAS,EACTC,UAAU,EACVC,2BAA2B,EAC3BC,eAAe,EACfC,kBAAkBC,sBAAsB,EACxCC,0BAA0B,EAC3B,GAAG,IAAI,CAACC,UAAU;IAEnB,MAAMH,mBAAqCI,KAAKC,KAAK,CACnDC,OAAOC,IAAI,CAACN,wBAAwB,UAAUO,QAAQ;IAGxD,MAAMhB,oBAAoBc,OAAOC,IAAI,CACnCd,2BAA2B,IAC3B,UACAe,QAAQ;IACV,MAAMd,eAAeY,OAAOC,IAAI,CAC9BZ,sBAAsB,IACtB,UACAa,QAAQ;IACV,MAAMC,WAAWb,cAAc;IAE/B,MAAMc,YAAY5C,mBAAmB,IAAI,CAAC6C,OAAO;IACjDD,UAAUE,WAAW,GAAG;QACtB,gIAAgI;QAChIrB,mBAAmBA,sBAAsB;QACzChB,MAAMA;QACNkC;IACF;IACAC,UAAUG,KAAK,GAAG;QAChBtC;QACAW;QACAa;QACAC;IACF;IAEA,MAAMc,sBAAsB9C,iBAAiB,IAAI,EAAEkB;IACnD,MAAM6B,qBAAqB/C,iBACzB,IAAI,EACJG,gCAAgCgB;IAElC,MAAM6B,uBAAuBhD,iBAC3B,IAAI,EACJG,gCAAgCmB;IAElC,MAAM2B,0BAA0BjD,iBAC9B,IAAI,EACJG,gCAAgCiB;IAElC,MAAM8B,qBAAqB7B,kBACvBrB,iBAAiB,IAAI,EAAEqB,mBACvB;IAEJ,MAAM8B,cAAc,CAAC,EAAEzB,aAAa,EAAEoB,oBAAoBM,SAAS,CACjE,GACAN,oBAAoBO,MAAM,GAAG,GAC7B,EAAEZ,WAAW,CAAC,CAAC,EAAE1C,yBAAyBuD,YAAY,CAAC,CAAC,GAAG,GAAG,CAAC;IAEjE,MAAMC,cAAc,CAAC;;;;;;sBAMH,EAAEnB,KAAKoB,SAAS,CAAC5B,WAAW;IAC9C,EACEa,WACI,CAAC;;+BAEoB,EAAEL,KAAKoB,SAAS,CAACL,aAAa;;;;;IAKzD,CAAC,GACK,CAAC;2BACgB,EAAEF,wBAAwB;8BACvB,EAAEF,mBAAmB;oCACf,EAAED,oBAAoB;yCACjB,EAAEE,qBAAqB;MAC1D,EACEE,qBACI,CAAC,iCAAiC,EAAEA,mBAAmB,CAAC,GACxD,GACL;;;;;;;;;;;aAWM,EAAEd,KAAKoB,SAAS,CAAClD,sBAAsBC,OAAO;;;;;;;;;;;;aAY9C,EAAE6B,KAAKoB,SAAS,CAAClD,sBAAsB,YAAY;;;;;;;;;0BAStC,EAClB4C,qBACI,CAAC;;;aAGA,EAAEd,KAAKoB,SAAS,CAAClD,sBAAsB,SAAS;;;;;;;OAOtD,CAAC,GACI,OACL,CAAC,CACH;;IAED,EACEwB,8BACI,CAAC,oCAAoC,EAAEM,KAAKoB,SAAS,CACnD1B,6BACA,CAAC,GACH,uCACL;;;;;;;8CAOyC,EAAEM,KAAKoB,SAAS,CAACjD,MAAM;;yCAE5B,EACnCsB,aACI,0DACA,YACL;;;;;WAKM,EAAEb,IAAI;YACL,EAAEoB,KAAKoB,SAAS,CAACjD,MAAM;;;;;;;iBAOlB,EAAE,CAAC,CAACkC,SAAS;;;;+BAIC,EAAElB,kBAAkB;6BACtB,EAAEA,kBAAkB;kCACf,EAAEA,kBAAkB,GAAG,EACnD,OAAOW,+BAA+B,cAClC,cACAE,KAAKoB,SAAS,CAACtB,4BACpB;;cAES,EAAEV,kBAAkB;eACnB,EAAEY,KAAKoB,SAAS,CAACvC,SAAS;;;;;;;;;;;;;KAapC,CAAC;IAEF,OAAOsC;AACT;AACF,eAAexC,cAAa"}