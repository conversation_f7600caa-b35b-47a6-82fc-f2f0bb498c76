{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-barrel-loader.ts"], "names": ["path", "transform", "barrelTransformMappingCache", "Map", "getBarrelMapping", "resourcePath", "swcCacheDir", "resolve", "fs", "has", "get", "transpileSource", "filename", "source", "isWildcard", "isTypeScript", "endsWith", "Promise", "res", "inputSourceMap", "undefined", "sourceFileName", "optimizeBarrelExports", "wildcard", "jsc", "parser", "syntax", "experimental", "cacheRoot", "then", "output", "code", "visited", "Set", "getMatches", "file", "add", "rej", "readFile", "err", "data", "toString", "matches", "match", "prefix", "exportList", "JSON", "parse", "slice", "wildcardExports", "matchAll", "map", "decl", "length", "all", "req", "targetPath", "dirname", "replace", "targetMatches", "concat", "set", "NextBarrelLoader", "async", "cacheable", "names", "getOptions", "getResolve", "mainFields", "mapping", "clearDependencies", "callback", "stringify", "exportMap", "name", "filePath", "orig", "missedNames", "push", "join"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAoFC,GAID,OAAOA,UAAU,OAAM;AACvB,SAASC,SAAS,QAAQ,YAAW;AAErC,iFAAiF;AACjF,mFAAmF;AACnF,+DAA+D;AAC/D,kEAAkE;AAClE,MAAMC,8BAA8B,IAAIC;AASxC,eAAeC,iBACbC,YAAoB,EACpBC,WAAmB,EACnBC,OAA8D,EAC9DC,EAKC;IAED,IAAIN,4BAA4BO,GAAG,CAACJ,eAAe;QACjD,OAAOH,4BAA4BQ,GAAG,CAACL;IACzC;IAEA,6EAA6E;IAC7E,mDAAmD;IACnD,eAAeM,gBACbC,QAAgB,EAChBC,MAAc,EACdC,UAAmB;QAEnB,MAAMC,eAAeH,SAASI,QAAQ,CAAC,UAAUJ,SAASI,QAAQ,CAAC;QACnE,OAAO,IAAIC,QAAgB,CAACC,MAC1BjB,UAAUY,QAAQ;gBAChBD;gBACAO,gBAAgBC;gBAChBC,gBAAgBT;gBAChBU,uBAAuB;oBACrBC,UAAUT;gBACZ;gBACAU,KAAK;oBACHC,QAAQ;wBACNC,QAAQX,eAAe,eAAe;wBACtC,CAACA,eAAe,QAAQ,MAAM,EAAE;oBAClC;oBACAY,cAAc;wBACZC,WAAWtB;oBACb;gBACF;YACF,GAAGuB,IAAI,CAAC,CAACC;gBACPZ,IAAIY,OAAOC,IAAI;YACjB;IAEJ;IAEA,yCAAyC;IACzC,MAAMC,UAAU,IAAIC;IACpB,eAAeC,WAAWC,IAAY,EAAErB,UAAmB;QACzD,IAAIkB,QAAQvB,GAAG,CAAC0B,OAAO;YACrB,OAAO;QACT;QACAH,QAAQI,GAAG,CAACD;QAEZ,MAAMtB,SAAS,MAAM,IAAII,QAAgB,CAACC,KAAKmB;YAC7C7B,GAAG8B,QAAQ,CAACH,MAAM,CAACI,KAAKC;gBACtB,IAAID,OAAOC,SAASpB,WAAW;oBAC7BiB,IAAIE;gBACN,OAAO;oBACLrB,IAAIsB,KAAKC,QAAQ;gBACnB;YACF;QACF;QAEA,MAAMX,SAAS,MAAMnB,gBAAgBwB,MAAMtB,QAAQC;QAEnD,MAAM4B,UAAUZ,OAAOa,KAAK,CAC1B;QAEF,IAAI,CAACD,SAAS;YACZ,OAAO;QACT;QAEA,MAAME,SAASF,OAAO,CAAC,EAAE;QACzB,IAAIG,aAAaC,KAAKC,KAAK,CAACL,OAAO,CAAC,EAAE,CAACM,KAAK,CAAC,GAAG,CAAC;QAKjD,MAAMC,kBAAkB;eACnBnB,OAAOoB,QAAQ,CAAC;SACpB,CAACC,GAAG,CAAC,CAACR,QAAUA,KAAK,CAAC,EAAE;QAEzB,uEAAuE;QACvE,sEAAsE;QACtE,eAAe;QACf,IAAI7B,YAAY;YACd,KAAK,MAAMsC,QAAQP,WAAY;gBAC7BO,IAAI,CAAC,EAAE,GAAGjB;gBACViB,IAAI,CAAC,EAAE,GAAGA,IAAI,CAAC,EAAE;YACnB;QACF;QAEA,6EAA6E;QAC7E,IAAIH,gBAAgBI,MAAM,EAAE;YAC1B,MAAMpC,QAAQqC,GAAG,CACfL,gBAAgBE,GAAG,CAAC,OAAOI;gBACzB,MAAMC,aAAa,MAAMjD,QACvBP,KAAKyD,OAAO,CAACtB,OACboB,IAAIG,OAAO,CAAC,gDAAgD;gBAG9D,MAAMC,gBAAgB,MAAMzB,WAAWsB,YAAY;gBACnD,IAAIG,eAAe;oBACjB,wBAAwB;oBACxBd,aAAaA,WAAWe,MAAM,CAACD,cAAcd,UAAU;gBACzD;YACF;QAEJ;QAEA,OAAO;YACLD;YACAC;YACAI;QACF;IACF;IAEA,MAAM/B,MAAM,MAAMgB,WAAW7B,cAAc;IAC3CH,4BAA4B2D,GAAG,CAACxD,cAAca;IAE9C,OAAOA;AACT;AAEA,MAAM4C,mBAAmB;IAMvB,IAAI,CAACC,KAAK;IACV,IAAI,CAACC,SAAS,CAAC;IAEf,MAAM,EAAEC,KAAK,EAAE3D,WAAW,EAAE,GAAG,IAAI,CAAC4D,UAAU;IAE9C,yEAAyE;IACzE,6EAA6E;IAC7E,MAAM3D,UAAU,IAAI,CAAC4D,UAAU,CAAC;QAC9BC,YAAY;YAAC;YAAU;SAAO;IAChC;IAEA,MAAMC,UAAU,MAAMjE,iBACpB,IAAI,CAACC,YAAY,EACjBC,aACAC,SACA,IAAI,CAACC,EAAE;IAGT,4EAA4E;IAC5E,yEAAyE;IACzE,6EAA6E;IAC7E,wBAAwB;IACxB,IAAI,CAAC8D,iBAAiB;IAEtB,IAAI,CAACD,SAAS;QACZ,6FAA6F;QAC7F,2FAA2F;QAC3F,0FAA0F;QAC1F,+BAA+B;QAC/B,IAAI,CAACE,QAAQ,CAAC,MAAM,CAAC,cAAc,EAAEzB,KAAK0B,SAAS,CAAC,IAAI,CAACnE,YAAY,EAAE,CAAC;QACxE;IACF;IAEA,6EAA6E;IAC7E,MAAMuC,SAASyB,QAAQzB,MAAM;IAC7B,MAAMC,aAAawB,QAAQxB,UAAU;IACrC,MAAM4B,YAAY,IAAItE;IACtB,KAAK,MAAM,CAACuE,MAAMC,UAAUC,KAAK,IAAI/B,WAAY;QAC/C4B,UAAUZ,GAAG,CAACa,MAAM;YAACC;YAAUC;SAAK;IACtC;IAEA,IAAI9C,SAASc;IACb,IAAIiC,cAAwB,EAAE;IAC9B,KAAK,MAAMH,QAAQT,MAAO;QACxB,sBAAsB;QACtB,IAAIQ,UAAUhE,GAAG,CAACiE,OAAO;YACvB,MAAMtB,OAAOqB,UAAU/D,GAAG,CAACgE;YAE3B,IAAItB,IAAI,CAAC,EAAE,KAAK,KAAK;gBACnBtB,UAAU,CAAC,cAAc,EAAE4C,KAAK,MAAM,EAAE5B,KAAK0B,SAAS,CAACpB,IAAI,CAAC,EAAE,EAAE,CAAC;YACnE,OAAO,IAAIA,IAAI,CAAC,EAAE,KAAK,WAAW;gBAChCtB,UAAU,CAAC,sBAAsB,EAAE4C,KAAK,QAAQ,EAAE5B,KAAK0B,SAAS,CAC9DpB,IAAI,CAAC,EAAE,EACP,CAAC;YACL,OAAO,IAAIA,IAAI,CAAC,EAAE,KAAKsB,MAAM;gBAC3B5C,UAAU,CAAC,WAAW,EAAE4C,KAAK,QAAQ,EAAE5B,KAAK0B,SAAS,CAACpB,IAAI,CAAC,EAAE,EAAE,CAAC;YAClE,OAAO;gBACLtB,UAAU,CAAC,WAAW,EAAEsB,IAAI,CAAC,EAAE,CAAC,IAAI,EAAEsB,KAAK,QAAQ,EAAE5B,KAAK0B,SAAS,CACjEpB,IAAI,CAAC,EAAE,EACP,CAAC;YACL;QACF,OAAO;YACLyB,YAAYC,IAAI,CAACJ;QACnB;IACF;IAEA,mCAAmC;IACnC,IAAIG,YAAYxB,MAAM,GAAG,GAAG;QAC1B,KAAK,MAAME,OAAOc,QAAQpB,eAAe,CAAE;YACzCnB,UAAU,CAAC,gBAAgB,EAAEgB,KAAK0B,SAAS,CACzCjB,IAAIG,OAAO,CAAC,mBAAmBmB,YAAYE,IAAI,CAAC,OAAO,cACvD,CAAC;QACL;IACF;IAEA,IAAI,CAACR,QAAQ,CAAC,MAAMzC;AACtB;AAEA,eAAegC,iBAAgB"}