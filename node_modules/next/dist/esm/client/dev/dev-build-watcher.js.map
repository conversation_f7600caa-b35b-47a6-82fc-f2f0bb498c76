{"version": 3, "sources": ["../../../src/client/dev/dev-build-watcher.ts"], "names": ["HMR_ACTIONS_SENT_TO_BROWSER", "addMessageListener", "initializeBuildWatcher", "toggleCallback", "position", "shadowHost", "document", "createElement", "verticalProperty", "horizontalProperty", "split", "id", "style", "width", "height", "zIndex", "body", "append<PERSON><PERSON><PERSON>", "shadowRoot", "prefix", "attachShadow", "mode", "container", "createContainer", "css", "createCss", "isVisible", "isBuilding", "timeoutId", "obj", "handleMessage", "show", "clearTimeout", "updateContainer", "hide", "setTimeout", "action", "BUILDING", "BUILT", "SYNC", "classList", "add", "remove", "innerHTML", "textContent"], "mappings": "AAAA,0DAA0D,GAC1D,SAASA,2BAA2B,QAAQ,sCAAqC;AAEjF,SAASC,kBAAkB,QAAQ,4BAA2B;AAU9D,eAAe,SAASC,uBACtBC,cAAmD,EACnDC,QAAyB;IAAzBA,IAAAA,qBAAAA,WAAW;IAEX,MAAMC,aAAaC,SAASC,aAAa,CAAC;IAC1C,MAAM,CAACC,kBAAkBC,mBAAmB,GAAGL,SAASM,KAAK,CAAC;IAI9DL,WAAWM,EAAE,GAAG;IAChB,gEAAgE;IAChEN,WAAWO,KAAK,CAACR,QAAQ,GAAG;IAC5B,4DAA4D;IAC5DC,WAAWO,KAAK,CAACJ,iBAAiB,GAAG;IACrC,4DAA4D;IAC5DH,WAAWO,KAAK,CAACH,mBAAmB,GAAG;IACvCJ,WAAWO,KAAK,CAACC,KAAK,GAAG;IACzBR,WAAWO,KAAK,CAACE,MAAM,GAAG;IAC1BT,WAAWO,KAAK,CAACG,MAAM,GAAG;IAC1BT,SAASU,IAAI,CAACC,WAAW,CAACZ;IAE1B,IAAIa;IACJ,IAAIC,SAAS;IAEb,IAAId,WAAWe,YAAY,EAAE;QAC3BF,aAAab,WAAWe,YAAY,CAAC;YAAEC,MAAM;QAAO;IACtD,OAAO;QACL,iEAAiE;QACjE,2DAA2D;QAC3D,uBAAuB;QACvBH,aAAab;QACbc,SAAS;IACX;IAEA,YAAY;IACZ,MAAMG,YAAYC,gBAAgBJ;IAClCD,WAAWD,WAAW,CAACK;IAEvB,MAAM;IACN,MAAME,MAAMC,UAAUN,QAAQ;QAAEV;QAAoBD;IAAiB;IACrEU,WAAWD,WAAW,CAACO;IAEvB,QAAQ;IACR,IAAIE,YAAY;IAChB,IAAIC,aAAa;IACjB,IAAIC,YAAkD;IAEtD,gBAAgB;IAEhB3B,mBAAmB,CAAC4B;QAClB,IAAI;YACFC,cAAcD;QAChB,EAAE,UAAM,CAAC;IACX;IAEA,SAASE;QACPH,aAAaI,aAAaJ;QAC1BF,YAAY;QACZC,aAAa;QACbM;IACF;IAEA,SAASC;QACPP,aAAa;QACb,+CAA+C;QAC/CC,YAAYO,WAAW;YACrBT,YAAY;YACZO;QACF,GAAG;QACHA;IACF;IAEA,SAASH,cAAcD,GAAqB;QAC1C,IAAI,CAAE,CAAA,YAAYA,GAAE,GAAI;YACtB;QACF;QAEA,wCAAwC;QACxC,OAAQA,IAAIO,MAAM;YAChB,KAAKpC,4BAA4BqC,QAAQ;gBACvCN;gBACA;YACF,KAAK/B,4BAA4BsC,KAAK;YACtC,KAAKtC,4BAA4BuC,IAAI;gBACnCL;gBACA;QACJ;IACF;IAEA/B,eAAe;QACb4B;QACAG;IACF;IAEA,SAASD;QACP,IAAIN,YAAY;YACdL,UAAUkB,SAAS,CAACC,GAAG,CAAC,AAAC,KAAEtB,SAAO;QACpC,OAAO;YACLG,UAAUkB,SAAS,CAACE,MAAM,CAAC,AAAC,KAAEvB,SAAO;QACvC;QAEA,IAAIO,WAAW;YACbJ,UAAUkB,SAAS,CAACC,GAAG,CAAC,AAAC,KAAEtB,SAAO;QACpC,OAAO;YACLG,UAAUkB,SAAS,CAACE,MAAM,CAAC,AAAC,KAAEvB,SAAO;QACvC;IACF;AACF;AAEA,SAASI,gBAAgBJ,MAAc;IACrC,MAAMG,YAAYhB,SAASC,aAAa,CAAC;IACzCe,UAAUX,EAAE,GAAG,AAAC,KAAEQ,SAAO;IACzBG,UAAUqB,SAAS,GAAG,AAAC,oBACVxB,SAAO,oOAQJA,SAAO,iNAMRA,SAAO,0CAAuCA,SAAO;IAOpE,OAAOG;AACT;AAEA,SAASG,UACPN,MAAc,EACd,KAG2D;IAH3D,IAAA,EACEV,kBAAkB,EAClBD,gBAAgB,EACyC,GAH3D;IAKA,MAAMgB,MAAMlB,SAASC,aAAa,CAAC;IACnCiB,IAAIoB,WAAW,GAAG,AAAC,YACdzB,SAAO,mDAENX,mBAAiB,oBACjBC,qBAAmB,weAkBYD,mBAAiB,mCACrCW,SAAO,8CAGnBA,SAAO,eAAYA,SAAO,oDAI1BA,SAAO,eAAYA,SAAO,uBACzBX,mBAAiB,+CAIlBW,SAAO,4EAKPA,SAAO,kFAKPA,SAAO,oCACKA,SAAO,uEAGTA,SAAO,sCAEdX,mBAAiB,gEAIjBA,mBAAiB,oEAKVW,SAAO;IAWtB,OAAOK;AACT"}