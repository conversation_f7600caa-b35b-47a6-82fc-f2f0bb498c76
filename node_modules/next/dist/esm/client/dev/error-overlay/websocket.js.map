{"version": 3, "sources": ["../../../../src/client/dev/error-overlay/websocket.ts"], "names": ["source", "eventCallbacks", "getSocketProtocol", "assetPrefix", "protocol", "location", "URL", "addMessageListener", "callback", "push", "sendMessage", "data", "readyState", "OPEN", "send", "reconnections", "connectHMR", "options", "init", "close", "handleOnline", "window", "console", "log", "handleMessage", "event", "msg", "JSON", "parse", "eventCallback", "timer", "handleDisconnect", "onerror", "onclose", "reload", "clearTimeout", "setTimeout", "hostname", "port", "replace", "url", "startsWith", "split", "WebSocket", "path", "onopen", "onmessage"], "mappings": "AAEA,IAAIA;AAIJ,MAAMC,iBAAwC,EAAE;AAEhD,SAASC,kBAAkBC,WAAmB;IAC5C,IAAIC,WAAWC,SAASD,QAAQ;IAEhC,IAAI;QACF,uBAAuB;QACvBA,WAAW,IAAIE,IAAIH,aAAaC,QAAQ;IAC1C,EAAE,UAAM,CAAC;IAET,OAAOA,aAAa,UAAU,OAAO;AACvC;AAEA,OAAO,SAASG,mBAAmBC,QAAwB;IACzDP,eAAeQ,IAAI,CAACD;AACtB;AAEA,OAAO,SAASE,YAAYC,IAAY;IACtC,IAAI,CAACX,UAAUA,OAAOY,UAAU,KAAKZ,OAAOa,IAAI,EAAE;IAClD,OAAOb,OAAOc,IAAI,CAACH;AACrB;AAEA,IAAII,gBAAgB;AAEpB,OAAO,SAASC,WAAWC,OAA8C;IACvE,SAASC;QACP,IAAIlB,QAAQA,OAAOmB,KAAK;QAExB,SAASC;YACPL,gBAAgB;YAChBM,OAAOC,OAAO,CAACC,GAAG,CAAC;QACrB;QAEA,SAASC,cAAcC,KAA2B;YAChD,sDAAsD;YACtD,MAAMC,MAAwBC,KAAKC,KAAK,CAACH,MAAMd,IAAI;YACnD,KAAK,MAAMkB,iBAAiB5B,eAAgB;gBAC1C4B,cAAcH;YAChB;QACF;QAEA,IAAII;QACJ,SAASC;YACP/B,OAAOgC,OAAO,GAAG;YACjBhC,OAAOiC,OAAO,GAAG;YACjBjC,OAAOmB,KAAK;YACZJ;YACA,yGAAyG;YACzG,IAAIA,gBAAgB,IAAI;gBACtBM,OAAOhB,QAAQ,CAAC6B,MAAM;gBACtB;YACF;YAEAC,aAAaL;YACb,4BAA4B;YAC5BA,QAAQM,WAAWlB,MAAMH,gBAAgB,IAAI,OAAO;QACtD;QAEA,MAAM,EAAEsB,QAAQ,EAAEC,IAAI,EAAE,GAAGjC;QAC3B,MAAMD,WAAWF,kBAAkBe,QAAQd,WAAW,IAAI;QAC1D,MAAMA,cAAcc,QAAQd,WAAW,CAACoC,OAAO,CAAC,QAAQ;QAExD,IAAIC,MAAM,AAAGpC,WAAS,QAAKiC,WAAS,MAAGC,OACrCnC,CAAAA,cAAc,AAAC,MAAGA,cAAgB,EAAC;QAGrC,IAAIA,YAAYsC,UAAU,CAAC,SAAS;YAClCD,MAAM,AAAGpC,WAAS,QAAKD,YAAYuC,KAAK,CAAC,MAAM,CAAC,EAAE;QACpD;QAEA1C,SAAS,IAAIqB,OAAOsB,SAAS,CAAC,AAAC,KAAEH,MAAMvB,QAAQ2B,IAAI;QACnD5C,OAAO6C,MAAM,GAAGzB;QAChBpB,OAAOgC,OAAO,GAAGD;QACjB/B,OAAOiC,OAAO,GAAGF;QACjB/B,OAAO8C,SAAS,GAAGtB;IACrB;IAEAN;AACF"}