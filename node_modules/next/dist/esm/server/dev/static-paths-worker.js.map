{"version": 3, "sources": ["../../../src/server/dev/static-paths-worker.ts"], "names": ["buildAppStaticPaths", "buildStaticPaths", "collectGenerateParams", "loadComponents", "setHttpClientAndAgentOptions", "serverHooks", "staticGenerationAsyncStorage", "AppRouteRouteModule", "require", "loadStaticPaths", "distDir", "pathname", "config", "httpAgentOptions", "locales", "defaultLocale", "isAppPath", "page", "isrFlushToDisk", "fetchCacheKeyPrefix", "maxMemoryCacheSize", "requestHeaders", "incremental<PERSON>ache<PERSON>andlerPath", "setConfig", "components", "getStaticPaths", "Error", "routeModule", "generateParams", "is", "revalidate", "userland", "dynamic", "dynamicParams", "generateStaticParams", "segmentPath", "ComponentMod", "tree", "configFileName"], "mappings": "AAEA,OAAO,kBAAiB;AACxB,OAAO,yBAAwB;AAC/B,OAAO,sBAAqB;AAC5B,OAAO,4CAA2C;AAElD,SACEA,mBAAmB,EACnBC,gBAAgB,EAChBC,qBAAqB,QAChB,oBAAmB;AAE1B,SAASC,cAAc,QAAQ,qBAAoB;AACnD,SAASC,4BAA4B,QAAQ,0BAAyB;AAEtE,YAAYC,iBAAiB,+CAA8C;AAC3E,SAASC,4BAA4B,QAAQ,mEAAkE;AAE/G,MAAM,EAAEC,mBAAmB,EAAE,GAC3BC,QAAQ;AAIV,yDAAyD;AACzD,uDAAuD;AACvD,4BAA4B;AAC5B,OAAO,eAAeC,gBAAgB,EACpCC,OAAO,EACPC,QAAQ,EACRC,MAAM,EACNC,gBAAgB,EAChBC,OAAO,EACPC,aAAa,EACbC,SAAS,EACTC,IAAI,EACJC,cAAc,EACdC,mBAAmB,EACnBC,kBAAkB,EAClBC,cAAc,EACdC,2BAA2B,EAe5B;IAKC,oCAAoC;IACpCd,QAAQ,4CAA4Ce,SAAS,CAACX;IAC9DR,6BAA6B;QAC3BS;IACF;IAEA,MAAMW,aAAa,MAAMrB,eAAe;QACtCO;QACA,qDAAqD;QACrDO,MAAMA,QAAQN;QACdK;IACF;IAEA,IAAI,CAACQ,WAAWC,cAAc,IAAI,CAACT,WAAW;QAC5C,yDAAyD;QACzD,mDAAmD;QACnD,MAAM,IAAIU,MACR,CAAC,uDAAuD,EAAEf,SAAS,CAAC;IAExE;IAEA,IAAIK,WAAW;QACb,MAAM,EAAEW,WAAW,EAAE,GAAGH;QACxB,MAAMI,iBACJD,eAAepB,oBAAoBsB,EAAE,CAACF,eAClC;YACE;gBACEf,QAAQ;oBACNkB,YAAYH,YAAYI,QAAQ,CAACD,UAAU;oBAC3CE,SAASL,YAAYI,QAAQ,CAACC,OAAO;oBACrCC,eAAeN,YAAYI,QAAQ,CAACE,aAAa;gBACnD;gBACAC,sBAAsBP,YAAYI,QAAQ,CAACG,oBAAoB;gBAC/DC,aAAaxB;YACf;SACD,GACD,MAAMT,sBAAsBsB,WAAWY,YAAY,CAACC,IAAI;QAE9D,OAAO,MAAMrC,oBAAoB;YAC/BiB,MAAMN;YACNiB;YACAU,gBAAgB1B,OAAO0B,cAAc;YACrC5B;YACAW;YACAC;YACAjB;YACAC;YACAY;YACAC;YACAC;QACF;IACF;IAEA,OAAO,MAAMnB,iBAAiB;QAC5BgB,MAAMN;QACNc,gBAAgBD,WAAWC,cAAc;QACzCa,gBAAgB1B,OAAO0B,cAAc;QACrCxB;QACAC;IACF;AACF"}