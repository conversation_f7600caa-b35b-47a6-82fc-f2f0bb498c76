{"version": 3, "sources": ["../../../src/server/stream-utils/node-web-streams-helper.ts"], "names": ["nonNullable", "getTracer", "AppRenderSpan", "decodeText", "encodeText", "queueTask", "process", "env", "NEXT_RUNTIME", "globalThis", "setTimeout", "setImmediate", "streamToBufferedResult", "renderResult", "textDecoder", "TextDecoder", "concatenatedString", "writable", "write", "chunk", "end", "on", "off", "pipe", "cloneTransformStream", "source", "sourceReader", "readable", "<PERSON><PERSON><PERSON><PERSON>", "clone", "TransformStream", "start", "controller", "done", "value", "read", "enqueue", "transform", "chainStreams", "streams", "promise", "Promise", "resolve", "i", "length", "then", "pipeTo", "preventClose", "streamFromString", "str", "ReadableStream", "close", "streamToString", "stream", "reader", "bufferedString", "createBufferedTransformStream", "bufferedBytes", "Uint8Array", "pendingFlush", "flushBuffer", "newBufferedBytes", "byteLength", "set", "flush", "createInsertedHTMLStream", "getServerInsertedHTML", "insertedHTMLChunk", "renderToInitialFizzStream", "ReactDOMServer", "element", "streamOptions", "trace", "renderToReadableStream", "createHeadInsertionTransformStream", "insert", "inserted", "freezing", "insertion", "content", "index", "indexOf", "insertedHeadContent", "slice", "createDeferredSuffixStream", "suffix", "suffixFlushed", "suffixFlushTask", "res", "createMergedTransformStream", "dataStream", "dataStreamFinished", "dataStreamReader", "err", "error", "createMoveSuffixStream", "foundSuffix", "endsWith", "contentWithoutSuffix", "createRootLayoutValidatorStream", "assetPrefix", "getTree", "foundHtml", "foundBody", "includes", "missingTags", "filter", "JSON", "stringify", "tree", "continueFizzStream", "renderStream", "inlinedDataStream", "generateStaticHTML", "serverInsertedHTMLToHead", "validateRootLayout", "closeTag", "suffixUnclosed", "split", "allReady", "transforms", "reduce", "pipeThrough"], "mappings": "AAGA,SAASA,WAAW,QAAQ,yBAAwB;AACpD,SAASC,SAAS,QAAQ,sBAAqB;AAC/C,SAASC,aAAa,QAAQ,yBAAwB;AACtD,SAASC,UAAU,EAAEC,UAAU,QAAQ,kBAAiB;AAExD,MAAMC,YACJC,QAAQC,GAAG,CAACC,YAAY,KAAK,SAASC,WAAWC,UAAU,GAAGC;AAMhE,OAAO,MAAMC,yBAAyB,OACpCC;IAEA,MAAMC,cAAc,IAAIC;IACxB,IAAIC,qBAAqB;IAEzB,MAAMC,WAAW;QACfC,OAAMC,KAAU;YACdH,sBAAsBb,WAAWgB,OAAOL;QAC1C;QACAM,QAAO;QAEP,wCAAwC;QACxCC,OAAM;QACNC,QAAO;IACT;IACA,MAAMT,aAAaU,IAAI,CAACN;IACxB,OAAOD;AACT,EAAC;AAED,OAAO,SAASQ,qBAAqBC,MAAuB;IAC1D,MAAMC,eAAeD,OAAOE,QAAQ,CAACC,SAAS;IAC9C,MAAMC,QAAQ,IAAIC,gBAAgB;QAChC,MAAMC,OAAMC,UAAU;YACpB,MAAO,KAAM;gBACX,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMR,aAAaS,IAAI;gBAC/C,IAAIF,MAAM;oBACR;gBACF;gBACAD,WAAWI,OAAO,CAACF;YACrB;QACF;QACA,wBAAwB;QACxBG,cAAa;IACf;IAEA,OAAOR;AACT;AAEA,OAAO,SAASS,aACdC,OAA4B;IAE5B,MAAM,EAAEZ,QAAQ,EAAEV,QAAQ,EAAE,GAAG,IAAIa;IAEnC,IAAIU,UAAUC,QAAQC,OAAO;IAC7B,IAAK,IAAIC,IAAI,GAAGA,IAAIJ,QAAQK,MAAM,EAAE,EAAED,EAAG;QACvCH,UAAUA,QAAQK,IAAI,CAAC,IACrBN,OAAO,CAACI,EAAE,CAACG,MAAM,CAAC7B,UAAU;gBAAE8B,cAAcJ,IAAI,IAAIJ,QAAQK,MAAM;YAAC;IAEvE;IAEA,OAAOjB;AACT;AAEA,OAAO,SAASqB,iBAAiBC,GAAW;IAC1C,OAAO,IAAIC,eAAe;QACxBnB,OAAMC,UAAU;YACdA,WAAWI,OAAO,CAAChC,WAAW6C;YAC9BjB,WAAWmB,KAAK;QAClB;IACF;AACF;AAEA,OAAO,eAAeC,eACpBC,MAAkC;IAElC,MAAMC,SAASD,OAAOzB,SAAS;IAC/B,MAAMd,cAAc,IAAIC;IAExB,IAAIwC,iBAAiB;IAErB,MAAO,KAAM;QACX,MAAM,EAAEtB,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMoB,OAAOnB,IAAI;QAEzC,IAAIF,MAAM;YACR,OAAOsB;QACT;QAEAA,kBAAkBpD,WAAW+B,OAAOpB;IACtC;AACF;AAEA,OAAO,SAAS0C;IAId,IAAIC,gBAA4B,IAAIC;IACpC,IAAIC,eAAqC;IAEzC,MAAMC,cAAc,CAAC5B;QACnB,IAAI,CAAC2B,cAAc;YACjBA,eAAe,IAAIlB,QAAQ,CAACC;gBAC1BrC,UAAU;oBACR2B,WAAWI,OAAO,CAACqB;oBACnBA,gBAAgB,IAAIC;oBACpBC,eAAe;oBACfjB;gBACF;YACF;QACF;IACF;IAEA,OAAO,IAAIZ,gBAAgB;QACzBO,WAAUlB,KAAK,EAAEa,UAAU;YACzB,MAAM6B,mBAAmB,IAAIH,WAC3BD,cAAcb,MAAM,GAAGzB,MAAM2C,UAAU;YAEzCD,iBAAiBE,GAAG,CAACN;YACrBI,iBAAiBE,GAAG,CAAC5C,OAAOsC,cAAcb,MAAM;YAChDa,gBAAgBI;YAChBD,YAAY5B;QACd;QAEAgC;YACE,IAAIL,cAAc;gBAChB,OAAOA;YACT;QACF;IACF;AACF;AAEA,OAAO,SAASM,yBACdC,qBAA4C;IAE5C,OAAO,IAAIpC,gBAAgB;QACzB,MAAMO,WAAUlB,KAAK,EAAEa,UAAU;YAC/B,MAAMmC,oBAAoB/D,WAAW,MAAM8D;YAC3ClC,WAAWI,OAAO,CAAC+B;YACnBnC,WAAWI,OAAO,CAACjB;QACrB;IACF;AACF;AAEA,OAAO,SAASiD,0BAA0B,EACxCC,cAAc,EACdC,OAAO,EACPC,aAAa,EAKd;IACC,OAAOtE,YAAYuE,KAAK,CAACtE,cAAcuE,sBAAsB,EAAE,UAC7DJ,eAAeI,sBAAsB,CAACH,SAASC;AAEnD;AAEA,SAASG,mCACPC,MAA6B;IAE7B,IAAIC,WAAW;IACf,IAAIC,WAAW;IACf,MAAM/D,cAAc,IAAIC;IAExB,OAAO,IAAIe,gBAAgB;QACzB,MAAMO,WAAUlB,KAAK,EAAEa,UAAU;YAC/B,4DAA4D;YAC5D,IAAI6C,UAAU;gBACZ7C,WAAWI,OAAO,CAACjB;gBACnB;YACF;YAEA,MAAM2D,YAAY,MAAMH;YACxB,IAAIC,UAAU;gBACZ5C,WAAWI,OAAO,CAAChC,WAAW0E;gBAC9B9C,WAAWI,OAAO,CAACjB;gBACnB0D,WAAW;YACb,OAAO;gBACL,MAAME,UAAU5E,WAAWgB,OAAOL;gBAClC,MAAMkE,QAAQD,QAAQE,OAAO,CAAC;gBAC9B,IAAID,UAAU,CAAC,GAAG;oBAChB,MAAME,sBACJH,QAAQI,KAAK,CAAC,GAAGH,SAASF,YAAYC,QAAQI,KAAK,CAACH;oBACtDhD,WAAWI,OAAO,CAAChC,WAAW8E;oBAC9BL,WAAW;oBACXD,WAAW;gBACb;YACF;YAEA,IAAI,CAACA,UAAU;gBACb5C,WAAWI,OAAO,CAACjB;YACrB,OAAO;gBACLd,UAAU;oBACRwE,WAAW;gBACb;YACF;QACF;QACA,MAAMb,OAAMhC,UAAU;YACpB,gEAAgE;YAChE,MAAM8C,YAAY,MAAMH;YACxB,IAAIG,WAAW;gBACb9C,WAAWI,OAAO,CAAChC,WAAW0E;YAChC;QACF;IACF;AACF;AAEA,2DAA2D;AAC3D,gDAAgD;AAChD,SAASM,2BACPC,MAAc;IAEd,IAAIC,gBAAgB;IACpB,IAAIC,kBAAwC;IAE5C,OAAO,IAAIzD,gBAAgB;QACzBO,WAAUlB,KAAK,EAAEa,UAAU;YACzBA,WAAWI,OAAO,CAACjB;YACnB,IAAI,CAACmE,iBAAiBD,OAAOzC,MAAM,EAAE;gBACnC0C,gBAAgB;gBAChBC,kBAAkB,IAAI9C,QAAQ,CAAC+C;oBAC7B,wBAAwB;oBACxB,mEAAmE;oBACnE,iEAAiE;oBACjEnF,UAAU;wBACR2B,WAAWI,OAAO,CAAChC,WAAWiF;wBAC9BG;oBACF;gBACF;YACF;QACF;QACAxB,OAAMhC,UAAU;YACd,IAAIuD,iBAAiB,OAAOA;YAC5B,IAAI,CAACD,iBAAiBD,OAAOzC,MAAM,EAAE;gBACnC0C,gBAAgB;gBAChBtD,WAAWI,OAAO,CAAChC,WAAWiF;YAChC;QACF;IACF;AACF;AAEA,0EAA0E;AAC1E,0BAA0B;AAC1B,SAASI,4BACPC,UAAsC;IAEtC,IAAIC,qBAA2C;IAC/C,OAAO,IAAI7D,gBAAgB;QACzBO,WAAUlB,KAAK,EAAEa,UAAU;YACzBA,WAAWI,OAAO,CAACjB;YAEnB,IAAI,CAACwE,oBAAoB;gBACvB,MAAMC,mBAAmBF,WAAW9D,SAAS;gBAE7C,wBAAwB;gBACxB,gEAAgE;gBAChE,qEAAqE;gBACrE,uEAAuE;gBACvE,8DAA8D;gBAC9D,aAAa;gBACb+D,qBAAqB,IAAIlD,QAAQ,CAAC+C,MAChC,uEAAuE;oBACvE,mEAAmE;oBACnE,wCAAwC;oBACxC,uEAAuE;oBACvE,mEAAmE;oBACnE,WAAW;oBACX9E,WAAW;wBACT,IAAI;4BACF,MAAO,KAAM;gCACX,MAAM,EAAEuB,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAM0D,iBAAiBzD,IAAI;gCACnD,IAAIF,MAAM;oCACR,OAAOuD;gCACT;gCACAxD,WAAWI,OAAO,CAACF;4BACrB;wBACF,EAAE,OAAO2D,KAAK;4BACZ7D,WAAW8D,KAAK,CAACD;wBACnB;wBACAL;oBACF,GAAG;YAEP;QACF;QACAxB;YACE,IAAI2B,oBAAoB;gBACtB,OAAOA;YACT;QACF;IACF;AACF;AAEA;;;;CAIC,GACD,SAASI,uBACPV,MAAc;IAEd,IAAIW,cAAc;IAClB,MAAMlF,cAAc,IAAIC;IAExB,8DAA8D;IAC9D,OAAO,IAAIe,gBAAgB;QACzBO,WAAUlB,KAAK,EAAEa,UAAU;YACzB,IAAI,CAACqD,UAAUW,aAAa;gBAC1B,OAAOhE,WAAWI,OAAO,CAACjB;YAC5B;YAEA,MAAM4D,UAAU5E,WAAWgB,OAAOL;YAClC,IAAIiE,QAAQkB,QAAQ,CAACZ,SAAS;gBAC5BW,cAAc;gBACd,MAAME,uBAAuBnB,QAAQI,KAAK,CAAC,GAAG,CAACE,OAAOzC,MAAM;gBAC5DZ,WAAWI,OAAO,CAAChC,WAAW8F;YAChC,OAAO;gBACLlE,WAAWI,OAAO,CAACjB;YACrB;QACF;QACA6C,OAAMhC,UAAU;YACd,IAAIqD,QAAQ;gBACVrD,WAAWI,OAAO,CAAChC,WAAWiF;YAChC;QACF;IACF;AACF;AAEA,OAAO,SAASc,gCACdC,cAAc,EAAE,EAChBC,OAAgC;IAEhC,IAAIC,YAAY;IAChB,IAAIC,YAAY;IAChB,MAAMzF,cAAc,IAAIC;IAExB,OAAO,IAAIe,gBAAgB;QACzB,MAAMO,WAAUlB,KAAK,EAAEa,UAAU;YAC/B,IAAI,CAACsE,aAAa,CAACC,WAAW;gBAC5B,MAAMxB,UAAU5E,WAAWgB,OAAOL;gBAClC,IAAI,CAACwF,aAAavB,QAAQyB,QAAQ,CAAC,UAAU;oBAC3CF,YAAY;gBACd;gBACA,IAAI,CAACC,aAAaxB,QAAQyB,QAAQ,CAAC,UAAU;oBAC3CD,YAAY;gBACd;YACF;YACAvE,WAAWI,OAAO,CAACjB;QACrB;QACA6C,OAAMhC,UAAU;YACd,uEAAuE;YACvE,cAAc;YACd,IAAI,CAACsE,aAAa,CAACC,WAAW;gBAC5B,MAAME,cAAc;oBAClBH,YAAY,OAAO;oBACnBC,YAAY,OAAO;iBACpB,CAACG,MAAM,CAAC1G;gBAETgC,WAAWI,OAAO,CAChBhC,WACE,CAAC,mDAAmD,EAAEuG,KAAKC,SAAS,CAClE;oBAAEH;oBAAaL,aAAaA,eAAe;oBAAIS,MAAMR;gBAAU,GAC/D,SAAS,CAAC;YAGlB;QACF;IACF;AACF;AAEA,OAAO,eAAeS,mBACpBC,YAAiC,EACjC,EACE1B,MAAM,EACN2B,iBAAiB,EACjBC,kBAAkB,EAClB/C,qBAAqB,EACrBgD,wBAAwB,EACxBC,kBAAkB,EAYnB;IAED,MAAMC,WAAW;IAEjB,6EAA6E;IAC7E,MAAMC,iBAAiBhC,SAASA,OAAOiC,KAAK,CAACF,SAAS,CAAC,EAAE,GAAG;IAE5D,IAAIH,oBAAoB;QACtB,MAAMF,aAAaQ,QAAQ;IAC7B;IAEA,MAAMC,aAA6D;QACjE,qDAAqD;QACrDhE;QAEA,gCAAgC;QAChCU,yBAAyB,CAACgD,2BACtBjD,yBAAyBC,yBACzB;QAEJ,wBAAwB;QACxBmD,kBAAkB,OAAOjC,2BAA2BiC,kBAAkB;QAEtE,+EAA+E;QAC/EL,oBAAoBvB,4BAA4BuB,qBAAqB;QAErE,kDAAkD;QAClDjB,uBAAuBqB;QAEvB,0BAA0B;QAC1B,qFAAqF;QACrF,+EAA+E;QAC/ElD,yBAAyBgD,2BACrBxC,mCAAmCR,yBACnC;QAEJiD,qBACIhB,gCACEgB,mBAAmBf,WAAW,EAC9Be,mBAAmBd,OAAO,IAE5B;KACL,CAACK,MAAM,CAAC1G;IAET,OAAOwH,WAAWC,MAAM,CACtB,CAAC9F,UAAUU,YAAcV,SAAS+F,WAAW,CAACrF,YAC9C0E;AAEJ"}