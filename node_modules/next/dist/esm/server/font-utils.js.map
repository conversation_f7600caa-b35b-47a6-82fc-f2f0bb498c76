{"version": 3, "sources": ["../../src/server/font-utils.ts"], "names": ["Log", "GOOGLE_FONT_PROVIDER", "DEFAULT_SERIF_FONT", "DEFAULT_SANS_SERIF_FONT", "capsizeFontsMetrics", "require", "https", "CHROME_UA", "IE_UA", "isGoogleFont", "url", "startsWith", "getFontForUA", "UA", "Promise", "resolve", "reject", "rawData", "get", "headers", "res", "on", "chunk", "toString", "e", "getFontDefinitionFromNetwork", "result", "warn", "getFontDefinitionFromManifest", "manifest", "find", "font", "content", "parseGoogleFontName", "css", "regex", "matches", "matchAll", "fontNames", "Set", "fontFamily", "replace", "add", "formatName", "str", "word", "index", "toLowerCase", "toUpperCase", "formatOverrideValue", "val", "Math", "abs", "toFixed", "calculateOverrideValues", "fontName", "font<PERSON>ey", "fontMetrics", "category", "ascent", "descent", "lineGap", "unitsPerEm", "fallbackFont", "name", "calculateSizeAdjustValues", "xWidthAvg", "mainFontAvgWidth", "fallback<PERSON>ontName", "fallbackFontMetrics", "fallbackFontAvgWidth", "sizeAdjust", "calculateOverrideCSS", "trim", "calculateSizeAdjustCSS", "getFontOverrideCss", "useSizeAdjust", "calcFn", "fontCss", "reduce", "cssStr", "console", "log"], "mappings": "AAAA,YAAYA,SAAS,sBAAqB;AAC1C,SACEC,oBAAoB,EACpBC,kBAAkB,EAClBC,uBAAuB,QAClB,0BAAyB;AAChC,MAAMC,sBAAsBC,QAAQ;AACpC,MAAMC,QAAQD,QAAQ;AAEtB,MAAME,YACJ;AACF,MAAMC,QAAQ;AASd,SAASC,aAAaC,GAAW;IAC/B,OAAOA,IAAIC,UAAU,CAACV;AACxB;AAEA,SAASW,aAAaF,GAAW,EAAEG,EAAU;IAC3C,OAAO,IAAIC,QAAQ,CAACC,SAASC;QAC3B,IAAIC,UAAe;QACnBX,MACGY,GAAG,CACFR,KACA;YACES,SAAS;gBACP,cAAcN;YAChB;QACF,GACA,CAACO;YACCA,IAAIC,EAAE,CAAC,QAAQ,CAACC;gBACdL,WAAWK;YACb;YACAF,IAAIC,EAAE,CAAC,OAAO;gBACZN,QAAQE,QAAQM,QAAQ,CAAC;YAC3B;QACF,GAEDF,EAAE,CAAC,SAAS,CAACG;YACZR,OAAOQ;QACT;IACJ;AACF;AAEA,OAAO,eAAeC,6BACpBf,GAAW;IAEX,IAAIgB,SAAS;IACb;;;GAGC,GACD,IAAI;QACF,IAAIjB,aAAaC,MAAM;YACrBgB,UAAU,MAAMd,aAAaF,KAAKF;QACpC;QACAkB,UAAU,MAAMd,aAAaF,KAAKH;IACpC,EAAE,OAAOiB,GAAG;QACVxB,IAAI2B,IAAI,CACN,CAAC,sCAAsC,EAAEjB,IAAI,+BAA+B,CAAC;QAE/E,OAAO;IACT;IAEA,OAAOgB;AACT;AAEA,OAAO,SAASE,8BACdlB,GAAW,EACXmB,QAAsB;QAGpBA;IADF,OACEA,EAAAA,iBAAAA,SAASC,IAAI,CAAC,CAACC;QACb,IAAIA,QAAQA,KAAKrB,GAAG,KAAKA,KAAK;YAC5B,OAAO;QACT;QACA,OAAO;IACT,uBALAmB,eAKIG,OAAO,KAAI;AAEnB;AAEA,SAASC,oBAAoBC,GAAW;IACtC,MAAMC,QAAQ;IACd,MAAMC,UAAUF,IAAIG,QAAQ,CAACF;IAC7B,MAAMG,YAAY,IAAIC;IAEtB,KAAK,IAAIR,QAAQK,QAAS;QACxB,MAAMI,aAAaT,IAAI,CAAC,EAAE,CAACU,OAAO,CAAC,gBAAgB;QACnDH,UAAUI,GAAG,CAACF;IAChB;IAEA,OAAO;WAAIF;KAAU;AACvB;AAEA,SAASK,WAAWC,GAAW;IAC7B,OAAOA,IACJH,OAAO,CAAC,uBAAuB,SAAUI,IAAI,EAAEC,KAAK;QACnD,OAAOA,UAAU,IAAID,KAAKE,WAAW,KAAKF,KAAKG,WAAW;IAC5D,GACCP,OAAO,CAAC,QAAQ;AACrB;AAEA,SAASQ,oBAAoBC,GAAW;IACtC,OAAOC,KAAKC,GAAG,CAACF,MAAM,KAAKG,OAAO,CAAC;AACrC;AAEA,OAAO,SAASC,wBAAwBC,QAAgB;IACtD,MAAMC,UAAUb,WAAWY;IAC3B,MAAME,cAAcrD,mBAAmB,CAACoD,QAAQ;IAChD,IAAI,EAAEE,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEC,UAAU,EAAE,GAAGL;IACzD,MAAMM,eACJL,aAAa,UAAUxD,qBAAqBC;IAC9CwD,SAASV,oBAAoBU,SAASG;IACtCF,UAAUX,oBAAoBW,UAAUE;IACxCD,UAAUZ,oBAAoBY,UAAUC;IAExC,OAAO;QACLH;QACAC;QACAC;QACAE,cAAcA,aAAaC,IAAI;IACjC;AACF;AAEA,OAAO,SAASC,0BAA0BV,QAAgB;IACxD,MAAMC,UAAUb,WAAWY;IAC3B,MAAME,cAAcrD,mBAAmB,CAACoD,QAAQ;IAChD,IAAI,EAAEE,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEC,UAAU,EAAEI,SAAS,EAAE,GAC/DT;IACF,MAAMU,mBAAmBD,YAAYJ;IACrC,MAAMC,eACJL,aAAa,UAAUxD,qBAAqBC;IAC9C,MAAMiE,mBAAmBzB,WAAWoB,aAAaC,IAAI;IACrD,MAAMK,sBAAsBjE,mBAAmB,CAACgE,iBAAiB;IACjE,MAAME,uBACJD,oBAAoBH,SAAS,GAAGG,oBAAoBP,UAAU;IAChE,IAAIS,aAAaL,YAAYC,mBAAmBG,uBAAuB;IAEvEX,SAASV,oBAAoBU,SAAUG,CAAAA,aAAaS,UAAS;IAC7DX,UAAUX,oBAAoBW,UAAWE,CAAAA,aAAaS,UAAS;IAC/DV,UAAUZ,oBAAoBY,UAAWC,CAAAA,aAAaS,UAAS;IAE/D,OAAO;QACLZ;QACAC;QACAC;QACAE,cAAcA,aAAaC,IAAI;QAC/BO,YAAYtB,oBAAoBsB;IAClC;AACF;AAEA,SAASC,qBAAqBzC,IAAY;IACxC,MAAMwB,WAAWxB,KAAK0C,IAAI;IAE1B,MAAM,EAAEd,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEE,YAAY,EAAE,GAC9CT,wBAAwBC;IAE1B,OAAO,CAAC;;oBAEU,EAAEA,SAAS;uBACR,EAAEI,OAAO;wBACR,EAAEC,QAAQ;yBACT,EAAEC,QAAQ;kBACjB,EAAEE,aAAa;;EAE/B,CAAC;AACH;AAEA,SAASW,uBAAuB3C,IAAY;IAC1C,MAAMwB,WAAWxB,KAAK0C,IAAI;IAE1B,MAAM,EAAEd,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEE,YAAY,EAAEQ,UAAU,EAAE,GAC1DN,0BAA0BV;IAE5B,OAAO,CAAC;;oBAEU,EAAEA,SAAS;uBACR,EAAEI,OAAO;wBACR,EAAEC,QAAQ;yBACT,EAAEC,QAAQ;mBAChB,EAAEU,WAAW;kBACd,EAAER,aAAa;;EAE/B,CAAC;AACH;AAEA,OAAO,SAASY,mBACdjE,GAAW,EACXwB,GAAW,EACX0C,gBAAgB,KAAK;IAErB,IAAI,CAACnE,aAAaC,MAAM;QACtB,OAAO;IACT;IAEA,MAAMmE,SAASD,gBAAgBF,yBAAyBF;IAExD,IAAI;QACF,MAAMlC,YAAYL,oBAAoBC;QAEtC,MAAM4C,UAAUxC,UAAUyC,MAAM,CAAC,CAACC,QAAQzB;YACxCyB,UAAUH,OAAOtB;YACjB,OAAOyB;QACT,GAAG;QAEH,OAAOF;IACT,EAAE,OAAOtD,GAAG;QACVyD,QAAQC,GAAG,CAAC,yCAAyC1D;QACrD,OAAO;IACT;AACF"}