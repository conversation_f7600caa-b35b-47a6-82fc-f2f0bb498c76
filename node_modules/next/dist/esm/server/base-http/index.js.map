{"version": 3, "sources": ["../../../src/server/base-http/index.ts"], "names": ["PERMANENT_REDIRECT_STATUS", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BaseNextRequest", "constructor", "method", "url", "body", "cookies", "_cookies", "headers", "BaseNextResponse", "destination", "redirect", "statusCode", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": "AAGA,SAASA,yBAAyB,QAAQ,6BAA4B;AAEtE,SAASC,eAAe,QAAQ,iCAAgC;AAmBhE,OAAO,MAAeC;IAIpBC,YAAmBC,QAAuBC,KAAoBC,KAAY;sBAAvDF;mBAAuBC;oBAAoBC;IAAa;IAI3E,qDAAqD;IAErD,IAAWC,UAAU;QACnB,IAAI,IAAI,CAACC,QAAQ,EAAE,OAAO,IAAI,CAACA,QAAQ;QACvC,OAAQ,IAAI,CAACA,QAAQ,GAAGP,gBAAgB,IAAI,CAACQ,OAAO;IACtD;AACF;AAEA,OAAO,MAAeC;IAKpBP,YAAmBQ,YAA0B;2BAA1BA;IAA2B;IAmC9C,qDAAqD;IAErDC,SAASD,WAAmB,EAAEE,UAAkB,EAAE;QAChD,IAAI,CAACC,SAAS,CAAC,YAAYH;QAC3B,IAAI,CAACE,UAAU,GAAGA;QAElB,0DAA0D;QAC1D,qCAAqC;QACrC,IAAIA,eAAeb,2BAA2B;YAC5C,IAAI,CAACc,SAAS,CAAC,WAAW,CAAC,MAAM,EAAEH,YAAY,CAAC;QAClD;QACA,OAAO,IAAI;IACb;AACF"}