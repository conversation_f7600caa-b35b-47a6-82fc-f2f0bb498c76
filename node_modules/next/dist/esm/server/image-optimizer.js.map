{"version": 3, "sources": ["../../src/server/image-optimizer.ts"], "names": ["createHash", "promises", "cpus", "mediaType", "bold", "yellow", "contentDisposition", "getOrientation", "Orientation", "imageSizeOf", "isAnimated", "join", "nodeUrl", "getImageBlurSvg", "hasMatch", "createRequestResponseMocks", "sendEtagResponse", "getContentType", "getExtension", "AVIF", "WEBP", "PNG", "JPEG", "GIF", "SVG", "ICO", "CACHE_VERSION", "ANIMATABLE_TYPES", "VECTOR_TYPES", "BLUR_IMG_SIZE", "BLUR_QUALITY", "sharp", "require", "process", "env", "NEXT_SHARP_PATH", "concurrency", "divisor", "NODE_ENV", "Math", "floor", "max", "length", "e", "showSharpMissingWarning", "getSupportedMimeType", "options", "accept", "mimeType", "includes", "getHash", "items", "hash", "item", "update", "String", "digest", "replace", "writeToCacheDir", "dir", "extension", "maxAge", "expireAt", "buffer", "etag", "filename", "rm", "recursive", "force", "catch", "mkdir", "writeFile", "detectContentType", "every", "b", "i", "ImageOptimizerCache", "validateParams", "req", "query", "nextConfig", "isDev", "imageData", "images", "deviceSizes", "imageSizes", "domains", "minimumCacheTTL", "formats", "remotePatterns", "url", "w", "q", "href", "errorMessage", "Array", "isArray", "isAbsolute", "startsWith", "hrefParsed", "URL", "toString", "_error", "protocol", "width", "parseInt", "isNaN", "sizes", "push", "isValidSize", "quality", "headers", "isStatic", "basePath", "get<PERSON><PERSON><PERSON><PERSON>", "constructor", "distDir", "cacheDir", "get", "cache<PERSON>ey", "files", "readdir", "now", "Date", "file", "maxAgeSt", "expireAtSt", "split", "readFile", "Number", "value", "kind", "revalidateAfter", "curRevalidate", "isStale", "_", "set", "revalidate", "Error", "err", "console", "error", "ImageError", "statusCode", "message", "parseCacheControl", "str", "map", "Map", "directive", "key", "trim", "toLowerCase", "getMaxAge", "age", "endsWith", "slice", "n", "optimizeImage", "contentType", "height", "nextConfigOutput", "optimizedBuffer", "transformer", "sequentialRead", "rotate", "resize", "undefined", "withoutEnlargement", "avif", "avifQuality", "chromaSubsampling", "warn", "webp", "png", "jpeg", "<PERSON><PERSON><PERSON><PERSON>", "orientation", "operations", "RIGHT_TOP", "type", "numRotations", "BOTTOM_RIGHT", "LEFT_BOTTOM", "processBuffer", "imageOptimizer", "_req", "_res", "paramsResult", "handleRequest", "upstreamBuffer", "upstreamType", "upstreamRes", "fetch", "ok", "status", "<PERSON><PERSON><PERSON>", "from", "arrayBuffer", "mocked", "method", "socket", "res", "parse", "hasStreamed", "concat", "buffers", "<PERSON><PERSON><PERSON><PERSON>", "cacheControl", "dangerouslyAllowSVG", "vector", "animate", "output", "getMetadata", "meta", "opts", "blur<PERSON>idth", "blurHeight", "blurDataURL", "unescape", "getFileNameWithExtension", "urlWithoutQueryParams", "fileNameWithExtension", "pop", "fileName", "setResponseHeaders", "xCache", "imagesConfig", "<PERSON><PERSON><PERSON><PERSON>", "finished", "contentDispositionType", "contentSecurityPolicy", "sendResponse", "result", "byteLength", "end", "getImageSize", "metadata", "decodeBuffer"], "mappings": "AAAA,SAASA,UAAU,QAAQ,SAAQ;AACnC,SAASC,QAAQ,QAAQ,KAAI;AAC7B,SAASC,IAAI,QAAQ,KAAI;AAEzB,SAASC,SAAS,QAAQ,kCAAiC;AAC3D,SAASC,IAAI,EAAEC,MAAM,QAAQ,oBAAmB;AAChD,OAAOC,wBAAwB,yCAAwC;AACvE,SAASC,cAAc,EAAEC,WAAW,QAAQ,qCAAoC;AAChF,OAAOC,iBAAiB,gCAA+B;AACvD,OAAOC,gBAAgB,iCAAgC;AACvD,SAASC,IAAI,QAAQ,OAAM;AAC3B,OAAOC,aAA0C,MAAK;AAEtD,SAASC,eAAe,QAAQ,+BAA8B;AAE9D,SAASC,QAAQ,QAAQ,qCAAoC;AAE7D,SAASC,0BAA0B,QAAQ,qBAAoB;AAW/D,SAASC,gBAAgB,QAAQ,iBAAgB;AACjD,SAASC,cAAc,EAAEC,YAAY,QAAQ,iBAAgB;AAI7D,MAAMC,OAAO;AACb,MAAMC,OAAO;AACb,MAAMC,MAAM;AACZ,MAAMC,OAAO;AACb,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,gBAAgB;AACtB,MAAMC,mBAAmB;IAACP;IAAMC;IAAKE;CAAI;AACzC,MAAMK,eAAe;IAACJ;CAAI;AAC1B,MAAMK,gBAAgB,EAAE,mCAAmC;;AAC3D,MAAMC,eAAe,GAAG,mCAAmC;;AAE3D,IAAIC;AAEJ,IAAI;IACFA,QAAQC,QAAQC,QAAQC,GAAG,CAACC,eAAe,IAAI;IAC/C,IAAIJ,SAASA,MAAMK,WAAW,KAAK,GAAG;QACpC,2DAA2D;QAC3D,8DAA8D;QAC9D,0DAA0D;QAC1D,MAAMC,UAAUJ,QAAQC,GAAG,CAACI,QAAQ,KAAK,gBAAgB,IAAI;QAC7DP,MAAMK,WAAW,CAACG,KAAKC,KAAK,CAACD,KAAKE,GAAG,CAACvC,OAAOwC,MAAM,GAAGL,SAAS;IACjE;AACF,EAAE,OAAOM,GAAG;AACV,iEAAiE;AACnE;AAEA,IAAIC,0BAA0BX,QAAQC,GAAG,CAACI,QAAQ,KAAK;AAavD,SAASO,qBAAqBC,OAAiB,EAAEC,SAAS,EAAE;IAC1D,MAAMC,WAAW7C,UAAU4C,QAAQD;IACnC,OAAOC,OAAOE,QAAQ,CAACD,YAAYA,WAAW;AAChD;AAEA,OAAO,SAASE,QAAQC,KAAmC;IACzD,MAAMC,OAAOpD,WAAW;IACxB,KAAK,IAAIqD,QAAQF,MAAO;QACtB,IAAI,OAAOE,SAAS,UAAUD,KAAKE,MAAM,CAACC,OAAOF;aAC5C;YACHD,KAAKE,MAAM,CAACD;QACd;IACF;IACA,qDAAqD;IACrD,OAAOD,KAAKI,MAAM,CAAC,UAAUC,OAAO,CAAC,OAAO;AAC9C;AAEA,eAAeC,gBACbC,GAAW,EACXC,SAAiB,EACjBC,MAAc,EACdC,QAAgB,EAChBC,MAAc,EACdC,IAAY;IAEZ,MAAMC,WAAWtD,KAAKgD,KAAK,CAAC,EAAEE,OAAO,CAAC,EAAEC,SAAS,CAAC,EAAEE,KAAK,CAAC,EAAEJ,UAAU,CAAC;IAEvE,MAAM3D,SAASiE,EAAE,CAACP,KAAK;QAAEQ,WAAW;QAAMC,OAAO;IAAK,GAAGC,KAAK,CAAC,KAAO;IAEtE,MAAMpE,SAASqE,KAAK,CAACX,KAAK;QAAEQ,WAAW;IAAK;IAC5C,MAAMlE,SAASsE,SAAS,CAACN,UAAUF;AACrC;AAEA;;;;CAIC,GACD,OAAO,SAASS,kBAAkBT,MAAc;IAC9C,IAAI;QAAC;QAAM;QAAM;KAAK,CAACU,KAAK,CAAC,CAACC,GAAGC,IAAMZ,MAAM,CAACY,EAAE,KAAKD,IAAI;QACvD,OAAOpD;IACT;IACA,IACE;QAAC;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK,CAACmD,KAAK,CACpD,CAACC,GAAGC,IAAMZ,MAAM,CAACY,EAAE,KAAKD,IAE1B;QACA,OAAOrD;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;KAAK,CAACoD,KAAK,CAAC,CAACC,GAAGC,IAAMZ,MAAM,CAACY,EAAE,KAAKD,IAAI;QAC7D,OAAOnD;IACT;IACA,IACE;QAAC;QAAM;QAAM;QAAM;QAAM;QAAG;QAAG;QAAG;QAAG;QAAM;QAAM;QAAM;KAAK,CAACkD,KAAK,CAChE,CAACC,GAAGC,IAAM,CAACD,KAAKX,MAAM,CAACY,EAAE,KAAKD,IAEhC;QACA,OAAOtD;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;QAAM;KAAK,CAACqD,KAAK,CAAC,CAACC,GAAGC,IAAMZ,MAAM,CAACY,EAAE,KAAKD,IAAI;QACnE,OAAOlD;IACT;IACA,IACE;QAAC;QAAG;QAAG;QAAG;QAAG;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK,CAACiD,KAAK,CAChE,CAACC,GAAGC,IAAM,CAACD,KAAKX,MAAM,CAACY,EAAE,KAAKD,IAEhC;QACA,OAAOvD;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;KAAK,CAACsD,KAAK,CAAC,CAACC,GAAGC,IAAMZ,MAAM,CAACY,EAAE,KAAKD,IAAI;QAC7D,OAAOjD;IACT;IACA,OAAO;AACT;AAEA,OAAO,MAAMmD;IAIX,OAAOC,eACLC,GAAoB,EACpBC,KAAkC,EAClCC,UAA8B,EAC9BC,KAAc,EACgC;YASvBD;QARvB,MAAME,YAAYF,WAAWG,MAAM;QACnC,MAAM,EACJC,cAAc,EAAE,EAChBC,aAAa,EAAE,EACfC,UAAU,EAAE,EACZC,kBAAkB,EAAE,EACpBC,UAAU;YAAC;SAAa,EACzB,GAAGN;QACJ,MAAMO,iBAAiBT,EAAAA,qBAAAA,WAAWG,MAAM,qBAAjBH,mBAAmBS,cAAc,KAAI,EAAE;QAC9D,MAAM,EAAEC,GAAG,EAAEC,CAAC,EAAEC,CAAC,EAAE,GAAGb;QACtB,IAAIc;QAEJ,IAAI,CAACH,KAAK;YACR,OAAO;gBAAEI,cAAc;YAA8B;QACvD,OAAO,IAAIC,MAAMC,OAAO,CAACN,MAAM;YAC7B,OAAO;gBAAEI,cAAc;YAAqC;QAC9D;QAEA,IAAIG;QAEJ,IAAIP,IAAIQ,UAAU,CAAC,MAAM;YACvBL,OAAOH;YACPO,aAAa;QACf,OAAO;YACL,IAAIE;YAEJ,IAAI;gBACFA,aAAa,IAAIC,IAAIV;gBACrBG,OAAOM,WAAWE,QAAQ;gBAC1BJ,aAAa;YACf,EAAE,OAAOK,QAAQ;gBACf,OAAO;oBAAER,cAAc;gBAA6B;YACtD;YAEA,IAAI,CAAC;gBAAC;gBAAS;aAAS,CAAC7C,QAAQ,CAACkD,WAAWI,QAAQ,GAAG;gBACtD,OAAO;oBAAET,cAAc;gBAA6B;YACtD;YAEA,IAAI,CAAChF,SAASwE,SAASG,gBAAgBU,aAAa;gBAClD,OAAO;oBAAEL,cAAc;gBAAiC;YAC1D;QACF;QAEA,IAAI,CAACH,GAAG;YACN,OAAO;gBAAEG,cAAc;YAAoC;QAC7D,OAAO,IAAIC,MAAMC,OAAO,CAACL,IAAI;YAC3B,OAAO;gBAAEG,cAAc;YAA2C;QACpE;QAEA,IAAI,CAACF,GAAG;YACN,OAAO;gBAAEE,cAAc;YAAsC;QAC/D,OAAO,IAAIC,MAAMC,OAAO,CAACJ,IAAI;YAC3B,OAAO;gBAAEE,cAAc;YAA6C;QACtE;QAEA,MAAMU,QAAQC,SAASd,GAAG;QAE1B,IAAIa,SAAS,KAAKE,MAAMF,QAAQ;YAC9B,OAAO;gBACLV,cAAc;YAChB;QACF;QAEA,MAAMa,QAAQ;eAAKvB,eAAe,EAAE;eAAOC,cAAc,EAAE;SAAE;QAE7D,IAAIJ,OAAO;YACT0B,MAAMC,IAAI,CAAC/E;QACb;QAEA,MAAMgF,cACJF,MAAM1D,QAAQ,CAACuD,UAAWvB,SAASuB,SAAS3E;QAE9C,IAAI,CAACgF,aAAa;YAChB,OAAO;gBACLf,cAAc,CAAC,yBAAyB,EAAEU,MAAM,eAAe,CAAC;YAClE;QACF;QAEA,MAAMM,UAAUL,SAASb;QAEzB,IAAIc,MAAMI,YAAYA,UAAU,KAAKA,UAAU,KAAK;YAClD,OAAO;gBACLhB,cACE;YACJ;QACF;QAEA,MAAM9C,WAAWH,qBAAqB2C,WAAW,EAAE,EAAEV,IAAIiC,OAAO,CAAC,SAAS;QAE1E,MAAMC,WAAWtB,IAAIQ,UAAU,CAC7B,CAAC,EAAElB,WAAWiC,QAAQ,IAAI,GAAG,mBAAmB,CAAC;QAGnD,OAAO;YACLpB;YACAc;YACAV;YACAe;YACAR;YACAM;YACA9D;YACAuC;QACF;IACF;IAEA,OAAO2B,YAAY,EACjBrB,IAAI,EACJW,KAAK,EACLM,OAAO,EACP9D,QAAQ,EAMT,EAAU;QACT,OAAOE,QAAQ;YAACxB;YAAemE;YAAMW;YAAOM;YAAS9D;SAAS;IAChE;IAEAmE,YAAY,EACVC,OAAO,EACPpC,UAAU,EAIX,CAAE;QACD,IAAI,CAACqC,QAAQ,GAAG1G,KAAKyG,SAAS,SAAS;QACvC,IAAI,CAACpC,UAAU,GAAGA;IACpB;IAEA,MAAMsC,IAAIC,QAAgB,EAAyC;QACjE,IAAI;YACF,MAAMF,WAAW1G,KAAK,IAAI,CAAC0G,QAAQ,EAAEE;YACrC,MAAMC,QAAQ,MAAMvH,SAASwH,OAAO,CAACJ;YACrC,MAAMK,MAAMC,KAAKD,GAAG;YAEpB,KAAK,MAAME,QAAQJ,MAAO;gBACxB,MAAM,CAACK,UAAUC,YAAY9D,MAAMJ,UAAU,GAAGgE,KAAKG,KAAK,CAAC;gBAC3D,MAAMhE,SAAS,MAAM9D,SAAS+H,QAAQ,CAACrH,KAAK0G,UAAUO;gBACtD,MAAM9D,WAAWmE,OAAOH;gBACxB,MAAMjE,SAASoE,OAAOJ;gBAEtB,OAAO;oBACLK,OAAO;wBACLC,MAAM;wBACNnE;wBACAD;wBACAH;oBACF;oBACAwE,iBACE7F,KAAKE,GAAG,CAACoB,QAAQ,IAAI,CAACmB,UAAU,CAACG,MAAM,CAACI,eAAe,IAAI,OAC3DoC,KAAKD,GAAG;oBACVW,eAAexE;oBACfyE,SAASZ,MAAM5D;gBACjB;YACF;QACF,EAAE,OAAOyE,GAAG;QACV,qDAAqD;QACvD;QACA,OAAO;IACT;IACA,MAAMC,IACJjB,QAAgB,EAChBW,KAAmC,EACnC,EACEO,UAAU,EAGX,EACD;QACA,IAAIP,CAAAA,yBAAAA,MAAOC,IAAI,MAAK,SAAS;YAC3B,MAAM,IAAIO,MAAM;QAClB;QAEA,IAAI,OAAOD,eAAe,UAAU;YAClC,MAAM,IAAIC,MAAM;QAClB;QACA,MAAM5E,WACJvB,KAAKE,GAAG,CAACgG,YAAY,IAAI,CAACzD,UAAU,CAACG,MAAM,CAACI,eAAe,IAAI,OAC/DoC,KAAKD,GAAG;QAEV,IAAI;YACF,MAAMhE,gBACJ/C,KAAK,IAAI,CAAC0G,QAAQ,EAAEE,WACpBW,MAAMtE,SAAS,EACf6E,YACA3E,UACAoE,MAAMnE,MAAM,EACZmE,MAAMlE,IAAI;QAEd,EAAE,OAAO2E,KAAK;YACZC,QAAQC,KAAK,CAAC,CAAC,+BAA+B,EAAEtB,SAAS,CAAC,EAAEoB;QAC9D;IACF;AACF;AACA,OAAO,MAAMG,mBAAmBJ;IAG9BvB,YAAY4B,UAAkB,EAAEC,OAAe,CAAE;QAC/C,KAAK,CAACA;QAEN,uCAAuC;QACvC,IAAID,cAAc,KAAK;YACrB,IAAI,CAACA,UAAU,GAAGA;QACpB,OAAO;YACL,IAAI,CAACA,UAAU,GAAG;QACpB;IACF;AACF;AAEA,SAASE,kBAAkBC,GAAkB;IAC3C,MAAMC,MAAM,IAAIC;IAChB,IAAI,CAACF,KAAK;QACR,OAAOC;IACT;IACA,KAAK,IAAIE,aAAaH,IAAInB,KAAK,CAAC,KAAM;QACpC,IAAI,CAACuB,KAAKpB,MAAM,GAAGmB,UAAUE,IAAI,GAAGxB,KAAK,CAAC;QAC1CuB,MAAMA,IAAIE,WAAW;QACrB,IAAItB,OAAO;YACTA,QAAQA,MAAMsB,WAAW;QAC3B;QACAL,IAAIX,GAAG,CAACc,KAAKpB;IACf;IACA,OAAOiB;AACT;AAEA,OAAO,SAASM,UAAUP,GAAkB;IAC1C,MAAMC,MAAMF,kBAAkBC;IAC9B,IAAIC,KAAK;QACP,IAAIO,MAAMP,IAAI7B,GAAG,CAAC,eAAe6B,IAAI7B,GAAG,CAAC,cAAc;QACvD,IAAIoC,IAAIxD,UAAU,CAAC,QAAQwD,IAAIC,QAAQ,CAAC,MAAM;YAC5CD,MAAMA,IAAIE,KAAK,CAAC,GAAG,CAAC;QACtB;QACA,MAAMC,IAAIpD,SAASiD,KAAK;QACxB,IAAI,CAAChD,MAAMmD,IAAI;YACb,OAAOA;QACT;IACF;IACA,OAAO;AACT;AAEA,OAAO,eAAeC,cAAc,EAClC/F,MAAM,EACNgG,WAAW,EACXjD,OAAO,EACPN,KAAK,EACLwD,MAAM,EACNC,gBAAgB,EAQjB;IACC,IAAIC,kBAAkBnG;IACtB,IAAIhC,OAAO;QACT,mCAAmC;QACnC,MAAMoI,cAAcpI,MAAMgC,QAAQ;YAChCqG,gBAAgB;QAClB;QAEAD,YAAYE,MAAM;QAElB,IAAIL,QAAQ;YACVG,YAAYG,MAAM,CAAC9D,OAAOwD;QAC5B,OAAO;YACLG,YAAYG,MAAM,CAAC9D,OAAO+D,WAAW;gBACnCC,oBAAoB;YACtB;QACF;QAEA,IAAIT,gBAAgB5I,MAAM;YACxB,IAAIgJ,YAAYM,IAAI,EAAE;gBACpB,MAAMC,cAAc5D,UAAU;gBAC9BqD,YAAYM,IAAI,CAAC;oBACf3D,SAASvE,KAAKE,GAAG,CAACiI,aAAa;oBAC/BC,mBAAmB;gBACrB;YACF,OAAO;gBACL/B,QAAQgC,IAAI,CACVvK,OAAOD,KAAK,gBACV,CAAC,wIAAwI,CAAC,GAC1I;gBAEJ+J,YAAYU,IAAI,CAAC;oBAAE/D;gBAAQ;YAC7B;QACF,OAAO,IAAIiD,gBAAgB3I,MAAM;YAC/B+I,YAAYU,IAAI,CAAC;gBAAE/D;YAAQ;QAC7B,OAAO,IAAIiD,gBAAgB1I,KAAK;YAC9B8I,YAAYW,GAAG,CAAC;gBAAEhE;YAAQ;QAC5B,OAAO,IAAIiD,gBAAgBzI,MAAM;YAC/B6I,YAAYY,IAAI,CAAC;gBAAEjE;YAAQ;QAC7B;QAEAoD,kBAAkB,MAAMC,YAAYa,QAAQ;IAC5C,iCAAiC;IACnC,OAAO;QACL,IAAIpI,2BAA2BqH,qBAAqB,cAAc;YAChErB,QAAQC,KAAK,CACX,CAAC,0LAA0L,CAAC;YAE9L,MAAM,IAAIC,WAAW,KAAK;QAC5B;QACA,wCAAwC;QACxC,IAAIlG,yBAAyB;YAC3BgG,QAAQgC,IAAI,CACVvK,OAAOD,KAAK,gBACV,CAAC,wLAAwL,CAAC,GAC1L;YAEJwC,0BAA0B;QAC5B;QAEA,qCAAqC;QACrC,MAAMqI,cAAc,MAAM1K,eAAewD;QAEzC,MAAMmH,aAA0B,EAAE;QAElC,IAAID,gBAAgBzK,YAAY2K,SAAS,EAAE;YACzCD,WAAWtE,IAAI,CAAC;gBAAEwE,MAAM;gBAAUC,cAAc;YAAE;QACpD,OAAO,IAAIJ,gBAAgBzK,YAAY8K,YAAY,EAAE;YACnDJ,WAAWtE,IAAI,CAAC;gBAAEwE,MAAM;gBAAUC,cAAc;YAAE;QACpD,OAAO,IAAIJ,gBAAgBzK,YAAY+K,WAAW,EAAE;YAClDL,WAAWtE,IAAI,CAAC;gBAAEwE,MAAM;gBAAUC,cAAc;YAAE;QACpD,OAAO;QACL,kCAAkC;QAClC,6DAA6D;QAC7D,+BAA+B;QACjC;QAEA,IAAIrB,QAAQ;YACVkB,WAAWtE,IAAI,CAAC;gBAAEwE,MAAM;gBAAU5E;gBAAOwD;YAAO;QAClD,OAAO;YACLkB,WAAWtE,IAAI,CAAC;gBAAEwE,MAAM;gBAAU5E;YAAM;QAC1C;QAEA,MAAM,EAAEgF,aAAa,EAAE,GACrBxJ,QAAQ;QAEV,IAAI+H,gBAAgB5I,MAAM;YACxB+I,kBAAkB,MAAMsB,cAAczH,QAAQmH,YAAY,QAAQpE;QACpE,OAAO,IAAIiD,gBAAgB3I,MAAM;YAC/B8I,kBAAkB,MAAMsB,cAAczH,QAAQmH,YAAY,QAAQpE;QACpE,OAAO,IAAIiD,gBAAgB1I,KAAK;YAC9B6I,kBAAkB,MAAMsB,cAAczH,QAAQmH,YAAY,OAAOpE;QACnE,OAAO,IAAIiD,gBAAgBzI,MAAM;YAC/B4I,kBAAkB,MAAMsB,cAAczH,QAAQmH,YAAY,QAAQpE;QACpE;IACF;IAEA,OAAOoD;AACT;AAEA,OAAO,eAAeuB,eACpBC,IAAqB,EACrBC,IAAoB,EACpBC,YAA+B,EAC/B5G,UAA8B,EAC9BC,KAA0B,EAC1B4G,aAIkB;IAElB,IAAIC;IACJ,IAAIC;IACJ,IAAIlI;IACJ,MAAM,EAAEoC,UAAU,EAAEJ,IAAI,EAAEW,KAAK,EAAExD,QAAQ,EAAE8D,OAAO,EAAE,GAAG8E;IAEvD,IAAI3F,YAAY;QACd,MAAM+F,cAAc,MAAMC,MAAMpG;QAEhC,IAAI,CAACmG,YAAYE,EAAE,EAAE;YACnBtD,QAAQC,KAAK,CACX,sCACAhD,MACAmG,YAAYG,MAAM;YAEpB,MAAM,IAAIrD,WACRkD,YAAYG,MAAM,EAClB;QAEJ;QAEAL,iBAAiBM,OAAOC,IAAI,CAAC,MAAML,YAAYM,WAAW;QAC1DP,eACEvH,kBAAkBsH,mBAClBE,YAAYjF,OAAO,CAACO,GAAG,CAAC;QAC1BzD,SAAS4F,UAAUuC,YAAYjF,OAAO,CAACO,GAAG,CAAC;IAC7C,OAAO;QACL,IAAI;YACF,MAAMiF,SAASxL,2BAA2B;gBACxC2E,KAAKG;gBACL2G,QAAQd,KAAKc,MAAM,IAAI;gBACvBzF,SAAS2E,KAAK3E,OAAO;gBACrB0F,QAAQf,KAAKe,MAAM;YACrB;YAEA,MAAMZ,cAAcU,OAAOzH,GAAG,EAAEyH,OAAOG,GAAG,EAAE9L,QAAQ+L,KAAK,CAAC9G,MAAM;YAChE,MAAM0G,OAAOG,GAAG,CAACE,WAAW;YAE5B,IAAI,CAACL,OAAOG,GAAG,CAAC3D,UAAU,EAAE;gBAC1BH,QAAQC,KAAK,CAAC,6BAA6BhD,MAAM0G,OAAOG,GAAG,CAAC3D,UAAU;gBACtE,MAAM,IAAID,WACRyD,OAAOG,GAAG,CAAC3D,UAAU,EACrB;YAEJ;YAEA+C,iBAAiBM,OAAOS,MAAM,CAACN,OAAOG,GAAG,CAACI,OAAO;YACjDf,eACEvH,kBAAkBsH,mBAClBS,OAAOG,GAAG,CAACK,SAAS,CAAC;YACvB,MAAMC,eAAeT,OAAOG,GAAG,CAACK,SAAS,CAAC;YAC1ClJ,SAASmJ,eAAevD,UAAUuD,gBAAgB;QACpD,EAAE,OAAOrE,KAAK;YACZC,QAAQC,KAAK,CAAC,sCAAsChD,MAAM8C;YAC1D,MAAM,IAAIG,WACR,KACA;QAEJ;IACF;IAEA,IAAIiD,cAAc;QAChBA,eAAeA,aAAavC,WAAW,GAAGD,IAAI;QAE9C,IACEwC,aAAa7F,UAAU,CAAC,gBACxB,CAAClB,WAAWG,MAAM,CAAC8H,mBAAmB,EACtC;YACArE,QAAQC,KAAK,CACX,CAAC,wBAAwB,EAAEhD,KAAK,YAAY,EAAEkG,aAAa,qCAAqC,CAAC;YAEnG,MAAM,IAAIjD,WACR,KACA;QAEJ;QACA,MAAMoE,SAAStL,aAAaqB,QAAQ,CAAC8I;QACrC,MAAMoB,UACJxL,iBAAiBsB,QAAQ,CAAC8I,iBAAiBrL,WAAWoL;QAExD,IAAIoB,UAAUC,SAAS;YACrB,OAAO;gBAAEpJ,QAAQ+H;gBAAgB/B,aAAagC;gBAAclI;YAAO;QACrE;QACA,IAAI,CAACkI,aAAa7F,UAAU,CAAC,aAAa6F,aAAa9I,QAAQ,CAAC,MAAM;YACpE2F,QAAQC,KAAK,CACX,kDACAhD,MACA,YACAkG;YAEF,MAAM,IAAIjD,WAAW,KAAK;QAC5B;IACF;IAEA,IAAIiB;IAEJ,IAAI/G,UAAU;QACZ+G,cAAc/G;IAChB,OAAO,IACL+I,CAAAA,gCAAAA,aAAc7F,UAAU,CAAC,cACzBhF,aAAa6K,iBACbA,iBAAiB3K,QACjB2K,iBAAiB5K,MACjB;QACA4I,cAAcgC;IAChB,OAAO;QACLhC,cAAczI;IAChB;IACA,IAAI;QACF,IAAI4I,kBAAkB,MAAMJ,cAAc;YACxC/F,QAAQ+H;YACR/B;YACAjD;YACAN;YACAyD,kBAAkBjF,WAAWoI,MAAM;QACrC;QACA,IAAIlD,iBAAiB;YACnB,IAAIjF,SAASuB,SAAS3E,iBAAiBiF,YAAYhF,cAAc;gBAC/D,MAAM,EAAEuL,WAAW,EAAE,GACnBrL,QAAQ;gBACV,8EAA8E;gBAC9E,gFAAgF;gBAChF,qFAAqF;gBACrF,MAAMsL,OAAO,MAAMD,YAAYnD;gBAC/B,MAAMqD,OAAO;oBACXC,WAAWF,KAAK9G,KAAK;oBACrBiH,YAAYH,KAAKtD,MAAM;oBACvB0D,aAAa,CAAC,KAAK,EAAE3D,YAAY,QAAQ,EAAEG,gBAAgB7D,QAAQ,CACjE,UACA,CAAC;gBACL;gBACA6D,kBAAkBkC,OAAOC,IAAI,CAACsB,SAAS9M,gBAAgB0M;gBACvDxD,cAAc;YAChB;YACA,OAAO;gBACLhG,QAAQmG;gBACRH;gBACAlG,QAAQtB,KAAKE,GAAG,CAACoB,QAAQmB,WAAWG,MAAM,CAACI,eAAe;YAC5D;QACF,OAAO;YACL,MAAM,IAAIuD,WAAW,KAAK;QAC5B;IACF,EAAE,OAAOD,OAAO;QACd,IAAIiD,kBAAkBC,cAAc;YAClC,yDAAyD;YACzD,OAAO;gBACLhI,QAAQ+H;gBACR/B,aAAagC;gBACblI,QAAQmB,WAAWG,MAAM,CAACI,eAAe;YAC3C;QACF,OAAO;YACL,MAAM,IAAIuD,WACR,KACA;QAEJ;IACF;AACF;AAEA,SAAS8E,yBACPlI,GAAW,EACXqE,WAA0B;IAE1B,MAAM,CAAC8D,sBAAsB,GAAGnI,IAAIqC,KAAK,CAAC;IAC1C,MAAM+F,wBAAwBD,sBAAsB9F,KAAK,CAAC,KAAKgG,GAAG;IAClE,IAAI,CAAChE,eAAe,CAAC+D,uBAAuB;QAC1C,OAAO;IACT;IAEA,MAAM,CAACE,SAAS,GAAGF,sBAAsB/F,KAAK,CAAC;IAC/C,MAAMnE,YAAY1C,aAAa6I;IAC/B,OAAO,CAAC,EAAEiE,SAAS,CAAC,EAAEpK,UAAU,CAAC;AACnC;AAEA,SAASqK,mBACPnJ,GAAoB,EACpB4H,GAAmB,EACnBhH,GAAW,EACX1B,IAAY,EACZ+F,WAA0B,EAC1B/C,QAAiB,EACjBkH,MAAoB,EACpBC,YAAiC,EACjCtK,MAAc,EACdoB,KAAc;IAEdyH,IAAI0B,SAAS,CAAC,QAAQ;IACtB1B,IAAI0B,SAAS,CACX,iBACApH,WACI,yCACA,CAAC,gBAAgB,EAAE/B,QAAQ,IAAIpB,OAAO,iBAAiB,CAAC;IAE9D,IAAI7C,iBAAiB8D,KAAK4H,KAAK1I,OAAO;QACpC,6CAA6C;QAC7C,OAAO;YAAEqK,UAAU;QAAK;IAC1B;IACA,IAAItE,aAAa;QACf2C,IAAI0B,SAAS,CAAC,gBAAgBrE;IAChC;IAEA,MAAMiE,WAAWJ,yBAAyBlI,KAAKqE;IAC/C2C,IAAI0B,SAAS,CACX,uBACA9N,mBAAmB0N,UAAU;QAAE5C,MAAM+C,aAAaG,sBAAsB;IAAC;IAG3E5B,IAAI0B,SAAS,CAAC,2BAA2BD,aAAaI,qBAAqB;IAC3E7B,IAAI0B,SAAS,CAAC,kBAAkBF;IAEhC,OAAO;QAAEG,UAAU;IAAM;AAC3B;AAEA,OAAO,SAASG,aACd1J,GAAoB,EACpB4H,GAAmB,EACnBhH,GAAW,EACX9B,SAAiB,EACjBG,MAAc,EACdiD,QAAiB,EACjBkH,MAAoB,EACpBC,YAAiC,EACjCtK,MAAc,EACdoB,KAAc;IAEd,MAAM8E,cAAc9I,eAAe2C;IACnC,MAAMI,OAAOd,QAAQ;QAACa;KAAO;IAC7B,MAAM0K,SAASR,mBACbnJ,KACA4H,KACAhH,KACA1B,MACA+F,aACA/C,UACAkH,QACAC,cACAtK,QACAoB;IAEF,IAAI,CAACwJ,OAAOJ,QAAQ,EAAE;QACpB3B,IAAI0B,SAAS,CAAC,kBAAkBhC,OAAOsC,UAAU,CAAC3K;QAClD2I,IAAIiC,GAAG,CAAC5K;IACV;AACF;AAEA,OAAO,eAAe6K,aACpB7K,MAAc,EACd,8BAA8B;AAC9BH,SAA2C;IAK3C,qDAAqD;IACrD,0DAA0D;IAC1D,IAAIA,cAAc,QAAQ;QACxB,IAAI7B,OAAO;YACT,MAAMoI,cAAcpI,MAAMgC;YAC1B,MAAM,EAAEyC,KAAK,EAAEwD,MAAM,EAAE,GAAG,MAAMG,YAAY0E,QAAQ;YACpD,OAAO;gBAAErI;gBAAOwD;YAAO;QACzB,OAAO;YACL,MAAM,EAAE8E,YAAY,EAAE,GACpB9M,QAAQ;YACV,MAAM,EAAEwE,KAAK,EAAEwD,MAAM,EAAE,GAAG,MAAM8E,aAAa/K;YAC7C,OAAO;gBAAEyC;gBAAOwD;YAAO;QACzB;IACF;IAEA,MAAM,EAAExD,KAAK,EAAEwD,MAAM,EAAE,GAAGvJ,YAAYsD;IACtC,OAAO;QAAEyC;QAAOwD;IAAO;AACzB"}