{"version": 3, "sources": ["../../../../src/server/web/sandbox/context.ts"], "names": ["AsyncLocalStorage", "decorateServerError", "getServerError", "COMPILER_NAMES", "EDGE_UNSUPPORTED_NODE_APIS", "EdgeRuntime", "readFileSync", "promises", "fs", "validateURL", "pick", "fetchInlineAsset", "runInContext", "BufferImplementation", "EventsImplementation", "AssertImplementation", "UtilImplementation", "AsyncHooksImplementation", "moduleContexts", "Map", "pendingModuleCaches", "clearModuleContext", "path", "handleContext", "key", "cache", "context", "paths", "has", "delete", "loadWasm", "wasm", "modules", "Promise", "all", "map", "binding", "module", "WebAssembly", "compile", "readFile", "filePath", "name", "buildEnvironmentVariablesFrom", "pairs", "Object", "keys", "process", "env", "fromEntries", "NEXT_RUNTIME", "throwUnsupportedAPIError", "error", "Error", "edgeServer", "createProcessPolyfill", "processPolyfill", "overridenValue", "defineProperty", "get", "undefined", "set", "value", "enumerable", "addStub", "getDecorateUnhandledError", "runtime", "EdgeRuntimeError", "evaluate", "getDecorateUnhandledRejection", "rejected", "reason", "NativeModuleMap", "mods", "entries", "createModuleContext", "options", "warnedEvals", "Set", "warnedWasmCodegens", "edgeFunctionEntry", "codeGeneration", "NODE_ENV", "strings", "extend", "id", "TypeError", "__next_eval__", "fn", "toString", "warning", "captureStackTrace", "add", "onWarning", "__next_webassembly_compile__", "__next_webassembly_instantiate__", "result", "instantiatedFromBuffer", "hasOwnProperty", "__fetch", "fetch", "input", "init", "callingError", "assetResponse", "assets", "distDir", "headers", "Headers", "prevs", "split", "concat", "moduleName", "join", "response", "url", "String", "catch", "err", "message", "stack", "__Request", "Request", "constructor", "next", "__redirect", "Response", "redirect", "bind", "args", "assign", "decorateUnhandledError", "addEventListener", "decorateUnhandledRejection", "getModuleContextShared", "deferredModuleContext", "getModuleContext", "lazyModuleContext", "useCache", "moduleContext", "evaluateInContext", "filepath", "content", "filename"], "mappings": "AAMA,SAASA,iBAAiB,QAAQ,cAAa;AAC/C,SACEC,mBAAmB,EACnBC,cAAc,QACT,6DAA4D;AACnE,SACEC,cAAc,EACdC,0BAA0B,QACrB,gCAA+B;AACtC,SAASC,WAAW,QAAQ,kCAAiC;AAC7D,SAASC,YAAY,EAAEC,YAAYC,EAAE,QAAQ,KAAI;AACjD,SAASC,WAAW,QAAQ,WAAU;AACtC,SAASC,IAAI,QAAQ,oBAAmB;AACxC,SAASC,gBAAgB,QAAQ,wBAAuB;AACxD,SAASC,YAAY,QAAQ,KAAI;AACjC,OAAOC,0BAA0B,cAAa;AAC9C,OAAOC,0BAA0B,cAAa;AAC9C,OAAOC,0BAA0B,cAAa;AAC9C,OAAOC,wBAAwB,YAAW;AAC1C,OAAOC,8BAA8B,mBAAkB;AAQvD;;;;CAIC,GACD,MAAMC,iBAAiB,IAAIC;AAE3B,MAAMC,sBAAsB,IAAID;AAEhC;;;;CAIC,GACD,OAAO,eAAeE,mBAAmBC,IAAY;IACnD,MAAMC,gBAAgB,CACpBC,KACAC,OACAC;QAEA,IAAID,yBAAAA,MAAOE,KAAK,CAACC,GAAG,CAACN,OAAO;YAC1BI,QAAQG,MAAM,CAACL;QACjB;IACF;IAEA,KAAK,MAAM,CAACA,KAAKC,MAAM,IAAIP,eAAgB;QACzCK,cAAcC,KAAKC,OAAOP;IAC5B;IACA,KAAK,MAAM,CAACM,KAAKC,MAAM,IAAIL,oBAAqB;QAC9CG,cAAcC,KAAK,MAAMC,OAAOL;IAClC;AACF;AAEA,eAAeU,SACbC,IAAoB;IAEpB,MAAMC,UAA8C,CAAC;IAErD,MAAMC,QAAQC,GAAG,CACfH,KAAKI,GAAG,CAAC,OAAOC;QACd,MAAMC,SAAS,MAAMC,YAAYC,OAAO,CACtC,MAAM/B,GAAGgC,QAAQ,CAACJ,QAAQK,QAAQ;QAEpCT,OAAO,CAACI,QAAQM,IAAI,CAAC,GAAGL;IAC1B;IAGF,OAAOL;AACT;AAEA,SAASW;IACP,MAAMC,QAAQC,OAAOC,IAAI,CAACC,QAAQC,GAAG,EAAEb,GAAG,CAAC,CAACX,MAAQ;YAACA;YAAKuB,QAAQC,GAAG,CAACxB,IAAI;SAAC;IAC3E,MAAMwB,MAAMH,OAAOI,WAAW,CAACL;IAC/BI,IAAIE,YAAY,GAAG;IACnB,OAAOF;AACT;AAEA,SAASG,yBAAyBT,IAAY;IAC5C,MAAMU,QACJ,IAAIC,MAAM,CAAC,uBAAuB,EAAEX,KAAK;8DACiB,CAAC;IAC7DzC,oBAAoBmD,OAAOjD,eAAemD,UAAU;IACpD,MAAMF;AACR;AAEA,SAASG;IACP,MAAMC,kBAAkB;QAAER,KAAKL;IAAgC;IAC/D,MAAMc,iBAAsC,CAAC;IAC7C,KAAK,MAAMjC,OAAOqB,OAAOC,IAAI,CAACC,SAAU;QACtC,IAAIvB,QAAQ,OAAO;QACnBqB,OAAOa,cAAc,CAACF,iBAAiBhC,KAAK;YAC1CmC;gBACE,IAAIF,cAAc,CAACjC,IAAI,KAAKoC,WAAW;oBACrC,OAAOH,cAAc,CAACjC,IAAI;gBAC5B;gBACA,IAAI,OAAO,AAACuB,OAAe,CAACvB,IAAI,KAAK,YAAY;oBAC/C,OAAO,IAAM2B,yBAAyB,CAAC,QAAQ,EAAE3B,IAAI,CAAC;gBACxD;gBACA,OAAOoC;YACT;YACAC,KAAIC,KAAK;gBACPL,cAAc,CAACjC,IAAI,GAAGsC;YACxB;YACAC,YAAY;QACd;IACF;IACA,OAAOP;AACT;AAEA,SAASQ,QAAQtC,OAA+B,EAAEgB,IAAY;IAC5DG,OAAOa,cAAc,CAAChC,SAASgB,MAAM;QACnCiB;YACE,OAAO;gBACLR,yBAAyBT;YAC3B;QACF;QACAqB,YAAY;IACd;AACF;AAEA,SAASE,0BAA0BC,OAAoB;IACrD,MAAMC,mBAAmBD,QAAQE,QAAQ,CAAC,CAAC,KAAK,CAAC;IACjD,OAAO,CAAChB;QACN,IAAIA,iBAAiBe,kBAAkB;YACrClE,oBAAoBmD,OAAOjD,eAAemD,UAAU;QACtD;IACF;AACF;AAEA,SAASe,8BAA8BH,OAAoB;IACzD,MAAMC,mBAAmBD,QAAQE,QAAQ,CAAC,CAAC,KAAK,CAAC;IACjD,OAAO,CAACE;QACN,IAAIA,SAASC,MAAM,YAAYJ,kBAAkB;YAC/ClE,oBAAoBqE,SAASC,MAAM,EAAEpE,eAAemD,UAAU;QAChE;IACF;AACF;AAEA,MAAMkB,kBAAkB,AAAC,CAAA;IACvB,MAAMC,OAGF;QACF,eAAe/D,KAAKG,sBAAsB;YACxC;YACA;YACA;YACA;YACA;SACD;QACD,eAAeH,KAAKI,sBAAsB;YACxC;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,oBAAoBJ,KAAKO,0BAA0B;YACjD;YACA;SACD;QACD,eAAeP,KAAKK,sBAAsB;YACxC;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAaL,KAAKM,oBAAoB;YACpC;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA,OAAO,IAAIG,IAAI0B,OAAO6B,OAAO,CAACD;AAChC,CAAA;AAEA;;;CAGC,GACD,eAAeE,oBAAoBC,OAA6B;IAC9D,MAAMC,cAAc,IAAIC;IACxB,MAAMC,qBAAqB,IAAID;IAC/B,MAAM/C,OAAO,MAAMD,SAAS8C,QAAQI,iBAAiB,CAACjD,IAAI,IAAI,EAAE;IAChE,MAAMmC,UAAU,IAAI7D,YAAY;QAC9B4E,gBACElC,QAAQC,GAAG,CAACkC,QAAQ,KAAK,eACrB;YAAEC,SAAS;YAAMpD,MAAM;QAAK,IAC5B6B;QACNwB,QAAQ,CAAC1D;YACPA,QAAQqB,OAAO,GAAGQ;YAElBV,OAAOa,cAAc,CAAChC,SAAS,WAAW;gBACxCqC,YAAY;gBACZD,OAAO,CAACuB;oBACN,MAAMvB,QAAQU,gBAAgBb,GAAG,CAAC0B;oBAClC,IAAI,CAACvB,OAAO;wBACV,MAAMwB,UAAU,8BAA8BD;oBAChD;oBACA,OAAOvB;gBACT;YACF;YAEApC,QAAQ6D,aAAa,GAAG,SAASA,cAAcC,EAAY;gBACzD,MAAMhE,MAAMgE,GAAGC,QAAQ;gBACvB,IAAI,CAACZ,YAAYjD,GAAG,CAACJ,MAAM;oBACzB,MAAMkE,UAAUxF,eACd,IAAImD,MACF,CAAC;yEAC0D,CAAC,GAE9DlD,eAAemD,UAAU;oBAE3BoC,QAAQhD,IAAI,GAAG;oBACfW,MAAMsC,iBAAiB,CAACD,SAASH;oBACjCV,YAAYe,GAAG,CAACpE;oBAChBoD,QAAQiB,SAAS,CAACH;gBACpB;gBACA,OAAOF;YACT;YAEA9D,QAAQoE,4BAA4B,GAClC,SAASA,6BAA6BN,EAAY;gBAChD,MAAMhE,MAAMgE,GAAGC,QAAQ;gBACvB,IAAI,CAACV,mBAAmBnD,GAAG,CAACJ,MAAM;oBAChC,MAAMkE,UAAUxF,eACd,IAAImD,MAAM,CAAC;yEACgD,CAAC,GAC5DlD,eAAemD,UAAU;oBAE3BoC,QAAQhD,IAAI,GAAG;oBACfW,MAAMsC,iBAAiB,CAACD,SAASI;oBACjCf,mBAAmBa,GAAG,CAACpE;oBACvBoD,QAAQiB,SAAS,CAACH;gBACpB;gBACA,OAAOF;YACT;YAEF9D,QAAQqE,gCAAgC,GACtC,eAAeA,iCAAiCP,EAAY;gBAC1D,MAAMQ,SAAS,MAAMR;gBAErB,kEAAkE;gBAClE,oEAAoE;gBACpE,oEAAoE;gBACpE,uCAAuC;gBACvC,EAAE;gBACF,wJAAwJ;gBACxJ,MAAMS,yBAAyBD,OAAOE,cAAc,CAAC;gBAErD,MAAM1E,MAAMgE,GAAGC,QAAQ;gBACvB,IAAIQ,0BAA0B,CAAClB,mBAAmBnD,GAAG,CAACJ,MAAM;oBAC1D,MAAMkE,UAAUxF,eACd,IAAImD,MAAM,CAAC;yEACgD,CAAC,GAC5DlD,eAAemD,UAAU;oBAE3BoC,QAAQhD,IAAI,GAAG;oBACfW,MAAMsC,iBAAiB,CAACD,SAASK;oBACjChB,mBAAmBa,GAAG,CAACpE;oBACvBoD,QAAQiB,SAAS,CAACH;gBACpB;gBACA,OAAOM;YACT;YAEF,MAAMG,UAAUzE,QAAQ0E,KAAK;YAC7B1E,QAAQ0E,KAAK,GAAG,OAAOC,OAAOC,OAAO,CAAC,CAAC;oBAcnCA;gBAbF,MAAMC,eAAe,IAAIlD,MAAM;gBAC/B,MAAMmD,gBAAgB,MAAM7F,iBAAiB;oBAC3C0F;oBACAI,QAAQ7B,QAAQI,iBAAiB,CAACyB,MAAM;oBACxCC,SAAS9B,QAAQ8B,OAAO;oBACxBhF;gBACF;gBACA,IAAI8E,eAAe;oBACjB,OAAOA;gBACT;gBAEAF,KAAKK,OAAO,GAAG,IAAIC,QAAQN,KAAKK,OAAO,IAAI,CAAC;gBAC5C,MAAME,QACJP,EAAAA,oBAAAA,KAAKK,OAAO,CAAChD,GAAG,CAAC,CAAC,uBAAuB,CAAC,sBAA1C2C,kBAA6CQ,KAAK,CAAC,SAAQ,EAAE;gBAC/D,MAAMhD,QAAQ+C,MAAME,MAAM,CAACnC,QAAQoC,UAAU,EAAEC,IAAI,CAAC;gBACpDX,KAAKK,OAAO,CAAC9C,GAAG,CAAC,2BAA2BC;gBAE5C,IAAI,CAACwC,KAAKK,OAAO,CAAC/E,GAAG,CAAC,eAAe;oBACnC0E,KAAKK,OAAO,CAAC9C,GAAG,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,kBAAkB,CAAC;gBACrD;gBAEA,MAAMqD,WACJ,OAAOb,UAAU,YAAY,SAASA,QAClCF,QAAQE,MAAMc,GAAG,EAAE;oBACjB,GAAGzG,KAAK2F,OAAO;wBACb;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;qBACD,CAAC;oBACF,GAAGC,IAAI;oBACPK,SAAS;wBACP,GAAG9D,OAAOI,WAAW,CAACoD,MAAMM,OAAO,CAAC;wBACpC,GAAG9D,OAAOI,WAAW,CAACqD,KAAKK,OAAO,CAAC;oBACrC;gBACF,KACAR,QAAQiB,OAAOf,QAAQC;gBAE7B,OAAO,MAAMY,SAASG,KAAK,CAAC,CAACC;oBAC3Bf,aAAagB,OAAO,GAAGD,IAAIC,OAAO;oBAClCD,IAAIE,KAAK,GAAGjB,aAAaiB,KAAK;oBAC9B,MAAMF;gBACR;YACF;YAEA,MAAMG,YAAY/F,QAAQgG,OAAO;YACjChG,QAAQgG,OAAO,GAAG,cAAcD;gBAE9BE,YAAYtB,KAAwB,EAAEC,IAA8B,CAAE;oBACpE,MAAMa,MACJ,OAAOd,UAAU,YAAY,SAASA,QAClCA,MAAMc,GAAG,GACTC,OAAOf;oBACb5F,YAAY0G;oBACZ,KAAK,CAACA,KAAKb;oBACX,IAAI,CAACsB,IAAI,GAAGtB,wBAAAA,KAAMsB,IAAI;gBACxB;YACF;YAEA,MAAMC,aAAanG,QAAQoG,QAAQ,CAACC,QAAQ,CAACC,IAAI,CAACtG,QAAQoG,QAAQ;YAClEpG,QAAQoG,QAAQ,CAACC,QAAQ,GAAG,CAAC,GAAGE;gBAC9BxH,YAAYwH,IAAI,CAAC,EAAE;gBACnB,OAAOJ,cAAcI;YACvB;YAEA,KAAK,MAAMvF,QAAQtC,2BAA4B;gBAC7C4D,QAAQtC,SAASgB;YACnB;YAEAG,OAAOqF,MAAM,CAACxG,SAASK;YAEvBL,QAAQ1B,iBAAiB,GAAGA;YAE5B,OAAO0B;QACT;IACF;IAEA,MAAMyG,yBAAyBlE,0BAA0BC;IACzDA,QAAQxC,OAAO,CAAC0G,gBAAgB,CAAC,SAASD;IAC1C,MAAME,6BAA6BhE,8BAA8BH;IACjEA,QAAQxC,OAAO,CAAC0G,gBAAgB,CAC9B,sBACAC;IAGF,OAAO;QACLnE;QACAvC,OAAO,IAAIR;QACX0D,aAAa,IAAIC;IACnB;AACF;AAUA,SAASwD,uBAAuB1D,OAA6B;IAC3D,IAAI2D,wBAAwBnH,oBAAoBuC,GAAG,CAACiB,QAAQoC,UAAU;IACtE,IAAI,CAACuB,uBAAuB;QAC1BA,wBAAwB5D,oBAAoBC;QAC5CxD,oBAAoByC,GAAG,CAACe,QAAQoC,UAAU,EAAEuB;IAC9C;IACA,OAAOA;AACT;AAEA;;;;;CAKC,GACD,OAAO,eAAeC,iBAAiB5D,OAA6B;IAMlE,IAAI6D;IAIJ,IAAI7D,QAAQ8D,QAAQ,EAAE;QACpBD,oBACEvH,eAAeyC,GAAG,CAACiB,QAAQoC,UAAU,KACpC,MAAMsB,uBAAuB1D;IAClC;IAEA,IAAI,CAAC6D,mBAAmB;QACtBA,oBAAoB,MAAM9D,oBAAoBC;QAC9C1D,eAAe2C,GAAG,CAACe,QAAQoC,UAAU,EAAEyB;IACzC;IAEA,MAAME,gBAAgBF;IAEtB,MAAMG,oBAAoB,CAACC;QACzB,IAAI,CAACF,cAAchH,KAAK,CAACC,GAAG,CAACiH,WAAW;YACtC,MAAMC,UAAUxI,aAAauI,UAAU;YACvC,IAAI;gBACFjI,aAAakI,SAASH,cAAczE,OAAO,CAACxC,OAAO,EAAE;oBACnDqH,UAAUF;gBACZ;gBACAF,cAAchH,KAAK,CAACkC,GAAG,CAACgF,UAAUC;YACpC,EAAE,OAAO1F,OAAO;gBACd,IAAIwB,QAAQ8D,QAAQ,EAAE;oBACpBC,iCAAAA,cAAehH,KAAK,CAACE,MAAM,CAACgH;gBAC9B;gBACA,MAAMzF;YACR;QACF;IACF;IAEA,OAAO;QAAE,GAAGuF,aAAa;QAAEC;IAAkB;AAC/C"}