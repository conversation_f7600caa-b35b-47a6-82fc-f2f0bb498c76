{"version": 3, "sources": ["../../../src/server/typescript/index.ts"], "names": ["init", "getIsClientEntry", "isAppEntryFile", "isDefaultFunctionExport", "isPositionInsideNode", "getSource", "isInsideApp", "NEXT_TS_ERRORS", "entryConfig", "serverLayer", "<PERSON><PERSON><PERSON><PERSON>", "clientBoundary", "metadata", "errorEntry", "createTSPlugin", "typescript", "ts", "create", "info", "proxy", "Object", "k", "keys", "languageService", "x", "args", "apply", "getCompletionsAtPosition", "fileName", "position", "options", "prior", "isGlobalCompletion", "isMemberCompletion", "isNewIdentifierLocation", "entries", "filterCompletionsAtPosition", "addCompletionsAtPosition", "source", "for<PERSON><PERSON><PERSON><PERSON><PERSON>", "node", "push", "getCompletionEntryDetails", "entryName", "formatOptions", "preferences", "data", "entryCompletionEntryDetails", "metadataCompletionEntryDetails", "getQuickInfoAtPosition", "definitions", "getDefinitionAtPosition", "hasDisallowedReactAPIDefinition", "metadataInfo", "overriden", "getSemanticDiagnostics", "isClientEntry", "isAppEntry", "e", "file", "category", "DiagnosticCategory", "Error", "code", "MISPLACED_CLIENT_ENTRY", "errorDiagnostic", "isImportDeclaration", "diagnostics", "getSemanticDiagnosticsForImportDeclaration", "isVariableStatement", "modifiers", "some", "m", "kind", "SyntaxKind", "ExportKeyword", "getSemanticDiagnosticsForExportVariableStatement", "metadataDiagnostics", "getSemanticDiagnosticsForExportVariableStatementInClientEntry", "getSemanticDiagnosticsForFunctionExport", "isFunctionDeclaration", "isExportDeclaration", "getSemanticDiagnosticsForExportDeclarationInClientEntry", "getSemanticDiagnosticsForExportDeclaration", "getDefinitionAndBoundSpan", "metadataDefinition"], "mappings": "AAAA;;;;;;;;CAQC,GAED,SACEA,IAAI,EACJC,gBAAgB,EAChBC,cAAc,EACdC,uBAAuB,EACvBC,oBAAoB,EACpBC,SAAS,EACTC,WAAW,QACN,UAAS;AAChB,SAASC,cAAc,QAAQ,aAAY;AAE3C,OAAOC,iBAAiB,iBAAgB;AACxC,OAAOC,iBAAiB,iBAAgB;AACxC,OAAOC,kBAAkB,gBAAe;AACxC,OAAOC,oBAAoB,0BAAyB;AACpD,OAAOC,cAAc,mBAAkB;AACvC,OAAOC,gBAAgB,gBAAe;AAGtC,OAAO,MAAMC,iBAAsD,CAAC,EAClEC,YAAYC,EAAE,EACf;IACC,SAASC,OAAOC,IAAsC;QACpDlB,KAAK;YACHgB;YACAE;QACF;QAEA,0BAA0B;QAC1B,MAAMC,QAAQC,OAAOH,MAAM,CAAC;QAC5B,KAAK,IAAII,KAAKD,OAAOE,IAAI,CAACJ,KAAKK,eAAe,EAAG;YAC/C,MAAMC,IAAI,AAACN,KAAKK,eAAe,AAAQ,CAACF,EAAE;YAC1CF,KAAK,CAACE,EAAE,GAAG,CAAC,GAAGI,OAAoBD,EAAEE,KAAK,CAACR,KAAKK,eAAe,EAAEE;QACnE;QAEA,kBAAkB;QAClBN,MAAMQ,wBAAwB,GAAG,CAC/BC,UACAC,UACAC;YAEA,IAAIC,QAAQb,KAAKK,eAAe,CAACI,wBAAwB,CACvDC,UACAC,UACAC,YACG;gBACHE,oBAAoB;gBACpBC,oBAAoB;gBACpBC,yBAAyB;gBACzBC,SAAS,EAAE;YACb;YACA,IAAI,CAACjC,eAAe0B,WAAW,OAAOG;YAEtC,0BAA0B;YAC1B,IAAI,CAAC9B,iBAAiB2B,WAAW;gBAC/B,gDAAgD;gBAChDG,MAAMI,OAAO,GAAG1B,YAAY2B,2BAA2B,CAACL,MAAMI,OAAO;gBAErE,6CAA6C;gBAC7CJ,QAAQnB,SAASwB,2BAA2B,CAC1CR,UACAC,UACAC,SACAC;YAEJ;YAEA,2CAA2C;YAC3CvB,YAAY6B,wBAAwB,CAACT,UAAUC,UAAUE;YAEzD,MAAMO,SAASjC,UAAUuB;YACzB,IAAI,CAACU,QAAQ,OAAOP;YAEpBf,GAAGuB,YAAY,CAACD,QAAS,CAACE;gBACxB,uDAAuD;gBACvD,IACEpC,qBAAqByB,UAAUW,SAC/BrC,wBAAwBqC,OACxB;oBACAT,MAAMI,OAAO,CAACM,IAAI,IACb/B,aAAaiB,wBAAwB,CACtCC,UACAY,MACAX;gBAGN;YACF;YAEA,OAAOE;QACT;QAEA,+BAA+B;QAC/BZ,MAAMuB,yBAAyB,GAAG,CAChCd,UACAC,UACAc,WACAC,eACAN,QACAO,aACAC;YAEA,MAAMC,8BAA8BvC,YAAYkC,yBAAyB,CACvEC,WACAG;YAEF,IAAIC,6BAA6B,OAAOA;YAExC,MAAMC,iCAAiCpC,SAAS8B,yBAAyB,CACvEd,UACAC,UACAc,WACAC,eACAN,QACAO,aACAC;YAEF,IAAIE,gCAAgC,OAAOA;YAE3C,OAAO9B,KAAKK,eAAe,CAACmB,yBAAyB,CACnDd,UACAC,UACAc,WACAC,eACAN,QACAO,aACAC;QAEJ;QAEA,aAAa;QACb3B,MAAM8B,sBAAsB,GAAG,CAACrB,UAAkBC;YAChD,MAAME,QAAQb,KAAKK,eAAe,CAAC0B,sBAAsB,CACvDrB,UACAC;YAEF,IAAI,CAAC3B,eAAe0B,WAAW,OAAOG;YAEtC,oEAAoE;YACpE,IAAI,CAAC9B,iBAAiB2B,WAAW;gBAC/B,MAAMsB,cAAchC,KAAKK,eAAe,CAAC4B,uBAAuB,CAC9DvB,UACAC;gBAEF,IACEqB,eACAzC,YAAY2C,+BAA+B,CAACF,cAC5C;oBACA;gBACF;gBAEA,MAAMG,eAAezC,SAASqC,sBAAsB,CAACrB,UAAUC;gBAC/D,IAAIwB,cAAc,OAAOA;YAC3B;YAEA,MAAMC,YAAY9C,YAAYyC,sBAAsB,CAACrB,UAAUC;YAC/D,IAAIyB,WAAW,OAAOA;YAEtB,OAAOvB;QACT;QAEA,qCAAqC;QACrCZ,MAAMoC,sBAAsB,GAAG,CAAC3B;YAC9B,MAAMG,QAAQb,KAAKK,eAAe,CAACgC,sBAAsB,CAAC3B;YAC1D,MAAMU,SAASjC,UAAUuB;YACzB,IAAI,CAACU,QAAQ,OAAOP;YAEpB,IAAIyB,gBAAgB;YACpB,MAAMC,aAAavD,eAAe0B;YAElC,IAAI;gBACF4B,gBAAgBvD,iBAAiB2B,UAAU;YAC7C,EAAE,OAAO8B,GAAQ;gBACf3B,MAAMU,IAAI,CAAC;oBACTkB,MAAMrB;oBACNsB,UAAU5C,GAAG6C,kBAAkB,CAACC,KAAK;oBACrCC,MAAMxD,eAAeyD,sBAAsB;oBAC3C,GAAGN,CAAC;gBACN;gBACAF,gBAAgB;YAClB;YAEA,IAAIlD,YAAYsB,WAAW;gBACzB,MAAMqC,kBAAkBpD,WAAW0C,sBAAsB,CACvDjB,QACAkB;gBAEFzB,MAAMU,IAAI,IAAIwB;YAChB;YAEAjD,GAAGuB,YAAY,CAACD,QAAS,CAACE;oBAgBtBA,iBAmDAA;gBAlEF,IAAIxB,GAAGkD,mBAAmB,CAAC1B,OAAO;oBAChC,aAAa;oBACb,IAAIiB,YAAY;wBACd,IAAI,CAACD,eAAe;4BAClB,oDAAoD;4BACpD,MAAMW,cACJ1D,YAAY2D,0CAA0C,CACpD9B,QACAE;4BAEJT,MAAMU,IAAI,IAAI0B;wBAChB;oBACF;gBACF,OAAO,IACLnD,GAAGqD,mBAAmB,CAAC7B,WACvBA,kBAAAA,KAAK8B,SAAS,qBAAd9B,gBAAgB+B,IAAI,CAAC,CAACC,IAAMA,EAAEC,IAAI,KAAKzD,GAAG0D,UAAU,CAACC,aAAa,IAClE;oBACA,mBAAmB;oBACnB,IAAIlB,YAAY;wBACd,yCAAyC;wBACzC,MAAMU,cACJ3D,YAAYoE,gDAAgD,CAC1DtC,QACAE;wBAEJ,MAAMqC,sBAAsBrB,gBACxB5C,SAASkE,6DAA6D,CACpElD,UACAY,QAEF5B,SAASgE,gDAAgD,CACvDhD,UACAY;wBAENT,MAAMU,IAAI,IAAI0B,gBAAgBU;oBAChC;oBAEA,IAAIrB,eAAe;wBACjBzB,MAAMU,IAAI,IACL9B,eAAeiE,gDAAgD,CAChEtC,QACAE;oBAGN;gBACF,OAAO,IAAIrC,wBAAwBqC,OAAO;oBACxC,8BAA8B;oBAC9B,IAAIiB,YAAY;wBACd,MAAMU,cAAczD,aAAa6C,sBAAsB,CACrD3B,UACAU,QACAE;wBAEFT,MAAMU,IAAI,IAAI0B;oBAChB;oBAEA,IAAIX,eAAe;wBACjBzB,MAAMU,IAAI,IACL9B,eAAeoE,uCAAuC,CACvDzC,QACAE;oBAGN;gBACF,OAAO,IACLxB,GAAGgE,qBAAqB,CAACxC,WACzBA,mBAAAA,KAAK8B,SAAS,qBAAd9B,iBAAgB+B,IAAI,CAAC,CAACC,IAAMA,EAAEC,IAAI,KAAKzD,GAAG0D,UAAU,CAACC,aAAa,IAClE;oBACA,sBAAsB;oBACtB,IAAIlB,YAAY;wBACd,MAAMoB,sBAAsBrB,gBACxB5C,SAASkE,6DAA6D,CACpElD,UACAY,QAEF5B,SAASgE,gDAAgD,CACvDhD,UACAY;wBAENT,MAAMU,IAAI,IAAIoC;oBAChB;oBAEA,IAAIrB,eAAe;wBACjBzB,MAAMU,IAAI,IACL9B,eAAeoE,uCAAuC,CACvDzC,QACAE;oBAGN;gBACF,OAAO,IAAIxB,GAAGiE,mBAAmB,CAACzC,OAAO;oBACvC,iBAAiB;oBACjB,IAAIiB,YAAY;wBACd,MAAMoB,sBAAsBrB,gBACxB5C,SAASsE,uDAAuD,CAC9DtD,UACAY,QAEF5B,SAASuE,0CAA0C,CACjDvD,UACAY;wBAENT,MAAMU,IAAI,IAAIoC;oBAChB;gBACF;YACF;YAEA,OAAO9C;QACT;QAEA,4CAA4C;QAC5CZ,MAAMiE,yBAAyB,GAAG,CAACxD,UAAkBC;YACnD,IAAI3B,eAAe0B,aAAa,CAAC3B,iBAAiB2B,WAAW;gBAC3D,MAAMyD,qBAAqBzE,SAASwE,yBAAyB,CAC3DxD,UACAC;gBAEF,IAAIwD,oBAAoB,OAAOA;YACjC;YAEA,OAAOnE,KAAKK,eAAe,CAAC6D,yBAAyB,CAACxD,UAAUC;QAClE;QAEA,OAAOV;IACT;IAEA,OAAO;QAAEF;IAAO;AAClB,EAAC"}