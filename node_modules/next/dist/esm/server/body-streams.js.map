{"version": 3, "sources": ["../../src/server/body-streams.ts"], "names": ["PassThrough", "requestToBodyStream", "context", "KUint8Array", "stream", "ReadableStream", "start", "controller", "on", "chunk", "enqueue", "Uint8Array", "close", "err", "error", "replaceRequestBody", "base", "key", "v", "bind", "getCloneableBody", "readable", "buffered", "endPromise", "Promise", "resolve", "reject", "catch", "finalize", "res", "cloneBodyStream", "input", "p1", "p2", "push"], "mappings": "AAEA,SAASA,WAAW,QAAQ,SAAQ;AAEpC,OAAO,SAASC,oBACdC,OAAkD,EAClDC,WAA8B,EAC9BC,MAAgB;IAEhB,OAAO,IAAIF,QAAQG,cAAc,CAAC;QAChCC,OAAMC,UAAU;YACdH,OAAOI,EAAE,CAAC,QAAQ,CAACC,QACjBF,WAAWG,OAAO,CAAC,IAAIP,YAAY;uBAAI,IAAIQ,WAAWF;iBAAO;YAE/DL,OAAOI,EAAE,CAAC,OAAO,IAAMD,WAAWK,KAAK;YACvCR,OAAOI,EAAE,CAAC,SAAS,CAACK,MAAQN,WAAWO,KAAK,CAACD;QAC/C;IACF;AACF;AAEA,SAASE,mBACPC,IAAO,EACPZ,MAAgB;IAEhB,IAAK,MAAMa,OAAOb,OAAQ;QACxB,IAAIc,IAAId,MAAM,CAACa,IAAsB;QACrC,IAAI,OAAOC,MAAM,YAAY;YAC3BA,IAAIA,EAAEC,IAAI,CAACH;QACb;QACAA,IAAI,CAACC,IAAe,GAAGC;IACzB;IAEA,OAAOF;AACT;AAOA,OAAO,SAASI,iBACdC,QAAW;IAEX,IAAIC,WAA4B;IAEhC,MAAMC,aAAa,IAAIC,QACrB,CAACC,SAASC;QACRL,SAASb,EAAE,CAAC,OAAOiB;QACnBJ,SAASb,EAAE,CAAC,SAASkB;IACvB,GACAC,KAAK,CAAC,CAACb;QACP,OAAO;YAAEA;QAAM;IACjB;IAEA,OAAO;QACL;;;;KAIC,GACD,MAAMc;YACJ,IAAIN,UAAU;gBACZ,MAAMO,MAAM,MAAMN;gBAElB,IAAIM,OAAO,OAAOA,QAAQ,YAAYA,IAAIf,KAAK,EAAE;oBAC/C,MAAMe,IAAIf,KAAK;gBACjB;gBACAC,mBAAmBM,UAAUC;gBAC7BA,WAAWD;YACb;QACF;QACA;;;KAGC,GACDS;YACE,MAAMC,QAAQT,YAAYD;YAC1B,MAAMW,KAAK,IAAIhC;YACf,MAAMiC,KAAK,IAAIjC;YACf+B,MAAMvB,EAAE,CAAC,QAAQ,CAACC;gBAChBuB,GAAGE,IAAI,CAACzB;gBACRwB,GAAGC,IAAI,CAACzB;YACV;YACAsB,MAAMvB,EAAE,CAAC,OAAO;gBACdwB,GAAGE,IAAI,CAAC;gBACRD,GAAGC,IAAI,CAAC;YACV;YACAZ,WAAWW;YACX,OAAOD;QACT;IACF;AACF"}