{"version": 3, "sources": ["../../../src/server/lib/start-server.ts"], "names": ["performance", "getEntriesByName", "length", "mark", "fs", "path", "http", "https", "Watchpack", "Log", "setupDebug", "RESTART_EXIT_CODE", "getDebugPort", "formatHostname", "initialize", "checkIsNodeDebugging", "CONFIG_FILES", "bold", "purple", "debug", "getRequestHandlers", "dir", "port", "isDev", "server", "hostname", "minimalMode", "isNodeDebugging", "keepAliveTimeout", "experimentalTestProxy", "experimentalHttpsServer", "dev", "logStartInfo", "networkUrl", "appUrl", "envInfo", "expFeatureInfo", "formatDurationText", "bootstrap", "prefixes", "ready", "process", "env", "__NEXT_VERSION", "join", "exp", "slice", "info", "event", "startServer", "allowRetry", "isExperimentalTestProxy", "selfSignedCertificate", "handlersReady", "handlersError", "handlersPromise", "Promise", "resolve", "reject", "requestHandler", "req", "res", "Error", "upgradeHandler", "socket", "head", "requestListener", "undefined", "err", "statusCode", "end", "error", "url", "console", "createServer", "key", "readFileSync", "cert", "on", "destroy", "portRetryCount", "code", "warn", "listen", "exit", "addr", "address", "actualHostname", "formattedHostname", "debugPort", "PORT", "cleanup", "close", "exception", "initResult", "Boolean", "startServerProcessDuration", "measure", "duration", "Math", "round", "watchConfigFiles", "dirToWatch", "onChange", "wp", "watch", "files", "map", "file", "filename", "__NEXT_DISABLE_MEMORY_WATCHER", "basename", "NEXT_PRIVATE_WORKER", "send", "addListener", "msg", "nextWorkerOptions", "nextServerReady", "nextWorkerReady"], "mappings": "AAAA,IAAIA,YAAYC,gBAAgB,CAAC,cAAcC,MAAM,KAAK,GAAG;IAC3DF,YAAYG,IAAI,CAAC;AACnB;AACA,OAAO,UAAS;AAChB,OAAO,yBAAwB;AAC/B,OAAO,kBAAiB;AAMxB,OAAOC,QAAQ,KAAI;AACnB,OAAOC,UAAU,OAAM;AACvB,OAAOC,UAAU,OAAM;AACvB,OAAOC,WAAW,QAAO;AACzB,OAAOC,eAAe,YAAW;AACjC,YAAYC,SAAS,yBAAwB;AAC7C,OAAOC,gBAAgB,2BAA0B;AACjD,SAASC,iBAAiB,EAAEC,YAAY,QAAQ,UAAS;AACzD,SAASC,cAAc,QAAQ,oBAAmB;AAClD,SAASC,UAAU,QAAQ,kBAAiB;AAC5C,SAASC,oBAAoB,QAAQ,sBAAqB;AAC1D,SAASC,YAAY,QAAQ,6BAA4B;AACzD,SAASC,IAAI,EAAEC,MAAM,QAAQ,uBAAsB;AAEnD,MAAMC,QAAQT,WAAW;AAmBzB,OAAO,eAAeU,mBAAmB,EACvCC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,eAAe,EACfC,gBAAgB,EAChBC,qBAAqB,EACrBC,uBAAuB,EAYxB;IACC,OAAOhB,WAAW;QAChBO;QACAC;QACAG;QACAM,KAAKR;QACLG;QACAF;QACAG,iBAAiBA,mBAAmB;QACpCC;QACAC;QACAC;IACF;AACF;AAEA,SAASE,aAAa,EACpBC,UAAU,EACVC,MAAM,EACNT,QAAQ,EACRU,OAAO,EACPC,cAAc,EACdC,kBAAkB,EAQnB;IACC5B,IAAI6B,SAAS,CACXrB,KAAKC,OAAO,CAAC,EAAET,IAAI8B,QAAQ,CAACC,KAAK,CAAC,SAAS,EAAEC,QAAQC,GAAG,CAACC,cAAc,CAAC,CAAC;IAE3ElC,IAAI6B,SAAS,CAAC,CAAC,gBAAgB,EAAEJ,OAAO,CAAC;IACzC,IAAIT,UAAU;QACZhB,IAAI6B,SAAS,CAAC,CAAC,gBAAgB,EAAEL,WAAW,CAAC;IAC/C;IACA,IAAIE,2BAAAA,QAASjC,MAAM,EAAEO,IAAI6B,SAAS,CAAC,CAAC,gBAAgB,EAAEH,QAAQS,IAAI,CAAC,MAAM,CAAC;IAE1E,IAAIR,kCAAAA,eAAgBlC,MAAM,EAAE;QAC1BO,IAAI6B,SAAS,CAAC,CAAC,qCAAqC,CAAC;QACrD,4BAA4B;QAC5B,KAAK,MAAMO,OAAOT,eAAeU,KAAK,CAAC,GAAG,GAAI;YAC5CrC,IAAI6B,SAAS,CAAC,CAAC,KAAK,EAAEO,IAAI,CAAC;QAC7B;QACA,qCAAqC,GACrC,IAAIT,eAAelC,MAAM,GAAG,GAAG;YAC7BO,IAAI6B,SAAS,CAAC,CAAC,QAAQ,CAAC;QAC1B;IACF;IAEA,oCAAoC;IACpC7B,IAAIsC,IAAI,CAAC;IACTtC,IAAIuC,KAAK,CAAC,CAAC,SAAS,EAAEX,mBAAmB,CAAC;AAC5C;AAEA,OAAO,eAAeY,YAAY,EAChC5B,GAAG,EACHC,IAAI,EACJC,KAAK,EACLE,QAAQ,EACRC,WAAW,EACXwB,UAAU,EACVtB,gBAAgB,EAChBuB,uBAAuB,EACvBC,qBAAqB,EACrBjB,OAAO,EACPC,cAAc,EACK;IACnB,IAAIiB,gBAAgB,KAAO;IAC3B,IAAIC,gBAAgB,KAAO;IAE3B,IAAIC,kBAA6C,IAAIC,QACnD,CAACC,SAASC;QACRL,gBAAgBI;QAChBH,gBAAgBI;IAClB;IAEF,IAAIC,iBAAuC,OACzCC,KACAC;QAEA,IAAIN,iBAAiB;YACnB,MAAMA;YACN,OAAOI,eAAeC,KAAKC;QAC7B;QACA,MAAM,IAAIC,MAAM;IAClB;IACA,IAAIC,iBAAuC,OACzCH,KACAI,QACAC;QAEA,IAAIV,iBAAiB;YACnB,MAAMA;YACN,OAAOQ,eAAeH,KAAKI,QAAQC;QACrC;QACA,MAAM,IAAIH,MAAM;IAClB;IAEA,4CAA4C;IAC5C,IAAIV,yBAAyB,CAAC7B,OAAO;QACnC,MAAM,IAAIuC,MACR;IAEJ;IAEA,eAAeI,gBAAgBN,GAAoB,EAAEC,GAAmB;QACtE,IAAI;YACF,IAAIN,iBAAiB;gBACnB,MAAMA;gBACNA,kBAAkBY;YACpB;YACA,MAAMR,eAAeC,KAAKC;QAC5B,EAAE,OAAOO,KAAK;YACZP,IAAIQ,UAAU,GAAG;YACjBR,IAAIS,GAAG,CAAC;YACR7D,IAAI8D,KAAK,CAAC,CAAC,6BAA6B,EAAEX,IAAIY,GAAG,CAAC,CAAC;YACnDC,QAAQF,KAAK,CAACH;QAChB;IACF;IAEA,MAAM5C,SAAS4B,wBACX7C,MAAMmE,YAAY,CAChB;QACEC,KAAKvE,GAAGwE,YAAY,CAACxB,sBAAsBuB,GAAG;QAC9CE,MAAMzE,GAAGwE,YAAY,CAACxB,sBAAsByB,IAAI;IAClD,GACAX,mBAEF5D,KAAKoE,YAAY,CAACR;IAEtB,IAAItC,kBAAkB;QACpBJ,OAAOI,gBAAgB,GAAGA;IAC5B;IACAJ,OAAOsD,EAAE,CAAC,WAAW,OAAOlB,KAAKI,QAAQC;QACvC,IAAI;YACF,MAAMF,eAAeH,KAAKI,QAAQC;QACpC,EAAE,OAAOG,KAAK;YACZJ,OAAOe,OAAO;YACdtE,IAAI8D,KAAK,CAAC,CAAC,6BAA6B,EAAEX,IAAIY,GAAG,CAAC,CAAC;YACnDC,QAAQF,KAAK,CAACH;QAChB;IACF;IAEA,IAAIY,iBAAiB;IAErBxD,OAAOsD,EAAE,CAAC,SAAS,CAACV;QAClB,IACElB,cACA5B,QACAC,SACA6C,IAAIa,IAAI,KAAK,gBACbD,iBAAiB,IACjB;YACAvE,IAAIyE,IAAI,CAAC,CAAC,KAAK,EAAE5D,KAAK,mBAAmB,EAAEA,OAAO,EAAE,SAAS,CAAC;YAC9DA,QAAQ;YACR0D,kBAAkB;YAClBxD,OAAO2D,MAAM,CAAC7D,MAAMG;QACtB,OAAO;YACLhB,IAAI8D,KAAK,CAAC,CAAC,sBAAsB,CAAC;YAClCE,QAAQF,KAAK,CAACH;YACd3B,QAAQ2C,IAAI,CAAC;QACf;IACF;IAEA,MAAMzD,kBAAkBZ;IAExB,MAAM,IAAIyC,QAAc,CAACC;QACvBjC,OAAOsD,EAAE,CAAC,aAAa;YACrB,MAAMO,OAAO7D,OAAO8D,OAAO;YAC3B,MAAMC,iBAAiB1E,eACrB,OAAOwE,SAAS,WACZA,CAAAA,wBAAAA,KAAMC,OAAO,KAAI7D,YAAY,cAC7B4D;YAEN,MAAMG,oBACJ,CAAC/D,YAAY8D,mBAAmB,YAC5B,cACAA,mBAAmB,SACnB,UACA1E,eAAeY;YAErBH,OAAO,OAAO+D,SAAS,WAAWA,CAAAA,wBAAAA,KAAM/D,IAAI,KAAIA,OAAOA;YAEvD,MAAMW,aAAa,CAAC,OAAO,EAAEsD,eAAe,CAAC,EAAEjE,KAAK,CAAC;YACrD,MAAMY,SAAS,CAAC,EACdkB,wBAAwB,UAAU,OACnC,GAAG,EAAEoC,kBAAkB,CAAC,EAAElE,KAAK,CAAC;YAEjC,IAAIK,iBAAiB;gBACnB,MAAM8D,YAAY7E;gBAClBH,IAAIsC,IAAI,CACN,CAAC,aAAa,EACZpB,oBAAoB,QAAQ,SAAS,GACtC,4EAA4E,EAAE8D,UAAU,CAAC,CAAC;YAE/F;YAEA,yCAAyC;YACzChD,QAAQC,GAAG,CAACgD,IAAI,GAAGpE,OAAO;YAE1B,IAAI;gBACF,MAAMqE,UAAU,CAACV;oBACf9D,MAAM;oBACNK,OAAOoE,KAAK;oBACZnD,QAAQ2C,IAAI,CAACH,QAAQ;gBACvB;gBACA,MAAMY,YAAY,CAACzB;oBACjB,uDAAuD;oBACvDK,QAAQF,KAAK,CAACH;gBAChB;gBACA3B,QAAQqC,EAAE,CAAC,QAAQ,CAACG,OAASU,QAAQV;gBACrC,+CAA+C;gBAC/CxC,QAAQqC,EAAE,CAAC,UAAU,IAAMa,QAAQ;gBACnClD,QAAQqC,EAAE,CAAC,WAAW,IAAMa,QAAQ;gBACpClD,QAAQqC,EAAE,CAAC,qBAAqBe;gBAChCpD,QAAQqC,EAAE,CAAC,sBAAsBe;gBAEjC,MAAMC,aAAa,MAAM1E,mBAAmB;oBAC1CC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC,iBAAiBoE,QAAQpE;oBACzBC;oBACAC,uBAAuB,CAAC,CAACsB;oBACzBrB,yBAAyB,CAAC,CAACsB;gBAC7B;gBACAO,iBAAiBmC,UAAU,CAAC,EAAE;gBAC9B/B,iBAAiB+B,UAAU,CAAC,EAAE;gBAE9B,MAAME,6BACJhG,YAAYG,IAAI,CAAC,qBACjBH,YAAYiG,OAAO,CACjB,uBACA,cACA,kBACAC,QAAQ;gBAEZ,MAAM7D,qBACJ2D,6BAA6B,OACzB,CAAC,EAAEG,KAAKC,KAAK,CAACJ,6BAA6B,OAAO,GAAG,CAAC,CAAC,GACvD,CAAC,EAAEG,KAAKC,KAAK,CAACJ,4BAA4B,EAAE,CAAC;gBAEnD3C;gBACArB,aAAa;oBACXC;oBACAC;oBACAT;oBACAU;oBACAC;oBACAC;gBACF;YACF,EAAE,OAAO+B,KAAK;gBACZ,gCAAgC;gBAChCd;gBACAmB,QAAQF,KAAK,CAACH;gBACd3B,QAAQ2C,IAAI,CAAC;YACf;YAEA3B;QACF;QACAjC,OAAO2D,MAAM,CAAC7D,MAAMG;IACtB;IAEA,IAAIF,OAAO;QACT,SAAS8E,iBACPC,UAAkB,EAClBC,QAAoC;YAEpC,MAAMC,KAAK,IAAIhG;YACfgG,GAAGC,KAAK,CAAC;gBACPC,OAAO1F,aAAa2F,GAAG,CAAC,CAACC,OAASvG,KAAKuC,IAAI,CAAC0D,YAAYM;YAC1D;YACAJ,GAAG1B,EAAE,CAAC,UAAUyB;QAClB;QACAF,iBAAiBhF,KAAK,OAAOwF;YAC3B,IAAIpE,QAAQC,GAAG,CAACoE,6BAA6B,EAAE;gBAC7CrG,IAAIsC,IAAI,CACN,CAAC,qFAAqF,CAAC;gBAEzF;YACF;YAEAtC,IAAIyE,IAAI,CACN,CAAC,kBAAkB,EAAE7E,KAAK0G,QAAQ,CAChCF,UACA,+CAA+C,CAAC;YAEpDpE,QAAQ2C,IAAI,CAACzE;QACf;IACF;AACF;AAEA,IAAI8B,QAAQC,GAAG,CAACsE,mBAAmB,IAAIvE,QAAQwE,IAAI,EAAE;IACnDxE,QAAQyE,WAAW,CAAC,WAAW,OAAOC;QACpC,IAAIA,OAAO,OAAOA,OAAOA,IAAIC,iBAAiB,IAAI3E,QAAQwE,IAAI,EAAE;YAC9D,MAAMhE,YAAYkE,IAAIC,iBAAiB;YACvC3E,QAAQwE,IAAI,CAAC;gBAAEI,iBAAiB;YAAK;QACvC;IACF;IACA5E,QAAQwE,IAAI,CAAC;QAAEK,iBAAiB;IAAK;AACvC"}