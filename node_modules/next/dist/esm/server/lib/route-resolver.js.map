{"version": 3, "sources": ["../../../src/server/lib/route-resolver.ts"], "names": ["url", "path", "findPageFile", "getRequestMeta", "setupDebug", "getCloneableBody", "findPagesDir", "setupFsCheck", "proxyRequest", "getResolveRoutes", "PERMANENT_REDIRECT_STATUS", "formatHostname", "signalFromNodeResponse", "getMiddlewareRouteMatcher", "pipeReadable", "debug", "makeResolver", "dir", "nextConfig", "middleware", "hostname", "port", "fs<PERSON><PERSON><PERSON>", "dev", "minimalMode", "config", "appDir", "pagesDir", "fetchHostname", "ensure<PERSON><PERSON>back", "item", "result", "type", "Error", "itemPath", "pageExtensions", "distDir", "join", "middlewareInfo", "name", "paths", "files", "map", "file", "process", "cwd", "wasm", "assets", "length", "middlewareMatcher", "matcher", "regexp", "originalSource", "resolveRoutes", "isNodeDebugging", "initialize", "requestHandler", "req", "res", "headers", "cloneableBody", "run", "require", "edgeFunctionEntry", "request", "method", "i18n", "basePath", "trailingSlash", "body", "signal", "useCache", "onWarning", "console", "warn", "err", "upgradeHandler", "deleteAppClientCache", "deleteCache", "clearModuleContext", "propagateServerField", "resolveRoute", "routeResult", "isUpgradeReq", "matchedOutput", "bodyStream", "statusCode", "parsedUrl", "resHeaders", "finished", "pathname", "query", "key", "Object", "keys", "<PERSON><PERSON><PERSON><PERSON>", "destination", "format", "end", "protocol", "undefined", "cloneBodyStream", "experimental", "proxyTimeout"], "mappings": "AAIA,OAAO,kBAAiB;AACxB,OAAO,yBAAwB;AAE/B,OAAOA,SAAS,MAAK;AACrB,OAAOC,UAAU,OAAM;AACvB,SAASC,YAAY,QAAQ,mBAAkB;AAC/C,SAASC,cAAc,QAAQ,kBAAiB;AAChD,OAAOC,gBAAgB,2BAA0B;AACjD,SAASC,gBAAgB,QAAQ,kBAAiB;AAClD,SAASC,YAAY,QAAQ,2BAA0B;AACvD,SAASC,YAAY,QAAQ,4BAA2B;AACxD,SAASC,YAAY,QAAQ,+BAA8B;AAC3D,SAASC,gBAAgB,QAAQ,gCAA+B;AAChE,SAASC,yBAAyB,QAAQ,6BAA4B;AACtE,SAASC,cAAc,QAAQ,oBAAmB;AAClD,SAASC,sBAAsB,QAAQ,8CAA6C;AACpF,SAASC,yBAAyB,QAAQ,yDAAwD;AAClG,SAASC,YAAY,QAAQ,mBAAkB;AA+B/C,MAAMC,QAAQX,WAAW;AAEzB,OAAO,eAAeY,aACpBC,GAAW,EACXC,UAA8B,EAC9BC,UAA4B,EAC5B,EAAEC,WAAW,WAAW,EAAEC,OAAO,IAAI,EAA0B;IAE/D,MAAMC,YAAY,MAAMf,aAAa;QACnCU;QACAM,KAAK;QACLC,aAAa;QACbC,QAAQP;IACV;IACA,MAAM,EAAEQ,MAAM,EAAEC,QAAQ,EAAE,GAAGrB,aAAaW;IAC1C,mDAAmD;IACnD,MAAMW,gBAAgBjB,eAAeS;IAErCE,UAAUO,cAAc,CAAC,OAAOC;QAC9B,IAAIC,SAAwB;QAE5B,IAAID,KAAKE,IAAI,KAAK,WAAW;YAC3B,IAAI,CAACN,QAAQ;gBACX,MAAM,IAAIO,MAAM;YAClB;YACAF,SAAS,MAAM7B,aACbwB,QACAI,KAAKI,QAAQ,EACbhB,WAAWiB,cAAc,EACzB;QAEJ,OAAO,IAAIL,KAAKE,IAAI,KAAK,YAAY;YACnC,IAAI,CAACL,UAAU;gBACb,MAAM,IAAIM,MAAM;YAClB;YACAF,SAAS,MAAM7B,aACbyB,UACAG,KAAKI,QAAQ,EACbhB,WAAWiB,cAAc,EACzB;QAEJ;QACA,IAAI,CAACJ,QAAQ;YACX,MAAM,IAAIE,MAAM,CAAC,yBAAyB,EAAEH,KAAKE,IAAI,CAAC,CAAC,EAAEF,KAAKI,QAAQ,CAAC,CAAC;QAC1E;IACF;IAEA,MAAME,UAAUnC,KAAKoC,IAAI,CAACpB,KAAKC,WAAWkB,OAAO;IACjD,MAAME,iBAAiBnB,aACnB;QACEoB,MAAM;QACNC,OAAOrB,WAAWsB,KAAK,CAACC,GAAG,CAAC,CAACC,OAAS1C,KAAKoC,IAAI,CAACO,QAAQC,GAAG,IAAIF;QAC/DG,MAAM,EAAE;QACRC,QAAQ,EAAE;IACZ,IACA,CAAC;IAEL,IAAI5B,8BAAAA,WAAYsB,KAAK,CAACO,MAAM,EAAE;YAE1B7B;QADFG,UAAU2B,iBAAiB,GAAGpC,0BAC5BM,EAAAA,sBAAAA,WAAW+B,OAAO,qBAAlB/B,oBAAoBuB,GAAG,CAAC,CAACZ,OAAU,CAAA;gBACjCqB,QAAQrB;gBACRsB,gBAAgBtB;YAClB,CAAA,OAAO;YAAC;gBAAEqB,QAAQ;gBAAMC,gBAAgB;YAAU;SAAE;IAExD;IAEA,MAAMC,gBAAgB5C,iBACpBa,WACAJ,YACA;QACED;QACAI;QACAD;QACAkC,iBAAiB;QACjB/B,KAAK;IACP,GACA;QACE,MAAMgC;YACJ,OAAO;gBACL,MAAMC,gBAAeC,GAAG,EAAEC,GAAG;oBAC3B,IAAI,CAACD,IAAIE,OAAO,CAAC,sBAAsB,EAAE;wBACvC,MAAM,IAAI1B,MAAM,CAAC,yCAAyC,CAAC;oBAC7D;oBAEA,MAAM2B,gBAAgBvD,iBAAiBoD;oBACvC,MAAM,EAAEI,GAAG,EAAE,GACXC,QAAQ;oBAEV,MAAM/B,SAAS,MAAM8B,IAAI;wBACvBzB;wBACAG,MAAMD,eAAeC,IAAI,IAAI;wBAC7BC,OAAOF,eAAeE,KAAK,IAAI,EAAE;wBACjCuB,mBAAmBzB;wBACnB0B,SAAS;4BACPL,SAASF,IAAIE,OAAO;4BACpBM,QAAQR,IAAIQ,MAAM,IAAI;4BACtB/C,YAAY;gCACVgD,MAAMhD,WAAWgD,IAAI;gCACrBC,UAAUjD,WAAWiD,QAAQ;gCAC7BC,eAAelD,WAAWkD,aAAa;4BACzC;4BACApE,KAAK,CAAC,OAAO,EAAE4B,cAAc,CAAC,EAAEP,KAAK,EAAEoC,IAAIzD,GAAG,CAAC,CAAC;4BAChDqE,MAAMT;4BACNU,QAAQ1D,uBAAuB8C;wBACjC;wBACAa,UAAU;wBACVC,WAAWC,QAAQC,IAAI;oBACzB;oBAEA,MAAMC,MAAM,IAAI1C;oBACd0C,IAAY5C,MAAM,GAAGA;oBACvB,MAAM4C;gBACR;gBACA,MAAMC;oBACJ,MAAM,IAAI3C,MAAM,CAAC,0CAA0C,CAAC;gBAC9D;YACF;QACF;QACA4C,yBAAwB;QACxB,MAAMC,gBAAe;QACrB,MAAMC,uBAAsB;QAC5B,MAAMC,yBAAwB;IAChC,GACA,CAAC;IAGH,OAAO,eAAeC,aACpBxB,GAAoB,EACpBC,GAAmB;QAEnB,MAAMwB,cAAc,MAAM7B,cAAc;YACtCI;YACAC;YACAyB,cAAc;YACdb,QAAQ1D,uBAAuB8C;QACjC;QAEA,MAAM,EACJ0B,aAAa,EACbC,UAAU,EACVC,UAAU,EACVC,SAAS,EACTC,UAAU,EACVC,QAAQ,EACT,GAAGP;QAEJnE,MAAM,mBAAmB0C,IAAIzD,GAAG,EAAE;YAChCoF;YACAE;YACAE;YACAH,YAAY,CAAC,CAACA;YACdE,WAAW;gBACTG,UAAUH,UAAUG,QAAQ;gBAC5BC,OAAOJ,UAAUI,KAAK;YACxB;YACAF;QACF;QAEA,KAAK,MAAMG,OAAOC,OAAOC,IAAI,CAACN,cAAc,CAAC,GAAI;YAC/C9B,IAAIqC,SAAS,CAACH,KAAKJ,UAAU,CAACI,IAAI;QACpC;QAEA,IAAI,CAACP,cAAcC,cAAcA,aAAa,OAAOA,aAAa,KAAK;YACrE,MAAMU,cAAchG,IAAIiG,MAAM,CAACV;YAC/B7B,IAAI4B,UAAU,GAAGA;YACjB5B,IAAIqC,SAAS,CAAC,YAAYC;YAE1B,IAAIV,eAAe5E,2BAA2B;gBAC5CgD,IAAIqC,SAAS,CAAC,WAAW,CAAC,MAAM,EAAEC,YAAY,CAAC;YACjD;YACAtC,IAAIwC,GAAG,CAACF;YACR;QACF;QAEA,kCAAkC;QAClC,IAAIX,YAAY;YACd3B,IAAI4B,UAAU,GAAGA,cAAc;YAC/B,OAAO,MAAMxE,aAAauE,YAAY3B;QACxC;QAEA,IAAI+B,YAAYF,UAAUY,QAAQ,EAAE;gBAMhChG;YALF,MAAMK,aACJiD,KACAC,KACA6B,WACAa,YACAjG,kBAAAA,eAAesD,KAAK,4CAApBtD,gBAA6CkG,eAAe,IAC5DnF,WAAWoF,YAAY,CAACC,YAAY;YAEtC;QACF;QAEA7C,IAAIqC,SAAS,CAAC,yBAAyB;QACvCrC,IAAIwC,GAAG;QAEP,OAAO;YACLlE,MAAM;YACNsD,YAAY;YACZ3B,SAAS6B;YACTxF,KAAKA,IAAIiG,MAAM,CAACV;QAClB;IACF;AACF"}