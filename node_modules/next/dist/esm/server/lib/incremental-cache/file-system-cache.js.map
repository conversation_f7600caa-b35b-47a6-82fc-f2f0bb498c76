{"version": 3, "sources": ["../../../../src/server/lib/incremental-cache/file-system-cache.ts"], "names": ["L<PERSON><PERSON><PERSON>", "path", "NEXT_CACHE_TAGS_HEADER", "memoryCache", "tagsManifest", "FileSystemCache", "constructor", "ctx", "fs", "flushToDisk", "serverDistDir", "appDir", "_appDir", "revalidatedTags", "maxMemoryCacheSize", "max", "length", "value", "JSON", "kind", "stringify", "props", "Error", "data", "body", "html", "pageData", "tagsManifestPath", "join", "loadTagsManifest", "parse", "readFileSync", "err", "version", "items", "revalidateTag", "tag", "revalidatedAt", "Date", "now", "mkdir", "dirname", "writeFile", "console", "warn", "get", "key", "tags", "softTags", "fetchCache", "process", "env", "NEXT_RUNTIME", "filePath", "getFsPath", "pathname", "fileData", "readFile", "mtime", "stat", "meta", "replace", "cacheEntry", "lastModified", "getTime", "headers", "status", "_", "isAppPath", "parsedData", "storedTags", "every", "includes", "set", "cacheTags", "<PERSON><PERSON><PERSON><PERSON>", "split", "isStale", "some", "undefined", "combinedTags", "wasRevalidated", "htmlPath"], "mappings": "AAKA,OAAOA,cAAc,+BAA8B;AACnD,OAAOC,UAAU,sCAAqC;AACtD,SAASC,sBAAsB,QAAQ,yBAAwB;AAc/D,IAAIC;AACJ,IAAIC;AAEJ,eAAe,MAAMC;IAQnBC,YAAYC,GAA2B,CAAE;QACvC,IAAI,CAACC,EAAE,GAAGD,IAAIC,EAAE;QAChB,IAAI,CAACC,WAAW,GAAGF,IAAIE,WAAW;QAClC,IAAI,CAACC,aAAa,GAAGH,IAAIG,aAAa;QACtC,IAAI,CAACC,MAAM,GAAG,CAAC,CAACJ,IAAIK,OAAO;QAC3B,IAAI,CAACC,eAAe,GAAGN,IAAIM,eAAe;QAE1C,IAAIN,IAAIO,kBAAkB,IAAI,CAACX,aAAa;YAC1CA,cAAc,IAAIH,SAAS;gBACzBe,KAAKR,IAAIO,kBAAkB;gBAC3BE,QAAO,EAAEC,KAAK,EAAE;wBAcSC;oBAbvB,IAAI,CAACD,OAAO;wBACV,OAAO;oBACT,OAAO,IAAIA,MAAME,IAAI,KAAK,YAAY;wBACpC,OAAOD,KAAKE,SAAS,CAACH,MAAMI,KAAK,EAAEL,MAAM;oBAC3C,OAAO,IAAIC,MAAME,IAAI,KAAK,SAAS;wBACjC,MAAM,IAAIG,MAAM;oBAClB,OAAO,IAAIL,MAAME,IAAI,KAAK,SAAS;wBACjC,OAAOD,KAAKE,SAAS,CAACH,MAAMM,IAAI,IAAI,IAAIP,MAAM;oBAChD,OAAO,IAAIC,MAAME,IAAI,KAAK,SAAS;wBACjC,OAAOF,MAAMO,IAAI,CAACR,MAAM;oBAC1B;oBACA,wCAAwC;oBACxC,OACEC,MAAMQ,IAAI,CAACT,MAAM,GAAIE,CAAAA,EAAAA,kBAAAA,KAAKE,SAAS,CAACH,MAAMS,QAAQ,sBAA7BR,gBAAgCF,MAAM,KAAI,CAAA;gBAEnE;YACF;QACF;QACA,IAAI,IAAI,CAACN,aAAa,IAAI,IAAI,CAACF,EAAE,EAAE;YACjC,IAAI,CAACmB,gBAAgB,GAAG1B,KAAK2B,IAAI,CAC/B,IAAI,CAAClB,aAAa,EAClB,MACA,SACA,eACA;YAEF,IAAI,CAACmB,gBAAgB;QACvB;IACF;IAEQA,mBAAmB;QACzB,IAAI,CAAC,IAAI,CAACF,gBAAgB,IAAI,CAAC,IAAI,CAACnB,EAAE,IAAIJ,cAAc;QACxD,IAAI;YACFA,eAAec,KAAKY,KAAK,CACvB,IAAI,CAACtB,EAAE,CAACuB,YAAY,CAAC,IAAI,CAACJ,gBAAgB,EAAE;QAEhD,EAAE,OAAOK,KAAU;YACjB5B,eAAe;gBAAE6B,SAAS;gBAAGC,OAAO,CAAC;YAAE;QACzC;IACF;IAEA,MAAaC,cAAcC,GAAW,EAAE;QACtC,kDAAkD;QAClD,wDAAwD;QACxD,2CAA2C;QAC3C,IAAI,CAACP,gBAAgB;QACrB,IAAI,CAACzB,gBAAgB,CAAC,IAAI,CAACuB,gBAAgB,EAAE;YAC3C;QACF;QAEA,MAAMJ,OAAOnB,aAAa8B,KAAK,CAACE,IAAI,IAAI,CAAC;QACzCb,KAAKc,aAAa,GAAGC,KAAKC,GAAG;QAC7BnC,aAAa8B,KAAK,CAACE,IAAI,GAAGb;QAE1B,IAAI;YACF,MAAM,IAAI,CAACf,EAAE,CAACgC,KAAK,CAACvC,KAAKwC,OAAO,CAAC,IAAI,CAACd,gBAAgB;YACtD,MAAM,IAAI,CAACnB,EAAE,CAACkC,SAAS,CACrB,IAAI,CAACf,gBAAgB,EACrBT,KAAKE,SAAS,CAAChB,gBAAgB,CAAC;QAEpC,EAAE,OAAO4B,KAAU;YACjBW,QAAQC,IAAI,CAAC,mCAAmCZ;QAClD;IACF;IAEA,MAAaa,IACXC,GAAW,EACX,EACEC,IAAI,EACJC,QAAQ,EACRC,UAAU,EAKX,GAAG,CAAC,CAAC,EACN;YAiHI1B,aA4BQA;QA5IZ,IAAIA,OAAOpB,+BAAAA,YAAa0C,GAAG,CAACC;QAE5B,qCAAqC;QACrC,IAAI,CAACvB,QAAQ2B,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;YAChD,IAAI;gBACF,MAAM,EAAEC,QAAQ,EAAE,GAAG,MAAM,IAAI,CAACC,SAAS,CAAC;oBACxCC,UAAU,CAAC,EAAET,IAAI,KAAK,CAAC;oBACvBnC,QAAQ;gBACV;gBACA,MAAM6C,WAAW,MAAM,IAAI,CAAChD,EAAE,CAACiD,QAAQ,CAACJ;gBACxC,MAAM,EAAEK,KAAK,EAAE,GAAG,MAAM,IAAI,CAAClD,EAAE,CAACmD,IAAI,CAACN;gBAErC,MAAMO,OAAO1C,KAAKY,KAAK,CACrB,MAAM,IAAI,CAACtB,EAAE,CAACiD,QAAQ,CAACJ,SAASQ,OAAO,CAAC,WAAW,UAAU;gBAG/D,MAAMC,aAAgC;oBACpCC,cAAcL,MAAMM,OAAO;oBAC3B/C,OAAO;wBACLE,MAAM;wBACNK,MAAMgC;wBACNS,SAASL,KAAKK,OAAO;wBACrBC,QAAQN,KAAKM,MAAM;oBACrB;gBACF;gBACA,OAAOJ;YACT,EAAE,OAAOK,GAAG;YACV,oCAAoC;YACtC;YAEA,IAAI;gBACF,MAAM,EAAEd,QAAQ,EAAEe,SAAS,EAAE,GAAG,MAAM,IAAI,CAACd,SAAS,CAAC;oBACnDC,UAAUN,aAAaH,MAAM,CAAC,EAAEA,IAAI,KAAK,CAAC;oBAC1CG;gBACF;gBACA,MAAMO,WAAW,MAAM,IAAI,CAAChD,EAAE,CAACiD,QAAQ,CAACJ,UAAU;gBAClD,MAAM,EAAEK,KAAK,EAAE,GAAG,MAAM,IAAI,CAAClD,EAAE,CAACmD,IAAI,CAACN;gBAErC,IAAIJ,YAAY;wBAQV1B;oBAPJ,MAAMwC,eAAeL,MAAMM,OAAO;oBAClC,MAAMK,aAA+BnD,KAAKY,KAAK,CAAC0B;oBAChDjC,OAAO;wBACLwC;wBACA9C,OAAOoD;oBACT;oBAEA,IAAI9C,EAAAA,eAAAA,KAAKN,KAAK,qBAAVM,aAAYJ,IAAI,MAAK,SAAS;4BACbI,kBAAAA;wBAAnB,MAAM+C,cAAa/C,eAAAA,KAAKN,KAAK,sBAAVM,mBAAAA,aAAYA,IAAI,qBAAhBA,iBAAkBwB,IAAI;wBAEzC,iDAAiD;wBACjD,8CAA8C;wBAC9C,gCAAgC;wBAChC,IAAI,EAACA,wBAAAA,KAAMwB,KAAK,CAAC,CAACnC,MAAQkC,8BAAAA,WAAYE,QAAQ,CAACpC,QAAO;4BACpD,MAAM,IAAI,CAACqC,GAAG,CAAC3B,KAAKvB,KAAKN,KAAK,EAAE;gCAAE8B;4BAAK;wBACzC;oBACF;gBACF,OAAO;oBACL,MAAMrB,WAAW0C,YACb,MAAM,IAAI,CAAC5D,EAAE,CAACiD,QAAQ,CACpB,AACE,CAAA,MAAM,IAAI,CAACH,SAAS,CAAC;wBACnBC,UAAU,CAAC,EAAET,IAAI,IAAI,CAAC;wBACtBnC,QAAQ;oBACV,EAAC,EACD0C,QAAQ,EACV,UAEFnC,KAAKY,KAAK,CACR,MAAM,IAAI,CAACtB,EAAE,CAACiD,QAAQ,CACpB,AACE,CAAA,MAAM,IAAI,CAACH,SAAS,CAAC;wBACnBC,UAAU,CAAC,EAAET,IAAI,KAAK,CAAC;wBACvBnC,QAAQ;oBACV,EAAC,EACD0C,QAAQ,EACV;oBAIR,IAAIO,OAA2D,CAAC;oBAEhE,IAAIQ,WAAW;wBACb,IAAI;4BACFR,OAAO1C,KAAKY,KAAK,CACf,MAAM,IAAI,CAACtB,EAAE,CAACiD,QAAQ,CACpBJ,SAASQ,OAAO,CAAC,WAAW,UAC5B;wBAGN,EAAE,OAAM,CAAC;oBACX;oBAEAtC,OAAO;wBACLwC,cAAcL,MAAMM,OAAO;wBAC3B/C,OAAO;4BACLE,MAAM;4BACNM,MAAM+B;4BACN9B;4BACAuC,SAASL,KAAKK,OAAO;4BACrBC,QAAQN,KAAKM,MAAM;wBACrB;oBACF;gBACF;gBAEA,IAAI3C,MAAM;oBACRpB,+BAAAA,YAAasE,GAAG,CAAC3B,KAAKvB;gBACxB;YACF,EAAE,OAAO4C,GAAG;YACV,+BAA+B;YACjC;QACF;QAEA,IAAI5C,CAAAA,yBAAAA,cAAAA,KAAMN,KAAK,qBAAXM,YAAaJ,IAAI,MAAK,QAAQ;gBAEbI;YADnB,IAAImD;YACJ,MAAMC,cAAapD,sBAAAA,KAAKN,KAAK,CAACgD,OAAO,qBAAlB1C,mBAAoB,CAACrB,uBAAuB;YAE/D,IAAI,OAAOyE,eAAe,UAAU;gBAClCD,YAAYC,WAAWC,KAAK,CAAC;YAC/B;YAEA,IAAIF,6BAAAA,UAAW1D,MAAM,EAAE;gBACrB,IAAI,CAACa,gBAAgB;gBAErB,MAAMgD,UAAUH,UAAUI,IAAI,CAAC,CAAC1C;wBAE5BhC;oBADF,OACEA,CAAAA,iCAAAA,0BAAAA,aAAc8B,KAAK,CAACE,IAAI,qBAAxBhC,wBAA0BiC,aAAa,KACvCjC,CAAAA,gCAAAA,aAAc8B,KAAK,CAACE,IAAI,CAACC,aAAa,KACnCd,CAAAA,CAAAA,wBAAAA,KAAMwC,YAAY,KAAIzB,KAAKC,GAAG,EAAC;gBAEtC;gBAEA,kDAAkD;gBAClD,uDAAuD;gBACvD,wDAAwD;gBACxD,IAAIsC,SAAS;oBACXtD,OAAOwD;gBACT;YACF;QACF;QAEA,IAAIxD,QAAQA,CAAAA,yBAAAA,eAAAA,KAAMN,KAAK,qBAAXM,aAAaJ,IAAI,MAAK,SAAS;YACzC,IAAI,CAACU,gBAAgB;YAErB,MAAMmD,eAAe;mBAAKjC,QAAQ,EAAE;mBAAOC,YAAY,EAAE;aAAE;YAE3D,MAAMiC,iBAAiBD,aAAaF,IAAI,CAAC,CAAC1C;oBAMtChC;gBALF,IAAI,IAAI,CAACS,eAAe,CAAC2D,QAAQ,CAACpC,MAAM;oBACtC,OAAO;gBACT;gBAEA,OACEhC,CAAAA,iCAAAA,0BAAAA,aAAc8B,KAAK,CAACE,IAAI,qBAAxBhC,wBAA0BiC,aAAa,KACvCjC,CAAAA,gCAAAA,aAAc8B,KAAK,CAACE,IAAI,CAACC,aAAa,KACnCd,CAAAA,CAAAA,wBAAAA,KAAMwC,YAAY,KAAIzB,KAAKC,GAAG,EAAC;YAEtC;YACA,gDAAgD;YAChD,wCAAwC;YACxC,IAAI0C,gBAAgB;gBAClB1D,OAAOwD;YACT;QACF;QAEA,OAAOxD,QAAQ;IACjB;IAEA,MAAakD,IACX3B,GAAW,EACXvB,IAAgC,EAChChB,GAEC,EACD;QACAJ,+BAAAA,YAAasE,GAAG,CAAC3B,KAAK;YACpB7B,OAAOM;YACPwC,cAAczB,KAAKC,GAAG;QACxB;QACA,IAAI,CAAC,IAAI,CAAC9B,WAAW,EAAE;QAEvB,IAAIc,CAAAA,wBAAAA,KAAMJ,IAAI,MAAK,SAAS;YAC1B,MAAM,EAAEkC,QAAQ,EAAE,GAAG,MAAM,IAAI,CAACC,SAAS,CAAC;gBACxCC,UAAU,CAAC,EAAET,IAAI,KAAK,CAAC;gBACvBnC,QAAQ;YACV;YACA,MAAM,IAAI,CAACH,EAAE,CAACgC,KAAK,CAACvC,KAAKwC,OAAO,CAACY;YACjC,MAAM,IAAI,CAAC7C,EAAE,CAACkC,SAAS,CAACW,UAAU9B,KAAKC,IAAI;YAC3C,MAAM,IAAI,CAAChB,EAAE,CAACkC,SAAS,CACrBW,SAASQ,OAAO,CAAC,WAAW,UAC5B3C,KAAKE,SAAS,CAAC;gBAAE6C,SAAS1C,KAAK0C,OAAO;gBAAEC,QAAQ3C,KAAK2C,MAAM;YAAC;YAE9D;QACF;QAEA,IAAI3C,CAAAA,wBAAAA,KAAMJ,IAAI,MAAK,QAAQ;YACzB,MAAMiD,YAAY,OAAO7C,KAAKG,QAAQ,KAAK;YAC3C,MAAM,EAAE2B,UAAU6B,QAAQ,EAAE,GAAG,MAAM,IAAI,CAAC5B,SAAS,CAAC;gBAClDC,UAAU,CAAC,EAAET,IAAI,KAAK,CAAC;gBACvBnC,QAAQyD;YACV;YACA,MAAM,IAAI,CAAC5D,EAAE,CAACgC,KAAK,CAACvC,KAAKwC,OAAO,CAACyC;YACjC,MAAM,IAAI,CAAC1E,EAAE,CAACkC,SAAS,CAACwC,UAAU3D,KAAKE,IAAI;YAE3C,MAAM,IAAI,CAACjB,EAAE,CAACkC,SAAS,CACrB,AACE,CAAA,MAAM,IAAI,CAACY,SAAS,CAAC;gBACnBC,UAAU,CAAC,EAAET,IAAI,CAAC,EAAEsB,YAAY,QAAQ,OAAO,CAAC;gBAChDzD,QAAQyD;YACV,EAAC,EACDf,QAAQ,EACVe,YAAY7C,KAAKG,QAAQ,GAAGR,KAAKE,SAAS,CAACG,KAAKG,QAAQ;YAG1D,IAAIH,KAAK0C,OAAO,IAAI1C,KAAK2C,MAAM,EAAE;gBAC/B,MAAM,IAAI,CAAC1D,EAAE,CAACkC,SAAS,CACrBwC,SAASrB,OAAO,CAAC,WAAW,UAC5B3C,KAAKE,SAAS,CAAC;oBACb6C,SAAS1C,KAAK0C,OAAO;oBACrBC,QAAQ3C,KAAK2C,MAAM;gBACrB;YAEJ;QACF,OAAO,IAAI3C,CAAAA,wBAAAA,KAAMJ,IAAI,MAAK,SAAS;YACjC,MAAM,EAAEkC,QAAQ,EAAE,GAAG,MAAM,IAAI,CAACC,SAAS,CAAC;gBACxCC,UAAUT;gBACVG,YAAY;YACd;YACA,MAAM,IAAI,CAACzC,EAAE,CAACgC,KAAK,CAACvC,KAAKwC,OAAO,CAACY;YACjC,MAAM,IAAI,CAAC7C,EAAE,CAACkC,SAAS,CACrBW,UACAnC,KAAKE,SAAS,CAAC;gBACb,GAAGG,IAAI;gBACPwB,MAAMxC,IAAIwC,IAAI;YAChB;QAEJ;IACF;IAEA,MAAcO,UAAU,EACtBC,QAAQ,EACR5C,MAAM,EACNsC,UAAU,EAKX,EAGE;QACD,IAAIA,YAAY;YACd,6DAA6D;YAC7D,iBAAiB;YACjB,OAAO;gBACLI,UAAUpD,KAAK2B,IAAI,CACjB,IAAI,CAAClB,aAAa,EAClB,MACA,SACA,eACA6C;gBAEFa,WAAW;YACb;QACF;QACA,IAAIA,YAAY;QAChB,IAAIf,WAAWpD,KAAK2B,IAAI,CAAC,IAAI,CAAClB,aAAa,EAAE,SAAS6C;QAEtD,IAAI,CAAC,IAAI,CAAC5C,MAAM,IAAIA,WAAW,OAC7B,OAAO;YACL0C;YACAe;QACF;QACF,IAAI;YACF,MAAM,IAAI,CAAC5D,EAAE,CAACiD,QAAQ,CAACJ;YACvB,OAAO;gBACLA;gBACAe;YACF;QACF,EAAE,OAAOpC,KAAK;YACZ,OAAO;gBACLqB,UAAUpD,KAAK2B,IAAI,CAAC,IAAI,CAAClB,aAAa,EAAE,OAAO6C;gBAC/Ca,WAAW;YACb;QACF;IACF;AACF"}