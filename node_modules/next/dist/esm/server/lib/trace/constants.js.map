{"version": 3, "sources": ["../../../../src/server/lib/trace/constants.ts"], "names": ["BaseServerSpan", "handleRequest", "run", "pipe", "getStaticHTML", "render", "renderToResponseWithComponents", "renderToResponse", "renderToHTML", "renderError", "renderErrorToResponse", "renderErrorToHTML", "render404", "LoadComponentsSpan", "loadDefaultErrorComponents", "loadComponents", "NextServerSpan", "getRequestHandler", "getServer", "getServerRequestHandler", "createServer", "NextNodeServerSpan", "compression", "getBuildId", "generateStaticRoutes", "generateFsStaticRoutes", "generatePublicRoutes", "generateImageRoutes", "sendRenderResult", "proxyRequest", "run<PERSON><PERSON>", "renderHTML", "imageOptimizer", "getPagePath", "getRoutesManifest", "findPageComponents", "getFontManifest", "getServerComponentManifest", "route", "onProxyReq", "apiResolver", "internalFetch", "StartServerSpan", "startServer", "RenderSpan", "getServerSideProps", "getStaticProps", "renderToString", "renderDocument", "createBodyResult", "AppRenderSpan", "renderToReadableStream", "getBodyResult", "fetch", "RouterSpan", "executeRoute", "NodeSpan", "<PERSON><PERSON><PERSON><PERSON>", "AppRouteRouteHandlersSpan", "ResolveMetadataSpan", "generateMetadata", "NextVanillaSpanAllowlist"], "mappings": "AAAA;;;;;EAKE,GAEF,4CAA4C;AAC5C,4BAA4B,OAE5B;UAAKA,cAAc;IAAdA,eACHC,mBAAgB;IADbD,eAEHE,SAAM;IAFHF,eAGHG,UAAO;IAHJH,eAIHI,mBAAgB;IAJbJ,eAKHK,YAAS;IALNL,eAMHM,oCAAiC;IAN9BN,eAOHO,sBAAmB;IAPhBP,eAQHQ,kBAAe;IARZR,eASHS,iBAAc;IATXT,eAUHU,2BAAwB;IAVrBV,eAWHW,uBAAoB;IAXjBX,eAYHY,eAAY;GAZTZ,mBAAAA;IAeL;UAAKa,kBAAkB;IAAlBA,mBACHC,gCAA6B;IAD1BD,mBAEHE,oBAAiB;GAFdF,uBAAAA;IAKL;UAAKG,cAAc;IAAdA,eACHC,uBAAoB;IADjBD,eAEHE,eAAY;IAFTF,eAGHG,6BAA0B;IAHvBH,eAIHI,kBAAe;GAJZJ,mBAAAA;IAOL;UAAKK,kBAAkB;IAAlBA,mBACHC,iBAAc;IADXD,mBAEHE,gBAAa;IAFVF,mBAGHG,0BAAuB;IAHpBH,mBAIHI,4BAAyB;IAJtBJ,mBAKHK,0BAAuB;IALpBL,mBAMHM,yBAAsB;IANnBN,mBAOHO,sBAAmB;IAPhBP,mBAQHQ,kBAAe;IARZR,mBASHS,YAAS;IATNT,mBAUHhB,YAAS;IAVNgB,mBAWHU,gBAAa;IAXVV,mBAYHW,oBAAiB;IAZdX,mBAaHY,iBAAc;IAbXZ,mBAcHa,uBAAoB;IAdjBb,mBAeHc,wBAAqB;IAflBd,mBAgBHe,qBAAkB;IAhBff,mBAiBHgB,gCAA6B;IAjB1BhB,mBAkBHJ,uBAAoB;IAlBjBI,mBAmBHb,kBAAe;IAnBZa,mBAoBHZ,iBAAc;IApBXY,mBAqBHV,uBAAoB;IArBjBU,mBAsBHT,eAAY;IAtBTS,mBAwBH,wDAAwD;IACxDiB,WAAAA;IAzBGjB,mBA0BHkB,gBAAAA;IA1BGlB,mBA2BHmB,iBAAAA;IA3BGnB,mBA4BHoB,mBAAAA;GA5BGpB,uBAAAA;IA+BL;UAAKqB,eAAe;IAAfA,gBACHC,iBAAc;GADXD,oBAAAA;IAIL;UAAKE,UAAU;IAAVA,WACHC,wBAAqB;IADlBD,WAEHE,oBAAiB;IAFdF,WAGHG,oBAAiB;IAHdH,WAIHI,oBAAiB;IAJdJ,WAKHK,sBAAmB;GALhBL,eAAAA;IAQL;UAAKM,aAAa;IAAbA,cACHH,oBAAiB;IADdG,cAEHC,4BAAyB;IAFtBD,cAGHE,mBAAgB;IAHbF,cAIHG,WAAQ;GAJLH,kBAAAA;IAOL;UAAKI,UAAU;IAAVA,WACHC,kBAAe;GADZD,eAAAA;IAIL;UAAKE,QAAQ;IAARA,SACHC,gBAAa;GADVD,aAAAA;IAIL;UAAKE,yBAAyB;IAAzBA,0BACHD,gBAAa;GADVC,8BAAAA;IAIL;UAAKC,mBAAmB;IAAnBA,oBACHC,sBAAmB;GADhBD,wBAAAA;AAiBL,0EAA0E;AAC1E,OAAO,MAAME,2BAA2B;IA1GtB;IA8DK;IACJ;IAUT;IADQ;IAPC;IAgBJ;IAIA;IAIM;CA2BpB,CAAA;AAED,SACE7D,cAAc,EACda,kBAAkB,EAClBG,cAAc,EACdK,kBAAkB,EAClBqB,eAAe,EACfE,UAAU,EACVU,UAAU,EACVJ,aAAa,EACbM,QAAQ,EACRE,yBAAyB,EACzBC,mBAAmB,KACpB"}