{"version": 3, "sources": ["../../../src/server/app-render/create-component-tree.tsx"], "names": ["React", "isClientReference", "getLayoutOrPageModule", "interopDefault", "preloadComponent", "addSearchParamsIfPageSegment", "parseLoaderTree", "createComponentAndStyles", "getLayerAssets", "hasLoadingComponentInTree", "createComponentTree", "createSegmentPath", "loaderTree", "tree", "parentParams", "firstItem", "rootLayoutIncluded", "injectedCSS", "injectedFontPreloadTags", "asNotFound", "metadataOutlet", "ctx", "renderOpts", "nextConfigOutput", "staticGenerationStore", "componentMod", "staticGenerationBailout", "NotFoundBoundary", "LayoutRouter", "RenderFromTemplateContext", "StaticGenerationSearchParamsBailoutProvider", "serverHooks", "DynamicServerError", "pagePath", "getDynamicParamFromSegment", "query", "isPrefetch", "searchParamsProps", "page", "layoutOrPagePath", "segment", "components", "parallelRoutes", "layout", "template", "error", "loading", "notFound", "injectedCSSWithCurrentLayout", "Set", "injectedFontPreloadTagsWithCurrentLayout", "styles", "Template", "templateStyles", "filePath", "getComponent", "Fragment", "ErrorComponent", "errorStyles", "Loading", "loadingStyles", "isLayout", "isPage", "layoutOrPageMod", "rootLayoutAtThisLevel", "rootLayoutIncludedAtThisLevelOrAbove", "NotFound", "notFoundStyles", "dynamic", "forceDynamic", "dynamicShouldError", "link", "forceStatic", "fetchCache", "revalidate", "defaultRevalidate", "isStaticGeneration", "dynamicUsageDescription", "dynamicUsageErr", "LayoutOrPage", "undefined", "Component", "parallelKeys", "Object", "keys", "hasSlot<PERSON>ey", "length", "componentProps", "NotFoundComponent", "RootLayoutComponent", "process", "env", "NODE_ENV", "isValidElementType", "require", "Error", "segmentParam", "currentParams", "value", "param", "actualSegment", "treeSegment", "parallelRouteMap", "Promise", "all", "map", "parallelRouteKey", "isChildrenRoute<PERSON>ey", "currentSegmentPath", "parallelRoute", "childSegment", "childSegmentParam", "notFoundComponent", "getParallelRoutePair", "currentChildProp", "currentStyles", "parallel<PERSON><PERSON>er<PERSON>ey", "segmentPath", "hasLoading", "Boolean", "childProp", "childElement", "childPropSegment", "ChildComponent", "childComponentStyles", "child", "current", "parallelRouteComponents", "reduce", "list", "Comp", "children", "isClientComponent", "meta", "name", "content", "props", "params", "resolve", "then", "propsForComponent"], "mappings": "AACA,OAAOA,WAAW,QAAO;AACzB,SAASC,iBAAiB,QAAQ,6BAA4B;AAC9D,SAASC,qBAAqB,QAAQ,wBAAuB;AAE7D,SAASC,cAAc,QAAQ,oBAAmB;AAClD,SAASC,gBAAgB,QAAQ,sBAAqB;AACtD,SAASC,4BAA4B,QAAQ,gDAA+C;AAC5F,SAASC,eAAe,QAAQ,sBAAqB;AAErD,SAASC,wBAAwB,QAAQ,gCAA+B;AACxE,SAASC,cAAc,QAAQ,qBAAoB;AACnD,SAASC,yBAAyB,QAAQ,kCAAiC;AAE3E;;CAEC,GAED,OAAO,eAAeC,oBAAoB,EACxCC,iBAAiB,EACjBC,YAAYC,IAAI,EAChBC,YAAY,EACZC,SAAS,EACTC,kBAAkB,EAClBC,WAAW,EACXC,uBAAuB,EACvBC,UAAU,EACVC,cAAc,EACdC,GAAG,EAYJ;IAIC,MAAM,EACJC,YAAY,EAAEC,gBAAgB,EAAE,EAChCC,qBAAqB,EACrBC,cAAc,EACZC,uBAAuB,EACvBC,gBAAgB,EAChBC,YAAY,EACZC,yBAAyB,EACzBC,2CAA2C,EAC3CC,aAAa,EAAEC,kBAAkB,EAAE,EACpC,EACDC,QAAQ,EACRC,0BAA0B,EAC1BC,KAAK,EACLC,UAAU,EACVC,iBAAiB,EAClB,GAAGhB;IAEJ,MAAM,EAAEiB,IAAI,EAAEC,gBAAgB,EAAEC,OAAO,EAAEC,UAAU,EAAEC,cAAc,EAAE,GACnEpC,gBAAgBO;IAElB,MAAM,EAAE8B,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,OAAO,EAAE,aAAaC,QAAQ,EAAE,GAAGN;IAEpE,MAAMO,+BAA+B,IAAIC,IAAIhC;IAC7C,MAAMiC,2CAA2C,IAAID,IACnD/B;IAGF,MAAMiC,SAAS3C,eAAe;QAC5Ba;QACAkB;QACAtB,aAAa+B;QACb9B,yBAAyBgC;IAC3B;IAEA,MAAM,CAACE,UAAUC,eAAe,GAAGT,WAC/B,MAAMrC,yBAAyB;QAC7Bc;QACAiC,UAAUV,QAAQ,CAAC,EAAE;QACrBW,cAAcX,QAAQ,CAAC,EAAE;QACzB3B,aAAa+B;IACf,KACA;QAAChD,MAAMwD,QAAQ;KAAC;IAEpB,MAAM,CAACC,gBAAgBC,YAAY,GAAGb,QAClC,MAAMtC,yBAAyB;QAC7Bc;QACAiC,UAAUT,KAAK,CAAC,EAAE;QAClBU,cAAcV,KAAK,CAAC,EAAE;QACtB5B,aAAa+B;IACf,KACA,EAAE;IAEN,MAAM,CAACW,SAASC,cAAc,GAAGd,UAC7B,MAAMvC,yBAAyB;QAC7Bc;QACAiC,UAAUR,OAAO,CAAC,EAAE;QACpBS,cAAcT,OAAO,CAAC,EAAE;QACxB7B,aAAa+B;IACf,KACA,EAAE;IAEN,MAAMa,WAAW,OAAOlB,WAAW;IACnC,MAAMmB,SAAS,OAAOxB,SAAS;IAC/B,MAAM,CAACyB,gBAAgB,GAAG,MAAM7D,sBAAsBW;IAEtD;;GAEC,GACD,MAAMmD,wBAAwBH,YAAY,CAAC7C;IAC3C;;GAEC,GACD,MAAMiD,uCACJjD,sBAAsBgD;IAExB,MAAM,CAACE,UAAUC,eAAe,GAAGpB,WAC/B,MAAMxC,yBAAyB;QAC7Bc;QACAiC,UAAUP,QAAQ,CAAC,EAAE;QACrBQ,cAAcR,QAAQ,CAAC,EAAE;QACzB9B,aAAa+B;IACf,KACA,EAAE;IAEN,IAAIoB,UAAUL,mCAAAA,gBAAiBK,OAAO;IAEtC,IAAI7C,qBAAqB,UAAU;QACjC,IAAI,CAAC6C,WAAWA,YAAY,QAAQ;YAClCA,UAAU;QACZ,OAAO,IAAIA,YAAY,iBAAiB;YACtC5C,sBAAsB6C,YAAY,GAAG;YACrC7C,sBAAsB8C,kBAAkB,GAAG;YAC3C5C,wBAAwB,CAAC,cAAc,CAAC,EAAE;gBACxC0C;gBACAG,MAAM;YACR;QACF;IACF;IAEA,IAAI,OAAOH,YAAY,UAAU;QAC/B,sDAAsD;QACtD,sDAAsD;QACtD,YAAY;QACZ,IAAIA,YAAY,SAAS;YACvB5C,sBAAsB8C,kBAAkB,GAAG;QAC7C,OAAO,IAAIF,YAAY,iBAAiB;YACtC5C,sBAAsB6C,YAAY,GAAG;YACrC3C,wBAAwB,CAAC,aAAa,CAAC,EAAE;gBAAE0C;YAAQ;QACrD,OAAO;YACL5C,sBAAsB8C,kBAAkB,GAAG;YAC3C,IAAIF,YAAY,gBAAgB;gBAC9B5C,sBAAsBgD,WAAW,GAAG;YACtC,OAAO;gBACLhD,sBAAsBgD,WAAW,GAAG;YACtC;QACF;IACF;IAEA,IAAI,QAAOT,mCAAAA,gBAAiBU,UAAU,MAAK,UAAU;QACnDjD,sBAAsBiD,UAAU,GAAGV,mCAAAA,gBAAiBU,UAAU;IAChE;IAEA,IAAI,QAAOV,mCAAAA,gBAAiBW,UAAU,MAAK,UAAU;QACnDrD,IAAIsD,iBAAiB,GAAGZ,gBAAgBW,UAAU;QAElD,IACE,OAAOlD,sBAAsBkD,UAAU,KAAK,eAC3C,OAAOlD,sBAAsBkD,UAAU,KAAK,YAC3ClD,sBAAsBkD,UAAU,GAAGrD,IAAIsD,iBAAiB,EAC1D;YACAnD,sBAAsBkD,UAAU,GAAGrD,IAAIsD,iBAAiB;QAC1D;QAEA,IACEnD,sBAAsBoD,kBAAkB,IACxCvD,IAAIsD,iBAAiB,KAAK,GAC1B;YACA,MAAME,0BAA0B,CAAC,yBAAyB,EAAErC,QAAQ,CAAC;YACrEhB,sBAAsBqD,uBAAuB,GAAGA;YAEhD,MAAM,IAAI7C,mBAAmB6C;QAC/B;IACF;IAEA,IAAIrD,yCAAAA,sBAAuBsD,eAAe,EAAE;QAC1C,MAAMtD,sBAAsBsD,eAAe;IAC7C;IAEA,MAAMC,eAAehB,kBACjB5D,eAAe4D,mBACfiB;IAEJ;;GAEC,GACD,IAAIC,YAAYF;IAChB,MAAMG,eAAeC,OAAOC,IAAI,CAAC1C;IACjC,MAAM2C,aAAaH,aAAaI,MAAM,GAAG;IAEzC,IAAID,cAAcrB,uBAAuB;QACvCiB,YAAY,CAACM;YACX,MAAMC,oBAAoBtB;YAC1B,MAAMuB,sBAAsBV;YAC5B,qBACE,oBAACpD;gBACCoB,wBACE,0CACGI,sBACD,oBAACsC,2BACEtB,8BACD,oBAACqB;6BAKP,oBAACC,qBAAwBF;QAG/B;IACF;IAEA,IAAIG,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,MAAM,EAAEC,kBAAkB,EAAE,GAAGC,QAAQ;QACvC,IACE,AAAChC,CAAAA,UAAU,OAAOmB,cAAc,WAAU,KAC1C,CAACY,mBAAmBZ,YACpB;YACA,MAAM,IAAIc,MACR,CAAC,sDAAsD,EAAE9D,SAAS,CAAC,CAAC;QAExE;QAEA,IACE,OAAOwB,mBAAmB,eAC1B,CAACoC,mBAAmBpC,iBACpB;YACA,MAAM,IAAIsC,MACR,CAAC,8DAA8D,EAAEvD,QAAQ,CAAC;QAE9E;QAEA,IAAI,OAAOmB,YAAY,eAAe,CAACkC,mBAAmBlC,UAAU;YAClE,MAAM,IAAIoC,MACR,CAAC,0DAA0D,EAAEvD,QAAQ,CAAC;QAE1E;QAEA,IAAI,OAAO0B,aAAa,eAAe,CAAC2B,mBAAmB3B,WAAW;YACpE,MAAM,IAAI6B,MACR,CAAC,2DAA2D,EAAEvD,QAAQ,CAAC;QAE3E;IACF;IAEA,iCAAiC;IACjC,MAAMwD,eAAe9D,2BAA2BM;IAChD;;GAEC,GACD,MAAMyD,gBACJ,mDAAmD;IACnDD,gBAAgBA,aAAaE,KAAK,KAAK,OACnC;QACE,GAAGpF,YAAY;QACf,CAACkF,aAAaG,KAAK,CAAC,EAAEH,aAAaE,KAAK;IAC1C,IAEApF;IACN,4BAA4B;IAC5B,MAAMsF,gBAAgBJ,eAAeA,aAAaK,WAAW,GAAG7D;IAEhE,mHAAmH;IACnH,MAAM8D,mBAAmB,MAAMC,QAAQC,GAAG,CACxCrB,OAAOC,IAAI,CAAC1C,gBAAgB+D,GAAG,CAC7B,OAAOC;QACL,MAAMC,qBAAqBD,qBAAqB;QAChD,MAAME,qBAAwC7F,YAC1C;YAAC2F;SAAiB,GAClB;YAACN;YAAeM;SAAiB;QAErC,MAAMG,gBAAgBnE,cAAc,CAACgE,iBAAiB;QAEtD,MAAMI,eAAeD,aAAa,CAAC,EAAE;QACrC,MAAME,oBAAoB7E,2BAA2B4E;QACrD,MAAME,oBACJ9C,YAAYyC,mCAAqB,oBAACzC,kBAAcc;QAElD,SAASiC,qBACPC,gBAA2B,EAC3BC,aAA8B;YAE9B,4CAA4C;YAC5C,OAAO;gBACLT;8BACA,oBAAC9E;oBACCwF,mBAAmBV;oBACnBW,aAAa1G,kBAAkBiG;oBAC/B9D,SAASa,wBAAU,oBAACA,iBAAaqB;oBACjCpB,eAAeA;oBACf,sKAAsK;oBACtK0D,YAAYC,QAAQ5D;oBACpBd,OAAOY;oBACPC,aAAaA;oBACbd,wBACE,oBAACQ,8BACC,oBAACvB;oBAGLwB,gBAAgBA;oBAChBN,UAAUiE;oBACV7C,gBAAgBA;oBAChBqD,WAAWN;oBACX/D,QAAQgE;;aAEX;QACH;QAEA,yEAAyE;QACzE,gDAAgD;QAChD,wEAAwE;QACxE,IAAIA,gBAAgBnC;QACpB,IAAIyC,eAAe;QACnB,MAAMC,mBAAmBrH,6BACvB0G,oBAAoBA,kBAAkBV,WAAW,GAAGS,cACpD3E;QAEF,IACE,CACEC,CAAAA,cACCuB,CAAAA,WAAW,CAAClD,0BAA0BoG,cAAa,CAAC,GAEvD;YACA,6BAA6B;YAC7B,MAAM,EAAE5B,WAAW0C,cAAc,EAAExE,QAAQyE,oBAAoB,EAAE,GAC/D,MAAMlH,oBAAoB;gBACxBC,mBAAmB,CAACkH;oBAClB,OAAOlH,kBAAkB;2BAAIiG;2BAAuBiB;qBAAM;gBAC5D;gBACAjH,YAAYiG;gBACZ/F,cAAcmF;gBACdjF,oBAAoBiD;gBACpBhD,aAAa+B;gBACb9B,yBAAyBgC;gBACzB/B;gBACAC;gBACAC;YACF;YAEF8F,gBAAgBS;YAChBH,6BAAe,oBAACE;QAClB;QAEA,MAAMH,YAAuB;YAC3BM,SAASL;YACTjF,SAASkF;QACX;QAEA,OAAOT,qBAAqBO,WAAWL;IACzC;IAIJ,uFAAuF;IACvF,MAAMY,0BAA0BzB,iBAAiB0B,MAAM,CACrD,CAACC,MAAM,CAACvB,kBAAkBwB,KAAK;QAC7BD,IAAI,CAACvB,iBAAiB,GAAGwB;QACzB,OAAOD;IACT,GACA,CAAC;IAGH,wIAAwI;IACxI,IAAI,CAAChD,WAAW;QACd,OAAO;YACLA,WAAW,kBAAM,0CAAG8C,wBAAwBI,QAAQ;YACpDhF;QACF;IACF;IAEA,MAAMiF,oBAAoBnI,kBAAkB8D;IAE5C,oEAAoE;IACpE,iEAAiE;IACjE,IAAIiD,oBAAoB,CAAC;IACzB,IACE9C,YACA/C,cACA,2GAA2G;IAC3G,6DAA6D;IAC7D,CAACmF,iBAAiBhB,MAAM,EACxB;QACA0B,oBAAoB;YAClBmB,wBACE,wDACE,oBAACE;gBAAKC,MAAK;gBAASC,SAAQ;gBAC3B7C,QAAQC,GAAG,CAACC,QAAQ,KAAK,+BACxB,oBAACyC;gBAAKC,MAAK;gBAAaC,SAAQ;gBAEjCpE,8BACD,oBAACD;QAGP;IACF;IAEA,MAAMsE,QAAQ;QACZ,GAAGT,uBAAuB;QAC1B,GAAGf,iBAAiB;QACpB,8GAA8G;QAC9G,gEAAgE;QAChE,+GAA+G;QAC/GyB,QAAQxC;QACR,iCAAiC;QACjC,GAAG,AAAC,CAAA;YACF,IAAImC,qBAAqB5G,sBAAsBoD,kBAAkB,EAAE;gBACjE,OAAO,CAAC;YACV;YAEA,IAAId,QAAQ;gBACV,OAAOzB;YACT;QACF,CAAA,GAAI;IACN;IAEA,kEAAkE;IAClE,IAAI,CAAC+F,mBAAmB;QACtBnD,YAAY,MAAMsB,QAAQmC,OAAO,GAAGC,IAAI,CAAC,IACvCvI,iBAAiB6E,WAAWuD;IAEhC;IAEA,OAAO;QACLvD,WAAW;YACT,qBACE,0CACGnB,SAAS1C,iBAAiB,MAE1B0C,UAAUsE,kCACT,oBAACtG;gBACC8G,mBAAmBJ;gBACnBvD,WAAWA;gBACXL,oBAAoBpD,sBAAsBoD,kBAAkB;+BAG9D,oBAACK,WAAcuD,QAUhB;QAGP;QACArF;IACF;AACF"}