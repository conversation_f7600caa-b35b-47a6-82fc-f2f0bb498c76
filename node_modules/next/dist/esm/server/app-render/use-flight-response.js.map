{"version": 3, "sources": ["../../../src/server/app-render/use-flight-response.tsx"], "names": ["encodeText", "decodeText", "htmlEscapeJsonString", "isEdgeRuntime", "process", "env", "NEXT_RUNTIME", "INLINE_FLIGHT_PAYLOAD_BOOTSTRAP", "INLINE_FLIGHT_PAYLOAD_DATA", "INLINE_FLIGHT_PAYLOAD_FORM_STATE", "useFlightResponse", "writable", "flightStream", "clientReferenceManifest", "flightResponseRef", "formState", "nonce", "current", "createFromReadableStream", "TURBOPACK", "require", "renderStream", "forwardStream", "tee", "res", "ssrManifest", "moduleLoading", "moduleMap", "edgeSSRModuleMapping", "ssrModuleMapping", "bootstrapped", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "writer", "getWriter", "startScriptTag", "JSON", "stringify", "textDecoder", "TextDecoder", "read", "then", "done", "value", "write", "setTimeout", "close", "responsePartial", "scripts"], "mappings": "AAEA,SAASA,UAAU,EAAEC,UAAU,QAAQ,gCAA+B;AACtE,SAASC,oBAAoB,QAAQ,gBAAe;AAEpD,MAAMC,gBAAgBC,QAAQC,GAAG,CAACC,YAAY,KAAK;AAEnD,MAAMC,kCAAkC;AACxC,MAAMC,6BAA6B;AACnC,MAAMC,mCAAmC;AAEzC;;;CAGC,GACD,OAAO,SAASC,kBACdC,QAAoC,EACpCC,YAAwC,EACxCC,uBAAgD,EAChDC,iBAAoC,EACpCC,SAAqB,EACrBC,KAAc;IAEd,IAAIF,kBAAkBG,OAAO,KAAK,MAAM;QACtC,OAAOH,kBAAkBG,OAAO;IAClC;IACA,wGAAwG;IACxG,IAAIC;IACJ,uGAAuG;IACvG,IAAId,QAAQC,GAAG,CAACc,SAAS,EAAE;QACzBD,2BACE,6DAA6D;QAC7DE,QAAQ,0CAA0CF,wBAAwB;IAC9E,OAAO;QACLA,2BACE,6DAA6D;QAC7DE,QAAQ,wCAAwCF,wBAAwB;IAC5E;IAEA,MAAM,CAACG,cAAcC,cAAc,GAAGV,aAAaW,GAAG;IACtD,MAAMC,MAAMN,yBAAyBG,cAAc;QACjDI,aAAa;YACXC,eAAeb,wBAAwBa,aAAa;YACpDC,WAAWxB,gBACPU,wBAAwBe,oBAAoB,GAC5Cf,wBAAwBgB,gBAAgB;QAC9C;QACAb;IACF;IACAF,kBAAkBG,OAAO,GAAGO;IAE5B,IAAIM,eAAe;IACnB,iDAAiD;IACjD,MAAMC,gBAAgBT,cAAcU,SAAS;IAC7C,MAAMC,SAAStB,SAASuB,SAAS;IACjC,MAAMC,iBAAiBnB,QACnB,CAAC,cAAc,EAAEoB,KAAKC,SAAS,CAACrB,OAAO,CAAC,CAAC,GACzC;IACJ,MAAMsB,cAAc,IAAIC;IAExB,SAASC;QACPT,cAAcS,IAAI,GAAGC,IAAI,CAAC,CAAC,EAAEC,IAAI,EAAEC,KAAK,EAAE;YACxC,IAAI,CAACb,cAAc;gBACjBA,eAAe;gBACfG,OAAOW,KAAK,CACV5C,WACE,CAAC,EAAEmC,eAAe,uCAAuC,EAAEjC,qBACzDkC,KAAKC,SAAS,CAAC;oBAAC9B;iBAAgC,GAChD,qBAAqB,EAAEL,qBACvBkC,KAAKC,SAAS,CAAC;oBAAC5B;oBAAkCM;iBAAU,GAC5D,UAAU,CAAC;YAGnB;YACA,IAAI2B,MAAM;gBACR,iIAAiI;gBACjI,iEAAiE;gBACjE,+IAA+I;gBAC/I,iDAAiD;gBACjDG,WAAW;oBACT/B,kBAAkBG,OAAO,GAAG;gBAC9B;gBACAgB,OAAOa,KAAK;YACd,OAAO;gBACL,MAAMC,kBAAkB9C,WAAW0C,OAAOL;gBAC1C,MAAMU,UAAU,CAAC,EAAEb,eAAe,mBAAmB,EAAEjC,qBACrDkC,KAAKC,SAAS,CAAC;oBAAC7B;oBAA4BuC;iBAAgB,GAC5D,UAAU,CAAC;gBAEbd,OAAOW,KAAK,CAAC5C,WAAWgD;gBACxBR;YACF;QACF;IACF;IACAA;IAEA,OAAOhB;AACT"}