{"version": 3, "sources": ["../../../src/server/app-render/create-component-and-styles.tsx"], "names": ["React", "interopDefault", "getCssInlinedLinkTags", "getAssetQueryString", "createComponentAndStyles", "filePath", "getComponent", "injectedCSS", "ctx", "cssHrefs", "clientReferenceManifest", "styles", "map", "href", "index", "fullHref", "assetPrefix", "precedence", "process", "env", "NODE_ENV", "link", "rel", "crossOrigin", "renderOpts", "key", "Comp"], "mappings": "AAAA,OAAOA,WAAW,QAAO;AACzB,SAASC,cAAc,QAAQ,oBAAmB;AAClD,SAASC,qBAAqB,QAAQ,8BAA6B;AAEnE,SAASC,mBAAmB,QAAQ,2BAA0B;AAE9D,OAAO,eAAeC,yBAAyB,EAC7CC,QAAQ,EACRC,YAAY,EACZC,WAAW,EACXC,GAAG,EAMJ;IACC,MAAMC,WAAWP,sBACfM,IAAIE,uBAAuB,EAC3BL,UACAE;IAGF,MAAMI,SAASF,WACXA,SAASG,GAAG,CAAC,CAACC,MAAMC;QAClB,iEAAiE;QACjE,kDAAkD;QAClD,mDAAmD;QACnD,mEAAmE;QACnE,mEAAmE;QACnE,cAAc;QACd,MAAMC,WAAW,CAAC,EAAEP,IAAIQ,WAAW,CAAC,OAAO,EAAEH,KAAK,EAAEV,oBAClDK,KACA,MACA,CAAC;QAEH,gEAAgE;QAChE,oEAAoE;QACpE,2DAA2D;QAC3D,iEAAiE;QACjE,0DAA0D;QAC1D,+CAA+C;QAC/C,MAAMS,aACJC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,UAAUP,OAAO;QAE5D,qBACE,oBAACQ;YACCC,KAAI;YACJT,MAAME;YACN,aAAa;YACbE,YAAYA;YACZM,aAAaf,IAAIgB,UAAU,CAACD,WAAW;YACvCE,KAAKX;;IAGX,KACA;IAEJ,MAAMY,OAAOzB,eAAe,MAAMK;IAElC,OAAO;QAACoB;QAAMf;KAAO;AACvB"}