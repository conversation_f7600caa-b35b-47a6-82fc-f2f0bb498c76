{"version": 3, "sources": ["../../../src/server/app-render/make-get-server-inserted-html.tsx"], "names": ["React", "isNotFoundError", "getURLFromRedirectError", "isRedirectError", "getRedirectStatusCodeFromError", "renderToString", "makeGetServerInsertedHTML", "polyfills", "renderServerInsertedHTML", "flushedErrorMetaTagsUntilIndex", "polyfillsFlushed", "getServerInsertedHTML", "serverCapturedErrors", "errorMetaTags", "length", "error", "push", "meta", "name", "content", "key", "digest", "process", "env", "NODE_ENV", "redirectUrl", "isPermanent", "httpEquiv", "flushed", "ReactDOMServer", "require", "element", "map", "polyfill", "script", "src"], "mappings": "AAAA,OAAOA,WAAW,QAAO;AACzB,SAASC,eAAe,QAAQ,oCAAmC;AACnE,SACEC,uBAAuB,EACvBC,eAAe,QACV,mCAAkC;AACzC,SAASC,8BAA8B,QAAQ,8DAA6D;AAC5G,SAASC,cAAc,QAAQ,qBAAoB;AAEnD,OAAO,SAASC,0BAA0B,EACxCC,SAAS,EACTC,wBAAwB,EAIzB;IACC,IAAIC,iCAAiC;IACrC,IAAIC,mBAAmB;IAEvB,OAAO,SAASC,sBAAsBC,oBAA6B;QACjE,kEAAkE;QAClE,WAAW;QACX,MAAMC,gBAAgB,EAAE;QACxB,MAEEJ,iCAAiCG,qBAAqBE,MAAM,EAC5DL,iCACA;YACA,MAAMM,QAAQH,oBAAoB,CAACH,+BAA+B;YAElE,IAAIR,gBAAgBc,QAAQ;gBAC1BF,cAAcG,IAAI,eAChB,oBAACC;oBAAKC,MAAK;oBAASC,SAAQ;oBAAUC,KAAKL,MAAMM,MAAM;oBACvDC,QAAQC,GAAG,CAACC,QAAQ,KAAK,8BACvB,oBAACP;oBAAKC,MAAK;oBAAaC,SAAQ;oBAAYC,KAAI;qBAC9C;YAER,OAAO,IAAIjB,gBAAgBY,QAAQ;gBACjC,MAAMU,cAAcvB,wBAAwBa;gBAC5C,MAAMW,cACJtB,+BAA+BW,WAAW,MAAM,OAAO;gBACzD,IAAIU,aAAa;oBACfZ,cAAcG,IAAI,eAChB,oBAACC;wBACCU,WAAU;wBACVR,SAAS,CAAC,EAAEO,cAAc,IAAI,EAAE,KAAK,EAAED,YAAY,CAAC;wBACpDL,KAAKL,MAAMM,MAAM;;gBAGvB;YACF;QACF;QAEA,MAAMO,UAAUvB,eAAe;YAC7BwB,gBAAgBC,QAAQ;YACxBC,uBACE,0CACGrB,mBACG,OACAH,6BAAAA,UAAWyB,GAAG,CAAC,CAACC;gBACd,qBAAO,oBAACC;oBAAOd,KAAKa,SAASE,GAAG;oBAAG,GAAGF,QAAQ;;YAChD,IACHzB,4BACAK;QAGP;QACAH,mBAAmB;QACnB,OAAOkB;IACT;AACF"}