{"version": 3, "sources": ["../../src/lib/turbopack-warning.ts"], "names": ["path", "loadConfig", "PHASE_DEVELOPMENT_SERVER", "supportedTurbopackNextConfigOptions", "prodSpecificTurboNextConfigOptions", "validateTurboNextConfig", "dir", "isDev", "getPkgManager", "require", "getBabelConfigFile", "defaultConfig", "bold", "cyan", "dim", "red", "underline", "yellow", "interopDefault", "isTTY", "process", "stdout", "turbopackGradient", "thankYouMessage", "join", "unsupportedParts", "babelrc", "basename", "hasWebpack", "hasTurbo", "unsupportedConfig", "rawNextConfig", "rawConfig", "flatten<PERSON>eys", "obj", "prefix", "keys", "key", "pre", "length", "Array", "isArray", "concat", "push", "getDeepValue", "split", "slice", "customKeys", "<PERSON><PERSON><PERSON><PERSON>", "startsWith", "isSupported", "some", "<PERSON><PERSON><PERSON>", "e", "console", "error", "hasWarningOrError", "log", "feedbackMessage", "warn", "map", "name", "pkgManager", "exit"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AACvB,OAAOC,gBAAgB,mBAAkB;AAEzC,SAASC,wBAAwB,QAAQ,0BAAyB;AAElE,MAAMC,sCAAsC;IAC1C,kCAAkC;IAClC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA,0DAA0D;IAC1D;IACA;IACA;IAEA,+CAA+C;IAC/C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA,qDAAqD;IACrD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA,oCAAoC;IACpC,kEAAkE;IAClE,yEAAyE;IACzE,4CAA4C;IAC5C;IACA;IACA,sBAAsB;IACtB,oCAAoC;IACpC,oBAAoB;IACpB,4BAA4B;IAC5B,+BAA+B;IAC/B,sCAAsC;IACtC,sCAAsC;IAEtC,yBAAyB;IACzB;IACA;IACA,gDAAgD;IAChD;IACA;CAsBD;AAED,kEAAkE;AAClE,MAAMC,qCAAqC;IACzC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,iCAAiC;AACjC,OAAO,eAAeC,wBAAwB,EAC5CC,GAAG,EACHC,KAAK,EAON;IACC,MAAM,EAAEC,aAAa,EAAE,GACrBC,QAAQ;IACV,MAAM,EAAEC,kBAAkB,EAAE,GAC1BD,QAAQ;IACV,MAAM,EAAEE,aAAa,EAAE,GACrBF,QAAQ;IACV,MAAM,EAAEG,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,SAAS,EAAEC,MAAM,EAAE,GAC/CR,QAAQ;IACV,MAAM,EAAES,cAAc,EAAE,GACtBT,QAAQ;IAEV,kGAAkG;IAClG,MAAMU,QAAQC,QAAQC,MAAM,CAACF,KAAK;IAElC,MAAMG,oBAAoB,CAAC,EAAEV,KAC3BO,QACI,sVACA,iBACJ,CAAC,EAAEL,IAAI,UAAU,IAAI,CAAC;IAExB,IAAIS,kBACF;QACE;QACA;QACA;QACA;KACD,CAACC,IAAI,CAAC,QAAQ;IAEjB,IAAIC,mBAAmB;IACvB,IAAIC,UAAU,MAAMhB,mBAAmBJ;IACvC,IAAIoB,SAASA,UAAU1B,KAAK2B,QAAQ,CAACD;IAErC,IAAIE,aAAa;IACjB,IAAIC,WAAW;IAEf,IAAIC,oBAA8B,EAAE;IACpC,IAAIC,gBAA4B,CAAC;IAEjC,IAAI;QACFA,gBAAgBb,eACd,MAAMjB,WAAWC,0BAA0BI,KAAK;YAC9C0B,WAAW;QACb;QAGF,IAAI,OAAOD,kBAAkB,YAAY;YACvCA,gBAAgB,AAACA,cAAsB7B,0BAA0B;gBAC/DS;YACF;QACF;QAEA,MAAMsB,cAAc,CAACC,KAAUC,SAAiB,EAAE;YAChD,IAAIC,OAAiB,EAAE;YAEvB,IAAK,MAAMC,OAAOH,IAAK;gBACrB,IAAI,QAAOA,uBAAAA,GAAK,CAACG,IAAI,MAAK,aAAa;oBACrC;gBACF;gBAEA,MAAMC,MAAMH,OAAOI,MAAM,GAAG,CAAC,EAAEJ,OAAO,CAAC,CAAC,GAAG;gBAE3C,IACE,OAAOD,GAAG,CAACG,IAAI,KAAK,YACpB,CAACG,MAAMC,OAAO,CAACP,GAAG,CAACG,IAAI,KACvBH,GAAG,CAACG,IAAI,KAAK,MACb;oBACAD,OAAOA,KAAKM,MAAM,CAACT,YAAYC,GAAG,CAACG,IAAI,EAAEC,MAAMD;gBACjD,OAAO;oBACLD,KAAKO,IAAI,CAACL,MAAMD;gBAClB;YACF;YAEA,OAAOD;QACT;QAEA,MAAMQ,eAAe,CAACV,KAAUE;YAC9B,IAAI,OAAOA,SAAS,UAAU;gBAC5BA,OAAOA,KAAKS,KAAK,CAAC;YACpB;YACA,IAAIT,KAAKG,MAAM,KAAK,GAAG;gBACrB,OAAOL,uBAAAA,GAAK,CAACE,wBAAAA,IAAM,CAAC,EAAE,CAAC;YACzB;YACA,OAAOQ,aAAaV,uBAAAA,GAAK,CAACE,wBAAAA,IAAM,CAAC,EAAE,CAAC,EAAEA,KAAKU,KAAK,CAAC;QACnD;QAEA,MAAMC,aAAad,YAAYF;QAE/B,IAAIiB,gBAAgBzC,QAChB;eACKJ;eACAC;SACJ,GACDD;QAEJ,KAAK,MAAMkC,OAAOU,WAAY;YAC5B,IAAIV,IAAIY,UAAU,CAAC,YAAY;gBAC7BrB,aAAa;YACf;YACA,IAAIS,IAAIY,UAAU,CAAC,uBAAuB;gBACxCpB,WAAW;YACb;YAEA,IAAIqB,cACFF,cAAcG,IAAI,CAAC,CAACC,eAAiBf,IAAIY,UAAU,CAACG,kBACpDR,aAAab,eAAeM,SAASO,aAAajC,eAAe0B;YACnE,IAAI,CAACa,aAAa;gBAChBpB,kBAAkBa,IAAI,CAACN;YACzB;QACF;IACF,EAAE,OAAOgB,GAAG;QACVC,QAAQC,KAAK,CAAC,mDAAmDF;IACnE;IAEA,MAAMG,oBAAoB9B,WAAWI,kBAAkBS,MAAM;IAC7D,IAAI,CAACiB,mBAAmB;QACtBjC,kBAAkBT,IAAIS;IACxB;IACA+B,QAAQG,GAAG,CAACnC,oBAAoBC;IAEhC,IAAImC,kBAAkB,CAAC,4CAA4C,EAAE1C,UACnE,sCACA,EAAE,CAAC;IAEL,IAAIY,cAAc,CAACC,UAAU;QAC3ByB,QAAQK,IAAI,CACV,CAAC,EAAE,EAAE1C,OACH,YACA;EACN,EAAE,CAAC,sHAAsH,CAAC,CAAC,CAAC;IAE5H;IAEA,IAAIS,SAAS;QACXD,oBAAoB,CAAC,oBAAoB,EAAEZ,KACzCa,SACA,8GAA8G,CAAC;IACnH;IAEA,IACEI,kBAAkBS,MAAM,KAAK,KAC7BT,iBAAiB,CAAC,EAAE,KAAK,uCACzB;QACAwB,QAAQK,IAAI,CACV,CAAC,EAAE,EAAE1C,OAAO,YAAY,CAAC,EAAEJ,KACzB,uCACA,uDAAuD,CAAC;IAE9D,OAAO,IAAIiB,kBAAkBS,MAAM,EAAE;QACnCd,oBAAoB,CAAC,mDAAmD,EAAEZ,KACxE,kBACA,oEAAoE,EAAEiB,kBACrE8B,GAAG,CAAC,CAACC,OAAS,CAAC,MAAM,EAAE9C,IAAI8C,MAAM,EAAE,CAAC,EACpCrC,IAAI,CAAC,IAAI,CAAC;IACf;IAEA,IAAIC,kBAAkB;QACpB,MAAMqC,aAAatD,cAAcF;QAEjCgD,QAAQC,KAAK,CACX,CAAC,4GAA4G,EAAE9B,iBAAiB;;;EAGpI,EAAEb,KACAC,KACE,CAAC,EACCiD,eAAe,QACX,wBACA,CAAC,EAAEA,WAAW,gBAAgB,CAAC,CACpC,4CAA4C,CAAC,GAEhD,6BAA6B,EAAEA,WAAW;QACtC,CAAC;QAGLR,QAAQK,IAAI,CAACD;QAEbtC,QAAQ2C,IAAI,CAAC;IACf;IAEAT,QAAQG,GAAG,CAACC;IAEZ,OAAO3B;AACT"}