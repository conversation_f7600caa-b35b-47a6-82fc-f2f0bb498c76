{"version": 3, "sources": ["../../../../src/lib/metadata/resolvers/resolve-basics.ts"], "names": ["resolveAsArrayOrUndefined", "resolveAbsoluteUrlWithPathname", "ViewPortKeys", "resolveAlternateUrl", "url", "metadataBase", "pathname", "URL", "resolveThemeColor", "themeColor", "themeColorDescriptors", "for<PERSON>ach", "descriptor", "push", "color", "media", "resolveViewport", "viewport", "resolved", "viewportKey_", "viewportKey", "value", "resolveUrlValuesOfObject", "obj", "result", "key", "Object", "entries", "item", "index", "title", "resolveCanonicalUrl", "urlOrDescriptor", "resolveAlternates", "alternates", "canonical", "languages", "types", "robotsKeys", "resolveRobotsValue", "robots", "values", "follow", "join", "resolveRobots", "basic", "googleBot", "VerificationKeys", "resolveVerification", "verification", "res", "other", "otherKey", "otherValue", "resolveAppleWebApp", "appWebApp", "capable", "startupImages", "startupImage", "map", "statusBarStyle", "resolveAppLinks", "appLinks", "resolveItunes", "itunes", "appId", "appArgument", "undefined"], "mappings": "AAYA,SAASA,yBAAyB,QAAQ,oBAAmB;AAC7D,SAASC,8BAA8B,QAAQ,gBAAe;AAC9D,SAASC,YAAY,QAAQ,eAAc;AAE3C,SAASC,oBACPC,GAAiB,EACjBC,YAAwB,EACxBC,QAAgB;IAEhB,0CAA0C;IAC1C,8DAA8D;IAC9D,IAAIF,eAAeG,KAAK;QACtBH,MAAM,IAAIG,IAAID,UAAUF;IAC1B;IACA,OAAOH,+BAA+BG,KAAKC,cAAcC;AAC3D;AAEA,OAAO,MAAME,oBAAiD,CAACC;QAI7DT;IAHA,IAAI,CAACS,YAAY,OAAO;IACxB,MAAMC,wBAAwD,EAAE;KAEhEV,6BAAAA,0BAA0BS,gCAA1BT,2BAAuCW,OAAO,CAAC,CAACC;QAC9C,IAAI,OAAOA,eAAe,UACxBF,sBAAsBG,IAAI,CAAC;YAAEC,OAAOF;QAAW;aAC5C,IAAI,OAAOA,eAAe,UAC7BF,sBAAsBG,IAAI,CAAC;YACzBC,OAAOF,WAAWE,KAAK;YACvBC,OAAOH,WAAWG,KAAK;QACzB;IACJ;IAEA,OAAOL;AACT,EAAC;AAED,OAAO,MAAMM,kBAA6C,CAACC;IACzD,IAAIC,WAAyC;IAE7C,IAAI,OAAOD,aAAa,UAAU;QAChCC,WAAWD;IACb,OAAO,IAAIA,UAAU;QACnBC,WAAW;QACX,IAAK,MAAMC,gBAAgBjB,aAAc;YACvC,MAAMkB,cAAcD;YACpB,IAAIC,eAAeH,UAAU;gBAC3B,IAAII,QAAQJ,QAAQ,CAACG,YAAY;gBACjC,IAAI,OAAOC,UAAU,WAAWA,QAAQA,QAAQ,QAAQ;gBACxD,IAAIH,UAAUA,YAAY;gBAC1BA,YAAY,CAAC,EAAEhB,YAAY,CAACkB,YAAY,CAAC,CAAC,EAAEC,MAAM,CAAC;YACrD;QACF;IACF;IACA,OAAOH;AACT,EAAC;AAED,SAASI,yBACPC,GAMa,EACblB,YAA8C,EAC9CC,QAAgB;IAEhB,IAAI,CAACiB,KAAK,OAAO;IAEjB,MAAMC,SAAoD,CAAC;IAC3D,KAAK,MAAM,CAACC,KAAKJ,MAAM,IAAIK,OAAOC,OAAO,CAACJ,KAAM;QAC9C,IAAI,OAAOF,UAAU,YAAYA,iBAAiBd,KAAK;YACrDiB,MAAM,CAACC,IAAI,GAAG;gBACZ;oBACErB,KAAKD,oBAAoBkB,OAAOhB,cAAcC;gBAChD;aACD;QACH,OAAO;YACLkB,MAAM,CAACC,IAAI,GAAG,EAAE;YAChBJ,yBAAAA,MAAOV,OAAO,CAAC,CAACiB,MAAMC;gBACpB,MAAMzB,MAAMD,oBAAoByB,KAAKxB,GAAG,EAAEC,cAAcC;gBACxDkB,MAAM,CAACC,IAAI,CAACI,MAAM,GAAG;oBACnBzB;oBACA0B,OAAOF,KAAKE,KAAK;gBACnB;YACF;QACF;IACF;IACA,OAAON;AACT;AAEA,SAASO,oBACPC,eAA0E,EAC1E3B,YAAwB,EACxBC,QAAgB;IAEhB,IAAI,CAAC0B,iBAAiB,OAAO;IAE7B,MAAM5B,MACJ,OAAO4B,oBAAoB,YAAYA,2BAA2BzB,MAC9DyB,kBACAA,gBAAgB5B,GAAG;IAEzB,qEAAqE;IACrE,OAAO;QACLA,KAAKD,oBAAoBC,KAAKC,cAAcC;IAC9C;AACF;AAEA,OAAO,MAAM2B,oBAGT,CAACC,YAAY7B,cAAc,EAAEC,QAAQ,EAAE;IACzC,IAAI,CAAC4B,YAAY,OAAO;IAExB,MAAMC,YAAYJ,oBAChBG,WAAWC,SAAS,EACpB9B,cACAC;IAEF,MAAM8B,YAAYd,yBAChBY,WAAWE,SAAS,EACpB/B,cACAC;IAEF,MAAMS,QAAQO,yBACZY,WAAWnB,KAAK,EAChBV,cACAC;IAEF,MAAM+B,QAAQf,yBACZY,WAAWG,KAAK,EAChBhC,cACAC;IAGF,MAAMkB,SAAgC;QACpCW;QACAC;QACArB;QACAsB;IACF;IAEA,OAAOb;AACT,EAAC;AAED,MAAMc,aAAa;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AACD,MAAMC,qBAAoE,CACxEC;IAEA,IAAI,CAACA,QAAQ,OAAO;IACpB,IAAI,OAAOA,WAAW,UAAU,OAAOA;IAEvC,MAAMC,SAAmB,EAAE;IAE3B,IAAID,OAAOX,KAAK,EAAEY,OAAO5B,IAAI,CAAC;SACzB,IAAI,OAAO2B,OAAOX,KAAK,KAAK,WAAWY,OAAO5B,IAAI,CAAC;IAExD,IAAI2B,OAAOE,MAAM,EAAED,OAAO5B,IAAI,CAAC;SAC1B,IAAI,OAAO2B,OAAOE,MAAM,KAAK,WAAWD,OAAO5B,IAAI,CAAC;IAEzD,KAAK,MAAMY,OAAOa,WAAY;QAC5B,MAAMjB,QAAQmB,MAAM,CAACf,IAAI;QACzB,IAAI,OAAOJ,UAAU,eAAeA,UAAU,OAAO;YACnDoB,OAAO5B,IAAI,CAAC,OAAOQ,UAAU,YAAYI,MAAM,CAAC,EAAEA,IAAI,CAAC,EAAEJ,MAAM,CAAC;QAClE;IACF;IAEA,OAAOoB,OAAOE,IAAI,CAAC;AACrB;AAEA,OAAO,MAAMC,gBAAyC,CAACJ;IACrD,IAAI,CAACA,QAAQ,OAAO;IACpB,OAAO;QACLK,OAAON,mBAAmBC;QAC1BM,WACE,OAAON,WAAW,WAAWD,mBAAmBC,OAAOM,SAAS,IAAI;IACxE;AACF,EAAC;AAED,MAAMC,mBAAmB;IAAC;IAAU;IAAS;IAAU;IAAM;CAAQ;AACrE,OAAO,MAAMC,sBAAqD,CAChEC;IAEA,IAAI,CAACA,cAAc,OAAO;IAC1B,MAAMC,MAA4B,CAAC;IAEnC,KAAK,MAAMzB,OAAOsB,iBAAkB;QAClC,MAAM1B,QAAQ4B,YAAY,CAACxB,IAAI;QAC/B,IAAIJ,OAAO;YACT,IAAII,QAAQ,SAAS;gBACnByB,IAAIC,KAAK,GAAG,CAAC;gBACb,IAAK,MAAMC,YAAYH,aAAaE,KAAK,CAAE;oBACzC,MAAME,aAAarD,0BACjBiD,aAAaE,KAAK,CAACC,SAAS;oBAE9B,IAAIC,YAAYH,IAAIC,KAAK,CAACC,SAAS,GAAGC;gBACxC;YACF,OAAOH,GAAG,CAACzB,IAAI,GAAGzB,0BAA0BqB;QAC9C;IACF;IACA,OAAO6B;AACT,EAAC;AAED,OAAO,MAAMI,qBAAmD,CAACC;QAS3DvD;IARJ,IAAI,CAACuD,WAAW,OAAO;IACvB,IAAIA,cAAc,MAAM;QACtB,OAAO;YACLC,SAAS;QACX;IACF;IAEA,MAAMC,gBAAgBF,UAAUG,YAAY,IACxC1D,6BAAAA,0BAA0BuD,UAAUG,YAAY,sBAAhD1D,2BAAmD2D,GAAG,CAAC,CAAC/B,OACtD,OAAOA,SAAS,WAAW;YAAExB,KAAKwB;QAAK,IAAIA,QAE7C;IAEJ,OAAO;QACL4B,SAAS,aAAaD,YAAY,CAAC,CAACA,UAAUC,OAAO,GAAG;QACxD1B,OAAOyB,UAAUzB,KAAK,IAAI;QAC1B4B,cAAcD;QACdG,gBAAgBL,UAAUK,cAAc,IAAI;IAC9C;AACF,EAAC;AAED,OAAO,MAAMC,kBAA6C,CAACC;IACzD,IAAI,CAACA,UAAU,OAAO;IACtB,IAAK,MAAMrC,OAAOqC,SAAU;QAC1B,iCAAiC;QACjCA,QAAQ,CAACrC,IAAI,GAAGzB,0BAA0B8D,QAAQ,CAACrC,IAAI;IACzD;IACA,OAAOqC;AACT,EAAC;AAED,OAAO,MAAMC,gBAGT,CAACC,QAAQ3D,cAAc,EAAEC,QAAQ,EAAE;IACrC,IAAI,CAAC0D,QAAQ,OAAO;IACpB,OAAO;QACLC,OAAOD,OAAOC,KAAK;QACnBC,aAAaF,OAAOE,WAAW,GAC3B/D,oBAAoB6D,OAAOE,WAAW,EAAE7D,cAAcC,YACtD6D;IACN;AACF,EAAC"}