{"version": 3, "sources": ["../../../src/lib/metadata/metadata.tsx"], "names": ["React", "AppleWebAppMeta", "FormatDetectionMeta", "ItunesMeta", "BasicMetadata", "VerificationMeta", "AlternatesMetadata", "OpenGraphMetadata", "TwitterMetadata", "AppLinksMeta", "IconsMetadata", "resolveMetadata", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createDefaultMetadata", "isNotFoundError", "createMetadataComponents", "tree", "pathname", "searchParams", "getDynamicParamFromSegment", "appUsingSizeAdjustment", "errorType", "metadataContext", "resolve", "metadataErrorResolving", "Promise", "res", "MetadataTree", "defaultMetadata", "metadata", "error", "errorMetadataItem", "errorConvention", "undefined", "resolvedMetadata", "resolvedError", "parentParams", "metadataItems", "notFoundMetadata", "notFoundMetadataError", "elements", "alternates", "itunes", "formatDetection", "verification", "appleWebApp", "openGraph", "twitter", "appLinks", "icons", "push", "meta", "name", "map", "el", "index", "cloneElement", "key", "MetadataOutlet"], "mappings": "AAGA,OAAOA,WAAW,QAAO;AACzB,SACEC,eAAe,EACfC,mBAAmB,EACnBC,UAAU,EACVC,aAAa,EACbC,gBAAgB,QACX,mBAAkB;AACzB,SAASC,kBAAkB,QAAQ,uBAAsB;AACzD,SACEC,iBAAiB,EACjBC,eAAe,EACfC,YAAY,QACP,uBAAsB;AAC7B,SAASC,aAAa,QAAQ,mBAAkB;AAChD,SAASC,eAAe,QAAQ,qBAAoB;AACpD,SAASC,UAAU,QAAQ,kBAAiB;AAE5C,SAASC,qBAAqB,QAAQ,qBAAoB;AAC1D,SAASC,eAAe,QAAQ,oCAAmC;AAEnE,+DAA+D;AAC/D,+DAA+D;AAC/D,sGAAsG;AACtG,0GAA0G;AAC1G,uEAAuE;AACvE,4EAA4E;AAC5E,OAAO,SAASC,yBAAyB,EACvCC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,0BAA0B,EAC1BC,sBAAsB,EACtBC,SAAS,EAQV;IACC,MAAMC,kBAAkB;QACtBL;IACF;IAEA,IAAIM;IACJ,8DAA8D;IAC9D,MAAMC,yBAAyB,IAAIC,QAA2B,CAACC;QAC7DH,UAAUG;IACZ;IAEA,eAAeC;QACb,MAAMC,kBAAkBf;QACxB,IAAIgB,WAAyCD;QAC7C,IAAIE;QACJ,MAAMC,oBAAkC;YAAC;YAAM;SAAK;QACpD,MAAMC,kBAAkBX,cAAc,aAAaY,YAAYZ;QAE/D,MAAM,CAACa,kBAAkBC,cAAc,GAAG,MAAMxB,gBAAgB;YAC9DK;YACAoB,cAAc,CAAC;YACfC,eAAe,EAAE;YACjBN;YACAb;YACAC;YACAa;YACAV;QACF;QACA,IAAI,CAACa,eAAe;YAClBN,WAAWK;YACXX,QAAQU;QACV,OAAO;YACLH,QAAQK;YACR,0FAA0F;YAC1F,kGAAkG;YAClG,kDAAkD;YAClD,IAAI,CAACd,aAAaP,gBAAgBqB,gBAAgB;gBAChD,MAAM,CAACG,kBAAkBC,sBAAsB,GAAG,MAAM5B,gBACtD;oBACEK;oBACAoB,cAAc,CAAC;oBACfC,eAAe,EAAE;oBACjBN;oBACAb;oBACAC;oBACAa,iBAAiB;oBACjBV;gBACF;gBAEFO,WAAWS;gBACXR,QAAQS,yBAAyBT;YACnC;YACAP,QAAQO;QACV;QAEA,MAAMU,WAAW5B,WAAW;YAC1BR,cAAc;gBAAEyB;YAAS;YACzBvB,mBAAmB;gBAAEmC,YAAYZ,SAASY,UAAU;YAAC;YACrDtC,WAAW;gBAAEuC,QAAQb,SAASa,MAAM;YAAC;YACrCxC,oBAAoB;gBAAEyC,iBAAiBd,SAASc,eAAe;YAAC;YAChEtC,iBAAiB;gBAAEuC,cAAcf,SAASe,YAAY;YAAC;YACvD3C,gBAAgB;gBAAE4C,aAAahB,SAASgB,WAAW;YAAC;YACpDtC,kBAAkB;gBAAEuC,WAAWjB,SAASiB,SAAS;YAAC;YAClDtC,gBAAgB;gBAAEuC,SAASlB,SAASkB,OAAO;YAAC;YAC5CtC,aAAa;gBAAEuC,UAAUnB,SAASmB,QAAQ;YAAC;YAC3CtC,cAAc;gBAAEuC,OAAOpB,SAASoB,KAAK;YAAC;SACvC;QAED,IAAI7B,wBAAwBoB,SAASU,IAAI,eAAC,oBAACC;YAAKC,MAAK;;QAErD,qBACE,0CACGZ,SAASa,GAAG,CAAC,CAACC,IAAIC;YACjB,qBAAOvD,MAAMwD,YAAY,CAACF,IAA0B;gBAAEG,KAAKF;YAAM;QACnE;IAGN;IAEA,eAAeG;QACb,MAAM5B,QAAQ,MAAMN;QACpB,IAAIM,OAAO;YACT,MAAMA;QACR;QACA,OAAO;IACT;IAEA,2CAA2C;IAC3C,OAAO;QAACH;QAAc+B;KAAe;AACvC"}