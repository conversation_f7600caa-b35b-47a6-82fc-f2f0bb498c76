{"version": 3, "sources": ["../../../src/lib/metadata/resolve-metadata.ts"], "names": ["createDefaultMetadata", "resolveOpenGraph", "resolveTwitter", "resolveTitle", "resolveAsArrayOrUndefined", "isClientReference", "getComponentTypeModule", "getLayoutOrPageModule", "interopDefault", "resolveAlternates", "resolveAppleWebApp", "resolveAppLinks", "resolveRobots", "resolveThemeColor", "resolveVerification", "resolveViewport", "resolveItunes", "resolveIcons", "getTracer", "ResolveMetadataSpan", "PAGE_SEGMENT_KEY", "hasIconsProperty", "icons", "prop", "URL", "Array", "isArray", "mergeStaticMetadata", "source", "target", "staticFilesMetadata", "metadataContext", "titleTemplates", "icon", "apple", "openGraph", "twitter", "manifest", "hasOwnProperty", "resolvedTwitter", "images", "metadataBase", "resolvedOpenGraph", "merge", "key_", "key", "title", "alternates", "verification", "viewport", "appleWebApp", "appLinks", "robots", "themeColor", "authors", "itunes", "other", "Object", "assign", "getDefinedMetadata", "mod", "props", "tracingProps", "generateMetadata", "route", "parent", "trace", "spanName", "attributes", "metadata", "collectStaticImagesFiles", "type", "undefined", "iconPromises", "map", "imageModule", "length", "Promise", "all", "flat", "resolveStaticMetadata", "components", "staticMetadata", "collectMetadata", "tree", "metadataItems", "errorMetadataItem", "errorConvention", "modType", "hasErrorConventionComponent", "Boolean", "metadataExport", "push", "errorMod", "errorMetadataExport", "resolveMetadataItems", "parentParams", "treePrefix", "getDynamicParamFromSegment", "searchParams", "segment", "parallelRoutes", "page", "currentTreePrefix", "isPage", "segmentParam", "currentParams", "value", "param", "layerProps", "params", "filter", "s", "join", "childTree", "keys", "commonOgKeys", "postProcessMetadata", "autoFillProps", "hasTwTitle", "absolute", "hasTwDescription", "description", "hasTwImages", "partialTwitter", "accumulateMetadata", "resolvedMetadata", "resolvers", "generateMetadataResults", "resolvingIndex", "i", "j", "preloadMetadataExport", "resolve", "resolveParent", "generatedMetadata", "currentResolvedMetadata", "process", "env", "NODE_ENV", "freeze", "require", "cloneMetadata", "template", "resolveMetadata", "resolvedMetadataItems", "error", "err"], "mappings": "AAYA,SAASA,qBAAqB,QAAQ,qBAAoB;AAC1D,SAASC,gBAAgB,EAAEC,cAAc,QAAQ,gCAA+B;AAChF,SAASC,YAAY,QAAQ,4BAA2B;AACxD,SAASC,yBAAyB,QAAQ,mBAAkB;AAC5D,SAASC,iBAAiB,QAAQ,sBAAqB;AACvD,SACEC,sBAAsB,EACtBC,qBAAqB,QAChB,kCAAiC;AACxC,SAASC,cAAc,QAAQ,qBAAoB;AACnD,SACEC,iBAAiB,EACjBC,kBAAkB,EAClBC,eAAe,EACfC,aAAa,EACbC,iBAAiB,EACjBC,mBAAmB,EACnBC,eAAe,EACfC,aAAa,QACR,6BAA4B;AACnC,SAASC,YAAY,QAAQ,4BAA2B;AACxD,SAASC,SAAS,QAAQ,gCAA+B;AACzD,SAASC,mBAAmB,QAAQ,mCAAkC;AACtE,SAASC,gBAAgB,QAAQ,6BAA4B;AAkB7D,SAASC,iBACPC,KAAwB,EACxBC,IAAsB;IAEtB,IAAI,CAACD,OAAO,OAAO;IACnB,IAAIC,SAAS,QAAQ;QACnB,0GAA0G;QAC1G,OAAO,CAAC,CACN,CAAA,OAAOD,UAAU,YACjBA,iBAAiBE,OACjBC,MAAMC,OAAO,CAACJ,UACbC,QAAQD,SAASA,KAAK,CAACC,KAAK;IAEjC,OAAO;QACL,4FAA4F;QAC5F,OAAO,CAAC,CAAE,CAAA,OAAOD,UAAU,YAAYC,QAAQD,SAASA,KAAK,CAACC,KAAK,AAAD;IACpE;AACF;AAEA,SAASI,oBACPC,MAAuB,EACvBC,MAAwB,EACxBC,mBAAmC,EACnCC,eAAgC,EAChCC,cAA8B;QAedJ,iBAUEA;IAvBlB,IAAI,CAACE,qBAAqB;IAC1B,MAAM,EAAEG,IAAI,EAAEC,KAAK,EAAEC,SAAS,EAAEC,OAAO,EAAEC,QAAQ,EAAE,GAAGP;IACtD,qFAAqF;IACrF,IACE,AAACG,QAAQ,CAACZ,iBAAiBO,0BAAAA,OAAQN,KAAK,EAAE,WACzCY,SAAS,CAACb,iBAAiBO,0BAAAA,OAAQN,KAAK,EAAE,UAC3C;QACAO,OAAOP,KAAK,GAAG;YACbW,MAAMA,QAAQ,EAAE;YAChBC,OAAOA,SAAS,EAAE;QACpB;IACF;IACA,8FAA8F;IAC9F,IAAIE,WAAW,EAACR,2BAAAA,kBAAAA,OAAQQ,OAAO,qBAAfR,gBAAiBU,cAAc,CAAC,YAAW;QACzD,MAAMC,kBAAkBrC,eACtB;YAAE,GAAG2B,OAAOO,OAAO;YAAEI,QAAQJ;QAAQ,GACrCP,OAAOY,YAAY,EACnBT,eAAeI,OAAO;QAExBP,OAAOO,OAAO,GAAGG;IACnB;IAEA,gGAAgG;IAChG,IAAIJ,aAAa,EAACP,2BAAAA,oBAAAA,OAAQO,SAAS,qBAAjBP,kBAAmBU,cAAc,CAAC,YAAW;QAC7D,MAAMI,oBAAoBzC,iBACxB;YAAE,GAAG4B,OAAOM,SAAS;YAAEK,QAAQL;QAAU,GACzCN,OAAOY,YAAY,EACnBV,iBACAC,eAAeG,SAAS;QAE1BN,OAAOM,SAAS,GAAGO;IACrB;IACA,IAAIL,UAAU;QACZR,OAAOQ,QAAQ,GAAGA;IACpB;IAEA,OAAOR;AACT;AAEA,+DAA+D;AAC/D,SAASc,MAAM,EACbf,MAAM,EACNC,MAAM,EACNC,mBAAmB,EACnBE,cAAc,EACdD,eAAe,EAOhB;IACC,sFAAsF;IACtF,MAAMU,eACJ,QAAOb,0BAAAA,OAAQa,YAAY,MAAK,cAC5Bb,OAAOa,YAAY,GACnBZ,OAAOY,YAAY;IACzB,IAAK,MAAMG,QAAQhB,OAAQ;QACzB,MAAMiB,MAAMD;QAEZ,OAAQC;YACN,KAAK;gBAAS;oBACZhB,OAAOiB,KAAK,GAAG3C,aAAayB,OAAOkB,KAAK,EAAEd,eAAec,KAAK;oBAC9D;gBACF;YACA,KAAK;gBAAc;oBACjBjB,OAAOkB,UAAU,GAAGtC,kBAClBmB,OAAOmB,UAAU,EACjBN,cACAV;oBAEF;gBACF;YACA,KAAK;gBAAa;oBAChBF,OAAOM,SAAS,GAAGlC,iBACjB2B,OAAOO,SAAS,EAChBM,cACAV,iBACAC,eAAeG,SAAS;oBAE1B;gBACF;YACA,KAAK;gBAAW;oBACdN,OAAOO,OAAO,GAAGlC,eACf0B,OAAOQ,OAAO,EACdK,cACAT,eAAeI,OAAO;oBAExB;gBACF;YACA,KAAK;gBACHP,OAAOmB,YAAY,GAAGlC,oBAAoBc,OAAOoB,YAAY;gBAC7D;YACF,KAAK;gBAAY;oBACfnB,OAAOoB,QAAQ,GAAGlC,gBAAgBa,OAAOqB,QAAQ;oBACjD;gBACF;YACA,KAAK;gBAAS;oBACZpB,OAAOP,KAAK,GAAGL,aAAaW,OAAON,KAAK;oBACxC;gBACF;YACA,KAAK;gBACHO,OAAOqB,WAAW,GAAGxC,mBAAmBkB,OAAOsB,WAAW;gBAC1D;YACF,KAAK;gBACHrB,OAAOsB,QAAQ,GAAGxC,gBAAgBiB,OAAOuB,QAAQ;gBACjD;YACF,KAAK;gBAAU;oBACbtB,OAAOuB,MAAM,GAAGxC,cAAcgB,OAAOwB,MAAM;oBAC3C;gBACF;YACA,KAAK;gBAAc;oBACjBvB,OAAOwB,UAAU,GAAGxC,kBAAkBe,OAAOyB,UAAU;oBACvD;gBACF;YACA,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBAAY;oBACfxB,MAAM,CAACgB,IAAI,GAAGzC,0BAA0BwB,MAAM,CAACiB,IAAI;oBACnD;gBACF;YACA,KAAK;gBAAW;oBACdhB,MAAM,CAACgB,IAAI,GAAGzC,0BAA0BwB,OAAO0B,OAAO;oBACtD;gBACF;YACA,KAAK;gBAAU;oBACbzB,MAAM,CAACgB,IAAI,GAAG7B,cACZY,OAAO2B,MAAM,EACbd,cACAV;oBAEF;gBACF;YACA,+CAA+C;YAC/C,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,qCAAqC;gBACrCF,MAAM,CAACgB,IAAI,GAAGjB,MAAM,CAACiB,IAAI,IAAI;gBAC7B;YACF,KAAK;gBACHhB,OAAO2B,KAAK,GAAGC,OAAOC,MAAM,CAAC,CAAC,GAAG7B,OAAO2B,KAAK,EAAE5B,OAAO4B,KAAK;gBAC3D;YACF,KAAK;gBACH3B,OAAOY,YAAY,GAAGA;gBACtB;YACF;gBACE;QACJ;IACF;IACAd,oBACEC,QACAC,QACAC,qBACAC,iBACAC;AAEJ;AAEA,eAAe2B,mBACbC,GAAQ,EACRC,KAAU,EACVC,YAA+B;IAE/B,iFAAiF;IACjF,0EAA0E;IAC1E,IAAIzD,kBAAkBuD,MAAM;QAC1B,OAAO;IACT;IACA,IAAI,OAAOA,IAAIG,gBAAgB,KAAK,YAAY;QAC9C,MAAM,EAAEC,KAAK,EAAE,GAAGF;QAClB,OAAO,CAACG,SACN/C,YAAYgD,KAAK,CACf/C,oBAAoB4C,gBAAgB,EACpC;gBACEI,UAAU,CAAC,iBAAiB,EAAEH,MAAM,CAAC;gBACrCI,YAAY;oBACV,aAAaJ;gBACf;YACF,GACA,IAAMJ,IAAIG,gBAAgB,CAACF,OAAOI;IAExC;IACA,OAAOL,IAAIS,QAAQ,IAAI;AACzB;AAEA,eAAeC,yBACbD,QAAoC,EACpCR,KAAU,EACVU,IAAmD;QAU9C;IARL,IAAI,EAACF,4BAAAA,QAAU,CAACE,KAAK,GAAE,OAAOC;IAE9B,MAAMC,eAAeJ,QAAQ,CAACE,KAAyB,CAACG,GAAG,CACzD,OAAOC,cACLnE,eAAe,MAAMmE,YAAYd;IAGrC,OAAOY,CAAAA,gCAAAA,aAAcG,MAAM,IAAG,KACzB,QAAA,MAAMC,QAAQC,GAAG,CAACL,kCAAnB,AAAC,MAAkCM,IAAI,KACvCP;AACN;AAEA,eAAeQ,sBAAsBC,UAA0B,EAAEpB,KAAU;IACzE,MAAM,EAAEQ,QAAQ,EAAE,GAAGY;IACrB,IAAI,CAACZ,UAAU,OAAO;IAEtB,MAAM,CAACpC,MAAMC,OAAOC,WAAWC,QAAQ,GAAG,MAAMyC,QAAQC,GAAG,CAAC;QAC1DR,yBAAyBD,UAAUR,OAAO;QAC1CS,yBAAyBD,UAAUR,OAAO;QAC1CS,yBAAyBD,UAAUR,OAAO;QAC1CS,yBAAyBD,UAAUR,OAAO;KAC3C;IAED,MAAMqB,iBAAiB;QACrBjD;QACAC;QACAC;QACAC;QACAC,UAAUgC,SAAShC,QAAQ;IAC7B;IAEA,OAAO6C;AACT;AAEA,4FAA4F;AAC5F,OAAO,eAAeC,gBAAgB,EACpCC,IAAI,EACJC,aAAa,EACbC,iBAAiB,EACjBzB,KAAK,EACLG,KAAK,EACLuB,eAAe,EAQhB;IACC,IAAI3B;IACJ,IAAI4B;IACJ,MAAMC,8BAA8BC,QAClCH,mBAAmBH,IAAI,CAAC,EAAE,CAACG,gBAAgB;IAE7C,IAAIA,iBAAiB;QACnB3B,MAAM,MAAMtD,uBAAuB8E,MAAM;QACzCI,UAAUD;IACZ,OAAO;QACJ,CAAC3B,KAAK4B,QAAQ,GAAG,MAAMjF,sBAAsB6E;IAChD;IAEA,IAAII,SAAS;QACXxB,SAAS,CAAC,CAAC,EAAEwB,QAAQ,CAAC;IACxB;IAEA,MAAM1D,sBAAsB,MAAMkD,sBAAsBI,IAAI,CAAC,EAAE,EAAEvB;IACjE,MAAM8B,iBAAiB/B,MACnB,MAAMD,mBAAmBC,KAAKC,OAAO;QAAEG;IAAM,KAC7C;IAEJqB,cAAcO,IAAI,CAAC;QAACD;QAAgB7D;KAAoB;IAExD,IAAI2D,+BAA+BF,iBAAiB;QAClD,MAAMM,WAAW,MAAMvF,uBAAuB8E,MAAMG;QACpD,MAAMO,sBAAsBD,WACxB,MAAMlC,mBAAmBkC,UAAUhC,OAAO;YAAEG;QAAM,KAClD;QACJsB,iBAAiB,CAAC,EAAE,GAAGQ;QACvBR,iBAAiB,CAAC,EAAE,GAAGxD;IACzB;AACF;AAEA,OAAO,eAAeiE,qBAAqB,EACzCX,IAAI,EACJY,YAAY,EACZX,aAAa,EACbC,iBAAiB,EACjBW,aAAa,EAAE,EACfC,0BAA0B,EAC1BC,YAAY,EACZZ,eAAe,EAWhB;IACC,MAAM,CAACa,SAASC,gBAAgB,EAAEC,IAAI,EAAE,CAAC,GAAGlB;IAC5C,MAAMmB,oBAAoB;WAAIN;QAAYG;KAAQ;IAClD,MAAMI,SAAS,OAAOF,SAAS;IAE/B,iCAAiC;IACjC,MAAMG,eAAeP,2BAA2BE;IAChD;;GAEC,GACD,MAAMM,gBACJ,mDAAmD;IACnDD,gBAAgBA,aAAaE,KAAK,KAAK,OACnC;QACE,GAAGX,YAAY;QACf,CAACS,aAAaG,KAAK,CAAC,EAAEH,aAAaE,KAAK;IAC1C,IAEAX;IAEN,MAAMa,aAAa;QACjBC,QAAQJ;QACR,GAAIF,UAAU;YAAEL;QAAa,CAAC;IAChC;IAEA,MAAMhB,gBAAgB;QACpBC;QACAC;QACAC;QACAC;QACA1B,OAAOgD;QACP7C,OAAOuC,iBACL,yCAAyC;SACxCQ,MAAM,CAAC,CAACC,IAAMA,MAAM5F,kBACpB6F,IAAI,CAAC;IACV;IAEA,IAAK,MAAMpE,OAAOwD,eAAgB;QAChC,MAAMa,YAAYb,cAAc,CAACxD,IAAI;QACrC,MAAMkD,qBAAqB;YACzBX,MAAM8B;YACN7B;YACAC;YACAU,cAAcU;YACdT,YAAYM;YACZJ;YACAD;YACAX;QACF;IACF;IAEA,IAAI9B,OAAO0D,IAAI,CAACd,gBAAgBzB,MAAM,KAAK,KAAKW,iBAAiB;QAC/D,0EAA0E;QAC1E,qCAAqC;QACrCF,cAAcO,IAAI,CAACN;IACrB;IAEA,OAAOD;AACT;AAEA,MAAM+B,eAAe;IAAC;IAAS;IAAe;CAAS;AACvD,SAASC,oBACPhD,QAA0B,EAC1BrC,cAA8B;IAE9B,MAAM,EAAEG,SAAS,EAAEC,OAAO,EAAE,GAAGiC;IAC/B,IAAIlC,WAAW;QACb,IAAImF,gBAIC,CAAC;QACN,MAAMC,aAAanF,2BAAAA,QAASU,KAAK,CAAC0E,QAAQ;QAC1C,MAAMC,mBAAmBrF,2BAAAA,QAASsF,WAAW;QAC7C,MAAMC,cAAcjC,QAClBtD,CAAAA,2BAAAA,QAASE,cAAc,CAAC,cAAaF,QAAQI,MAAM;QAErD,IAAI,CAAC+E,YAAYD,cAAcxE,KAAK,GAAGX,UAAUW,KAAK;QACtD,IAAI,CAAC2E,kBAAkBH,cAAcI,WAAW,GAAGvF,UAAUuF,WAAW;QACxE,IAAI,CAACC,aAAaL,cAAc9E,MAAM,GAAGL,UAAUK,MAAM;QAEzD,IAAIiB,OAAO0D,IAAI,CAACG,eAAe1C,MAAM,GAAG,GAAG;YACzC,MAAMgD,iBAAiB1H,eACrBoH,eACAjD,SAAS5B,YAAY,EACrBT,eAAeI,OAAO;YAExB,IAAIiC,SAASjC,OAAO,EAAE;gBACpBiC,SAASjC,OAAO,GAAGqB,OAAOC,MAAM,CAAC,CAAC,GAAGW,SAASjC,OAAO,EAAE;oBACrD,GAAI,CAACmF,cAAc;wBAAEzE,KAAK,EAAE8E,kCAAAA,eAAgB9E,KAAK;oBAAC,CAAC;oBACnD,GAAI,CAAC2E,oBAAoB;wBACvBC,WAAW,EAAEE,kCAAAA,eAAgBF,WAAW;oBAC1C,CAAC;oBACD,GAAI,CAACC,eAAe;wBAAEnF,MAAM,EAAEoF,kCAAAA,eAAgBpF,MAAM;oBAAC,CAAC;gBACxD;YACF,OAAO;gBACL6B,SAASjC,OAAO,GAAGwF;YACrB;QACF;IACF;IACA,OAAOvD;AACT;AAEA,OAAO,eAAewD,mBACpBxC,aAA4B,EAC5BtD,eAAgC;IAEhC,MAAM+F,mBAAmB9H;IACzB,MAAM+H,YAAmD,EAAE;IAC3D,MAAMC,0BAA4D,EAAE;IAEpE,IAAIhG,iBAAiC;QACnCc,OAAO;QACPV,SAAS;QACTD,WAAW;IACb;IAEA,uFAAuF;IACvF,yGAAyG;IAEzG,IAAI8F,iBAAiB;IACrB,IAAK,IAAIC,IAAI,GAAGA,IAAI7C,cAAcT,MAAM,EAAEsD,IAAK;QAC7C,MAAM,CAACvC,gBAAgB7D,oBAAoB,GAAGuD,aAAa,CAAC6C,EAAE;QAC9D,IAAI7D,WAA4B;QAChC,IAAI,OAAOsB,mBAAmB,YAAY;YACxC,IAAI,CAACoC,UAAUnD,MAAM,EAAE;gBACrB,IAAK,IAAIuD,IAAID,GAAGC,IAAI9C,cAAcT,MAAM,EAAEuD,IAAK;oBAC7C,MAAM,CAACC,sBAAsB,GAAG/C,aAAa,CAAC8C,EAAE;oBAChD,6EAA6E;oBAC7E,IAAI,OAAOC,0BAA0B,YAAY;wBAC/CJ,wBAAwBpC,IAAI,CAC1BwC,sBACE,IAAIvD,QAAQ,CAACwD;4BACXN,UAAUnC,IAAI,CAACyC;wBACjB;oBAGN;gBACF;YACF;YAEA,MAAMC,gBAAgBP,SAAS,CAACE,eAAe;YAC/C,MAAMM,oBAAoBP,uBAAuB,CAACC,iBAAiB;YAEnE,uFAAuF;YACvF,qEAAqE;YACrE,MAAMO,0BACJC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACrBlF,OAAOmF,MAAM,CACXC,QAAQ,oBAAoBC,aAAa,CAAChB,qBAE5CA;YAEN,qFAAqF;YACrF,8FAA8F;YAC9F,mGAAmG;YACnGQ,cAAcE;YACdnE,WACEkE,6BAA6B1D,UACzB,MAAM0D,oBACNA;QACR,OAAO,IAAI5C,mBAAmB,QAAQ,OAAOA,mBAAmB,UAAU;YACxE,yCAAyC;YACzCtB,WAAWsB;QACb;QAEAhD,MAAM;YACJZ;YACAF,QAAQiG;YACRlG,QAAQyC;YACRvC;YACAE;QACF;QAEA,gFAAgF;QAChF,kDAAkD;QAClD,IAAIkG,IAAI7C,cAAcT,MAAM,GAAG,GAAG;gBAEvBkD,yBACIA,6BACFA;YAHX9F,iBAAiB;gBACfc,OAAOgF,EAAAA,0BAAAA,iBAAiBhF,KAAK,qBAAtBgF,wBAAwBiB,QAAQ,KAAI;gBAC3C5G,WAAW2F,EAAAA,8BAAAA,iBAAiB3F,SAAS,qBAA1B2F,4BAA4BhF,KAAK,CAACiG,QAAQ,KAAI;gBACzD3G,SAAS0F,EAAAA,4BAAAA,iBAAiB1F,OAAO,qBAAxB0F,0BAA0BhF,KAAK,CAACiG,QAAQ,KAAI;YACvD;QACF;IACF;IAEA,OAAO1B,oBAAoBS,kBAAkB9F;AAC/C;AAEA,OAAO,eAAegH,gBAAgB,EACpC5D,IAAI,EACJY,YAAY,EACZX,aAAa,EACbC,iBAAiB,EACjBY,0BAA0B,EAC1BC,YAAY,EACZZ,eAAe,EACfxD,eAAe,EAYhB;IACC,MAAMkH,wBAAwB,MAAMlD,qBAAqB;QACvDX;QACAY;QACAX;QACAC;QACAY;QACAC;QACAZ;IACF;IACA,IAAIlB,WAA6BrE;IACjC,IAAIkJ;IACJ,IAAI;QACF7E,WAAW,MAAMwD,mBAAmBoB,uBAAuBlH;IAC7D,EAAE,OAAOoH,KAAU;QACjBD,QAAQC;IACV;IACA,OAAO;QAAC9E;QAAU6E;KAAM;AAC1B"}