{"version": 3, "sources": ["../../src/lib/mkcert.ts"], "names": ["fs", "path", "getCacheDirectory", "Log", "execSync", "fetch", "require", "MKCERT_VERSION", "getBinaryName", "platform", "process", "arch", "Error", "downloadBinary", "binaryName", "cacheDirectory", "binaryPath", "join", "existsSync", "downloadUrl", "promises", "mkdir", "recursive", "info", "response", "ok", "body", "status", "arrayBuffer", "buffer", "<PERSON><PERSON><PERSON>", "from", "writeFile", "chmod", "err", "error", "createSelfSignedCertificate", "host", "certDir", "resolvedCertDir", "resolve", "cwd", "keyP<PERSON>", "certPath", "defaultHosts", "hosts", "includes", "stdio", "caLocation", "toString", "trim", "gitignore<PERSON>ath", "gitignore", "readFile", "appendFile", "key", "cert", "rootCA"], "mappings": "AAAA,OAAOA,QAAQ,KAAI;AACnB,OAAOC,UAAU,OAAM;AACvB,SAASC,iBAAiB,QAAQ,gCAA+B;AACjE,YAAYC,SAAS,sBAAqB;AAC1C,SAASC,QAAQ,QAAQ,gBAAe;AAExC,MAAM,EAAEC,KAAK,EAAE,GAAGC,QAAQ;AAI1B,MAAMC,iBAAiB;AAQvB,SAASC;IACP,MAAMC,WAAWC,QAAQD,QAAQ;IACjC,MAAME,OAAOD,QAAQC,IAAI,KAAK,QAAQ,UAAUD,QAAQC,IAAI;IAE5D,IAAIF,aAAa,SAAS;QACxB,OAAO,CAAC,OAAO,EAAEF,eAAe,SAAS,EAAEI,KAAK,IAAI,CAAC;IACvD;IACA,IAAIF,aAAa,UAAU;QACzB,OAAO,CAAC,OAAO,EAAEF,eAAe,QAAQ,EAAEI,KAAK,CAAC;IAClD;IACA,IAAIF,aAAa,SAAS;QACxB,OAAO,CAAC,OAAO,EAAEF,eAAe,OAAO,EAAEI,KAAK,CAAC;IACjD;IAEA,MAAM,IAAIC,MAAM,CAAC,sBAAsB,EAAEH,SAAS,CAAC;AACrD;AAEA,eAAeI;IACb,IAAI;QACF,MAAMC,aAAaN;QACnB,MAAMO,iBAAiBb,kBAAkB;QACzC,MAAMc,aAAaf,KAAKgB,IAAI,CAACF,gBAAgBD;QAE7C,IAAId,GAAGkB,UAAU,CAACF,aAAa;YAC7B,OAAOA;QACT;QAEA,MAAMG,cAAc,CAAC,wDAAwD,EAAEZ,eAAe,CAAC,EAAEO,WAAW,CAAC;QAE7G,MAAMd,GAAGoB,QAAQ,CAACC,KAAK,CAACN,gBAAgB;YAAEO,WAAW;QAAK;QAE1DnB,IAAIoB,IAAI,CAAC,CAAC,6BAA6B,CAAC;QAExC,MAAMC,WAAW,MAAMnB,MAAMc;QAE7B,IAAI,CAACK,SAASC,EAAE,IAAI,CAACD,SAASE,IAAI,EAAE;YAClC,MAAM,IAAId,MAAM,CAAC,2BAA2B,EAAEY,SAASG,MAAM,CAAC,CAAC;QACjE;QAEAxB,IAAIoB,IAAI,CAAC,CAAC,iDAAiD,CAAC;QAE5D,MAAMK,cAAc,MAAMJ,SAASI,WAAW;QAC9C,MAAMC,SAASC,OAAOC,IAAI,CAACH;QAE3B,MAAM5B,GAAGoB,QAAQ,CAACY,SAAS,CAAChB,YAAYa;QACxC,MAAM7B,GAAGoB,QAAQ,CAACa,KAAK,CAACjB,YAAY;QAEpC,OAAOA;IACT,EAAE,OAAOkB,KAAK;QACZ/B,IAAIgC,KAAK,CAAC,6BAA6BD;IACzC;AACF;AAEA,OAAO,eAAeE,4BACpBC,IAAa,EACbC,UAAkB,cAAc;IAEhC,IAAI;QACF,MAAMtB,aAAa,MAAMH;QACzB,IAAI,CAACG,YAAY,MAAM,IAAIJ,MAAM;QAEjC,MAAM2B,kBAAkBtC,KAAKuC,OAAO,CAAC9B,QAAQ+B,GAAG,IAAI,CAAC,EAAE,EAAEH,QAAQ,CAAC;QAElE,MAAMtC,GAAGoB,QAAQ,CAACC,KAAK,CAACkB,iBAAiB;YACvCjB,WAAW;QACb;QAEA,MAAMoB,UAAUzC,KAAKuC,OAAO,CAACD,iBAAiB;QAC9C,MAAMI,WAAW1C,KAAKuC,OAAO,CAACD,iBAAiB;QAE/CpC,IAAIoB,IAAI,CACN;QAGF,MAAMqB,eAAe;YAAC;YAAa;YAAa;SAAM;QAEtD,MAAMC,QACJR,QAAQ,CAACO,aAAaE,QAAQ,CAACT,QAC3B;eAAIO;YAAcP;SAAK,GACvBO;QAENxC,SACE,CAAC,CAAC,EAAEY,WAAW,sBAAsB,EAAE0B,QAAQ,cAAc,EAAEC,SAAS,EAAE,EAAEE,MAAM5B,IAAI,CACpF,KACA,CAAC,EACH;YAAE8B,OAAO;QAAS;QAGpB,MAAMC,aAAa5C,SAAS,CAAC,CAAC,EAAEY,WAAW,SAAS,CAAC,EAAEiC,QAAQ,GAAGC,IAAI;QAEtE,IAAI,CAAClD,GAAGkB,UAAU,CAACwB,YAAY,CAAC1C,GAAGkB,UAAU,CAACyB,WAAW;YACvD,MAAM,IAAI/B,MAAM;QAClB;QAEAT,IAAIoB,IAAI,CAAC,CAAC,+BAA+B,EAAEyB,WAAW,CAAC;QACvD7C,IAAIoB,IAAI,CAAC,CAAC,wBAAwB,EAAEgB,gBAAgB,CAAC;QAErD,MAAMY,gBAAgBlD,KAAKuC,OAAO,CAAC9B,QAAQ+B,GAAG,IAAI;QAElD,IAAIzC,GAAGkB,UAAU,CAACiC,gBAAgB;YAChC,MAAMC,YAAY,MAAMpD,GAAGoB,QAAQ,CAACiC,QAAQ,CAACF,eAAe;YAC5D,IAAI,CAACC,UAAUN,QAAQ,CAACR,UAAU;gBAChCnC,IAAIoB,IAAI,CAAC;gBAET,MAAMvB,GAAGoB,QAAQ,CAACkC,UAAU,CAACH,eAAe,CAAC,EAAE,EAAEb,QAAQ,CAAC;YAC5D;QACF;QAEA,OAAO;YACLiB,KAAKb;YACLc,MAAMb;YACNc,QAAQ,CAAC,EAAET,WAAW,WAAW,CAAC;QACpC;IACF,EAAE,OAAOd,KAAK;QACZ/B,IAAIgC,KAAK,CACP,qEACAD;IAEJ;AACF"}