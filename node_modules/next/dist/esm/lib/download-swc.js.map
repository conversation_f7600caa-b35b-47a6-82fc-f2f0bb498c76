{"version": 3, "sources": ["../../src/lib/download-swc.ts"], "names": ["fs", "path", "Log", "tar", "fetch", "require", "WritableStream", "getRegistry", "getCacheDirectory", "MAX_VERSIONS_TO_CACHE", "extractBinary", "outputDirectory", "pkgName", "tarFileName", "cacheDirectory", "process", "env", "extractFromTar", "x", "file", "join", "cwd", "strip", "existsSync", "info", "promises", "mkdir", "recursive", "tempFile", "Date", "now", "registry", "downloadUrl", "then", "res", "ok", "body", "error", "Error", "status", "cacheWriteStream", "createWriteStream", "pipeTo", "write", "chunk", "close", "rename", "cacheFiles", "readdir", "length", "sort", "a", "b", "localeCompare", "i", "unlink", "catch", "downloadNativeNextSwc", "version", "bindingsDirectory", "triplesABI", "triple", "substring", "downloadWasmSwc", "wasmDirectory", "variant"], "mappings": "AAAA,OAAOA,QAAQ,KAAI;AACnB,OAAOC,UAAU,OAAM;AACvB,YAAYC,SAAS,sBAAqB;AAC1C,OAAOC,SAAS,yBAAwB;AACxC,MAAM,EAAEC,KAAK,EAAE,GAAGC,QAAQ;AAG1B,MAAM,EAAEC,cAAc,EAAE,GAAGD,QAAQ;AAGnC,SAASE,WAAW,QAAQ,yBAAwB;AACpD,SAASC,iBAAiB,QAAQ,gCAA+B;AAEjE,MAAMC,wBAAwB;AAE9B,eAAeC,cACbC,eAAuB,EACvBC,OAAe,EACfC,WAAmB;IAEnB,MAAMC,iBAAiBN,kBACrB,YACAO,QAAQC,GAAG,CAAC,gBAAgB;IAG9B,MAAMC,iBAAiB,IACrBd,IAAIe,CAAC,CAAC;YACJC,MAAMlB,KAAKmB,IAAI,CAACN,gBAAgBD;YAChCQ,KAAKV;YACLW,OAAO;QACT;IAEF,IAAI,CAACtB,GAAGuB,UAAU,CAACtB,KAAKmB,IAAI,CAACN,gBAAgBD,eAAe;QAC1DX,IAAIsB,IAAI,CAAC,CAAC,wBAAwB,EAAEZ,QAAQ,GAAG,CAAC;QAChD,MAAMZ,GAAGyB,QAAQ,CAACC,KAAK,CAACZ,gBAAgB;YAAEa,WAAW;QAAK;QAC1D,MAAMC,WAAW3B,KAAKmB,IAAI,CACxBN,gBACA,CAAC,EAAED,YAAY,MAAM,EAAEgB,KAAKC,GAAG,GAAG,CAAC;QAGrC,MAAMC,WAAWxB;QAEjB,MAAMyB,cAAc,CAAC,EAAED,SAAS,EAAEnB,QAAQ,GAAG,EAAEC,YAAY,CAAC;QAE5D,MAAMT,MAAM4B,aAAaC,IAAI,CAAC,CAACC;YAC7B,MAAM,EAAEC,EAAE,EAAEC,IAAI,EAAE,GAAGF;YACrB,IAAI,CAACC,MAAM,CAACC,MAAM;gBAChBlC,IAAImC,KAAK,CAAC,CAAC,oCAAoC,EAAEL,YAAY,CAAC;YAChE;YAEA,IAAI,CAACG,IAAI;gBACP,MAAM,IAAIG,MAAM,CAAC,2BAA2B,EAAEJ,IAAIK,MAAM,CAAC,CAAC;YAC5D;YACA,IAAI,CAACH,MAAM;gBACT,MAAM,IAAIE,MAAM;YAClB;YACA,MAAME,mBAAmBxC,GAAGyC,iBAAiB,CAACb;YAC9C,OAAOQ,KAAKM,MAAM,CAChB,IAAIpC,eAAe;gBACjBqC,OAAMC,KAAK;oBACTJ,iBAAiBG,KAAK,CAACC;gBACzB;gBACAC;oBACEL,iBAAiBK,KAAK;gBACxB;YACF;QAEJ;QACA,MAAM7C,GAAGyB,QAAQ,CAACqB,MAAM,CAAClB,UAAU3B,KAAKmB,IAAI,CAACN,gBAAgBD;IAC/D;IACA,MAAMI;IAEN,MAAM8B,aAAa,MAAM/C,GAAGyB,QAAQ,CAACuB,OAAO,CAAClC;IAE7C,IAAIiC,WAAWE,MAAM,GAAGxC,uBAAuB;QAC7CsC,WAAWG,IAAI,CAAC,CAACC,GAAGC;YAClB,IAAID,EAAEF,MAAM,GAAGG,EAAEH,MAAM,EAAE,OAAO,CAAC;YACjC,OAAOE,EAAEE,aAAa,CAACD;QACzB;QAEA,iCAAiC;QACjC,IAAK,IAAIE,IAAI,GAAGA,KAAKA,IAAIP,WAAWE,MAAM,GAAGxC,sBAAuB;YAClE,MAAMT,GAAGyB,QAAQ,CACd8B,MAAM,CAACtD,KAAKmB,IAAI,CAACN,gBAAgBiC,UAAU,CAACO,EAAE,GAC9CE,KAAK,CAAC,KAAO;QAClB;IACF;AACF;AAEA,OAAO,eAAeC,sBACpBC,OAAe,EACfC,iBAAyB,EACzBC,UAAyB;IAEzB,KAAK,MAAMC,UAAUD,WAAY;QAC/B,MAAMhD,UAAU,CAAC,UAAU,EAAEiD,OAAO,CAAC;QACrC,MAAMhD,cAAc,CAAC,EAAED,QAAQkD,SAAS,CAAC,GAAG,CAAC,EAAEJ,QAAQ,IAAI,CAAC;QAC5D,MAAM/C,kBAAkBV,KAAKmB,IAAI,CAACuC,mBAAmB/C;QAErD,IAAIZ,GAAGuB,UAAU,CAACZ,kBAAkB;YAClC,mDAAmD;YACnD,0CAA0C;YAC1C;QACF;QAEA,MAAMX,GAAGyB,QAAQ,CAACC,KAAK,CAACf,iBAAiB;YAAEgB,WAAW;QAAK;QAC3D,MAAMjB,cAAcC,iBAAiBC,SAASC;IAChD;AACF;AAEA,OAAO,eAAekD,gBACpBL,OAAe,EACfM,aAAqB,EACrBC,UAA4B,QAAQ;IAEpC,MAAMrD,UAAU,CAAC,eAAe,EAAEqD,QAAQ,CAAC;IAC3C,MAAMpD,cAAc,CAAC,EAAED,QAAQkD,SAAS,CAAC,GAAG,CAAC,EAAEJ,QAAQ,IAAI,CAAC;IAC5D,MAAM/C,kBAAkBV,KAAKmB,IAAI,CAAC4C,eAAepD;IAEjD,IAAIZ,GAAGuB,UAAU,CAACZ,kBAAkB;QAClC,mDAAmD;QACnD,0CAA0C;QAC1C;IACF;IAEA,MAAMX,GAAGyB,QAAQ,CAACC,KAAK,CAACf,iBAAiB;QAAEgB,WAAW;IAAK;IAC3D,MAAMjB,cAAcC,iBAAiBC,SAASC;AAChD"}