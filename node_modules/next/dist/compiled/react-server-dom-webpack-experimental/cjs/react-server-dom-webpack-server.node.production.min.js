/**
 * @license React
 * react-server-dom-webpack-server.node.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
'use strict';var aa=require("util");require("crypto");var ba=require("async_hooks"),ca=require("react"),da=require("react-dom"),l=null,m=0,r=!0;function t(a,b){a=a.write(b);r=r&&a}
function u(a,b){if("string"===typeof b){if(0!==b.length)if(2048<3*b.length)0<m&&(t(a,l.subarray(0,m)),l=new Uint8Array(2048),m=0),t(a,ea.encode(b));else{var d=l;0<m&&(d=l.subarray(m));d=ea.encodeInto(b,d);var c=d.read;m+=d.written;c<b.length&&(t(a,l.subarray(0,m)),l=new Uint8Array(2048),m=ea.encodeInto(b.slice(c),l).written);2048===m&&(t(a,l),l=new Uint8Array(2048),m=0)}}else 0!==b.byteLength&&(2048<b.byteLength?(0<m&&(t(a,l.subarray(0,m)),l=new Uint8Array(2048),m=0),t(a,b)):(d=l.length-m,d<b.byteLength&&
(0===d?t(a,l):(l.set(b.subarray(0,d),m),m+=d,t(a,l),b=b.subarray(d)),l=new Uint8Array(2048),m=0),l.set(b,m),m+=b.byteLength,2048===m&&(t(a,l),l=new Uint8Array(2048),m=0)));return r}var ea=new aa.TextEncoder,v=Symbol.for("react.client.reference"),w=Symbol.for("react.server.reference");function x(a,b,d){return Object.defineProperties(a,{$$typeof:{value:v},$$id:{value:b},$$async:{value:d}})}var fa=Function.prototype.bind,ha=Array.prototype.slice;
function ia(){var a=fa.apply(this,arguments);if(this.$$typeof===w){var b=ha.call(arguments,1);a.$$typeof=w;a.$$id=this.$$id;a.$$bound=this.$$bound?this.$$bound.concat(b):b}return a}
var ja=Promise.prototype,ka={get:function(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "displayName":return;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case "Provider":throw Error("Cannot render a Client Context Provider on the Server. Instead, you can export a Client Component wrapper that itself renders a Client Context Provider.");}throw Error("Cannot access "+
(String(a.name)+"."+String(b))+" on the server. You cannot dot into a client module from a server component. You can only pass the imported name through.");},set:function(){throw Error("Cannot assign to a client module from a server module.");}};
function la(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case "__esModule":var d=a.$$id;a.default=x(function(){throw Error("Attempted to call the default export of "+d+" from the server but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
},a.$$id+"#",a.$$async);return!0;case "then":if(a.then)return a.then;if(a.$$async)return;var c=x({},a.$$id,!0),e=new Proxy(c,ma);a.status="fulfilled";a.value=e;return a.then=x(function(f){return Promise.resolve(f(e))},a.$$id+"#then",!1)}c=a[b];c||(c=x(function(){throw Error("Attempted to call "+String(b)+"() from the server but "+String(b)+" is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
},a.$$id+"#"+b,a.$$async),Object.defineProperty(c,"name",{value:b}),c=a[b]=new Proxy(c,ka));return c}
var ma={get:function(a,b){return la(a,b)},getOwnPropertyDescriptor:function(a,b){var d=Object.getOwnPropertyDescriptor(a,b);d||(d={value:la(a,b),writable:!1,configurable:!1,enumerable:!1},Object.defineProperty(a,b,d));return d},getPrototypeOf:function(){return ja},set:function(){throw Error("Cannot assign to a client module from a server module.");}},ua={prefetchDNS:na,preconnect:oa,preload:pa,preloadModule:qa,preinitStyle:ra,preinitScript:sa,preinitModuleScript:ta};
function na(a){if("string"===typeof a&&a){var b=y();if(b){var d=b.hints,c="D|"+a;d.has(c)||(d.add(c),z(b,"D",a))}}}function oa(a,b){if("string"===typeof a){var d=y();if(d){var c=d.hints,e="C|"+(null==b?"null":b)+"|"+a;c.has(e)||(c.add(e),"string"===typeof b?z(d,"C",[a,b]):z(d,"C",a))}}}
function pa(a,b,d){if("string"===typeof a){var c=y();if(c){var e=c.hints,f="L";if("image"===b&&d){var g=d.imageSrcSet,h=d.imageSizes,k="";"string"===typeof g&&""!==g?(k+="["+g+"]","string"===typeof h&&(k+="["+h+"]")):k+="[][]"+a;f+="[image]"+k}else f+="["+b+"]"+a;e.has(f)||(e.add(f),(d=A(d))?z(c,"L",[a,b,d]):z(c,"L",[a,b]))}}}function qa(a,b){if("string"===typeof a){var d=y();if(d){var c=d.hints,e="m|"+a;if(!c.has(e))return c.add(e),(b=A(b))?z(d,"m",[a,b]):z(d,"m",a)}}}
function ra(a,b,d){if("string"===typeof a){var c=y();if(c){var e=c.hints,f="S|"+a;if(!e.has(f))return e.add(f),(d=A(d))?z(c,"S",[a,"string"===typeof b?b:0,d]):"string"===typeof b?z(c,"S",[a,b]):z(c,"S",a)}}}function sa(a,b){if("string"===typeof a){var d=y();if(d){var c=d.hints,e="X|"+a;if(!c.has(e))return c.add(e),(b=A(b))?z(d,"X",[a,b]):z(d,"X",a)}}}function ta(a,b){if("string"===typeof a){var d=y();if(d){var c=d.hints,e="M|"+a;if(!c.has(e))return c.add(e),(b=A(b))?z(d,"M",[a,b]):z(d,"M",a)}}}
function A(a){if(null==a)return null;var b=!1,d={},c;for(c in a)null!=a[c]&&(b=!0,d[c]=a[c]);return b?d:null}
var va=da.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,wa=new ba.AsyncLocalStorage,B=Symbol.for("react.element"),xa=Symbol.for("react.fragment"),ya=Symbol.for("react.provider"),za=Symbol.for("react.server_context"),Aa=Symbol.for("react.forward_ref"),Ba=Symbol.for("react.suspense"),Ca=Symbol.for("react.suspense_list"),Da=Symbol.for("react.memo"),C=Symbol.for("react.lazy"),E=Symbol.for("react.default_value"),Ea=Symbol.for("react.memo_cache_sentinel"),Fa=Symbol.for("react.postpone"),
Ga=Symbol.iterator,F=null;function G(a,b){if(a!==b){a.context._currentValue=a.parentValue;a=a.parent;var d=b.parent;if(null===a){if(null!==d)throw Error("The stacks must reach the root at the same time. This is a bug in React.");}else{if(null===d)throw Error("The stacks must reach the root at the same time. This is a bug in React.");G(a,d);b.context._currentValue=b.value}}}function Ha(a){a.context._currentValue=a.parentValue;a=a.parent;null!==a&&Ha(a)}
function Ia(a){var b=a.parent;null!==b&&Ia(b);a.context._currentValue=a.value}function Ja(a,b){a.context._currentValue=a.parentValue;a=a.parent;if(null===a)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===b.depth?G(a,b):Ja(a,b)}
function Ka(a,b){var d=b.parent;if(null===d)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===d.depth?G(a,d):Ka(a,d);b.context._currentValue=b.value}function La(a){var b=F;b!==a&&(null===b?Ia(a):null===a?Ha(b):b.depth===a.depth?G(b,a):b.depth>a.depth?Ja(b,a):Ka(b,a),F=a)}function Ma(a,b){var d=a._currentValue;a._currentValue=b;var c=F;return F=a={parent:c,depth:null===c?0:c.depth+1,context:a,parentValue:d,value:b}}var Na=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`");
function Oa(){}function Pa(a,b,d){d=a[d];void 0===d?a.push(b):d!==b&&(b.then(Oa,Oa),b=d);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(c){if("pending"===b.status){var e=b;e.status="fulfilled";e.value=c}},function(c){if("pending"===b.status){var e=b;e.status="rejected";e.reason=c}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}I=b;throw Na;}}var I=null;
function Qa(){if(null===I)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var a=I;I=null;return a}var J=null,K=0,L=null;function Ra(){var a=L;L=null;return a}function Sa(a){return a._currentValue}
var Wa={useMemo:function(a){return a()},useCallback:function(a){return a},useDebugValue:function(){},useDeferredValue:M,useTransition:M,readContext:Sa,useContext:Sa,useReducer:M,useRef:M,useState:M,useInsertionEffect:M,useLayoutEffect:M,useImperativeHandle:M,useEffect:M,useId:Ta,useSyncExternalStore:M,useCacheRefresh:function(){return Ua},useMemoCache:function(a){for(var b=Array(a),d=0;d<a;d++)b[d]=Ea;return b},use:Va};
function M(){throw Error("This Hook is not supported in Server Components.");}function Ua(){throw Error("Refreshing the cache is not supported in Server Components.");}function Ta(){if(null===J)throw Error("useId can only be used while React is rendering");var a=J.identifierCount++;return":"+J.identifierPrefix+"S"+a.toString(32)+":"}
function Va(a){if(null!==a&&"object"===typeof a||"function"===typeof a){if("function"===typeof a.then){var b=K;K+=1;null===L&&(L=[]);return Pa(L,a,b)}if(a.$$typeof===za)return a._currentValue}throw Error("An unsupported type was passed to use(): "+String(a));}function Xa(){return(new AbortController).signal}function Ya(){var a=y();return a?a.cache:new Map}
var Za={getCacheSignal:function(){var a=Ya(),b=a.get(Xa);void 0===b&&(b=Xa(),a.set(Xa,b));return b},getCacheForType:function(a){var b=Ya(),d=b.get(a);void 0===d&&(d=a(),b.set(a,d));return d}},$a=Array.isArray;function ab(a){return Object.prototype.toString.call(a).replace(/^\[object (.*)\]$/,function(b,d){return d})}
function bb(a){switch(typeof a){case "string":return JSON.stringify(10>=a.length?a:a.slice(0,10)+"...");case "object":if($a(a))return"[...]";a=ab(a);return"Object"===a?"{...}":a;case "function":return"function";default:return String(a)}}
function cb(a){if("string"===typeof a)return a;switch(a){case Ba:return"Suspense";case Ca:return"SuspenseList"}if("object"===typeof a)switch(a.$$typeof){case Aa:return cb(a.render);case Da:return cb(a.type);case C:var b=a._payload;a=a._init;try{return cb(a(b))}catch(d){}}return""}
function N(a,b){var d=ab(a);if("Object"!==d&&"Array"!==d)return d;d=-1;var c=0;if($a(a)){var e="[";for(var f=0;f<a.length;f++){0<f&&(e+=", ");var g=a[f];g="object"===typeof g&&null!==g?N(g):bb(g);""+f===b?(d=e.length,c=g.length,e+=g):e=10>g.length&&40>e.length+g.length?e+g:e+"..."}e+="]"}else if(a.$$typeof===B)e="<"+cb(a.type)+"/>";else{e="{";f=Object.keys(a);for(g=0;g<f.length;g++){0<g&&(e+=", ");var h=f[g],k=JSON.stringify(h);e+=('"'+h+'"'===k?h:k)+": ";k=a[h];k="object"===typeof k&&null!==k?N(k):
bb(k);h===b?(d=e.length,c=k.length,e+=k):e=10>k.length&&40>e.length+k.length?e+k:e+"..."}e+="}"}return void 0===b?e:-1<d&&0<c?(a=" ".repeat(d)+"^".repeat(c),"\n  "+e+"\n  "+a):"\n  "+e}var db=ca.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,eb=db.ContextRegistry,O=JSON.stringify,fb=db.ReactCurrentDispatcher,gb=db.ReactCurrentCache;function hb(a){console.error(a)}function ib(){}
function jb(a,b,d,c,e,f){if(null!==gb.current&&gb.current!==Za)throw Error("Currently React only supports one RSC renderer at a time.");va.current=ua;gb.current=Za;var g=new Set,h=[],k=new Set,n={status:0,flushScheduled:!1,fatalError:null,destination:null,bundlerConfig:b,cache:new Map,nextChunkId:0,pendingChunks:0,hints:k,abortableTasks:g,pingedTasks:h,completedImportChunks:[],completedHintChunks:[],completedRegularChunks:[],completedErrorChunks:[],writtenSymbols:new Map,writtenClientReferences:new Map,
writtenServerReferences:new Map,writtenProviders:new Map,identifierPrefix:e||"",identifierCount:1,onError:void 0===d?hb:d,onPostpone:void 0===f?ib:f,toJSON:function(p,q){return kb(n,this,p,q)}};n.pendingChunks++;b=lb(c);a=mb(n,a,b,g);h.push(a);return n}var P=null;function y(){if(P)return P;var a=wa.getStore();return a?a:null}var nb={};
function ob(a,b){a.pendingChunks++;var d=mb(a,null,F,a.abortableTasks);switch(b.status){case "fulfilled":return d.model=b.value,pb(a,d),d.id;case "rejected":var c=b.reason;"object"===typeof c&&null!==c&&c.$$typeof===Fa?(qb(a,c.message),rb(a,d.id)):(c=Q(a,c),R(a,d.id,c));return d.id;default:"string"!==typeof b.status&&(b.status="pending",b.then(function(e){"pending"===b.status&&(b.status="fulfilled",b.value=e)},function(e){"pending"===b.status&&(b.status="rejected",b.reason=e)}))}b.then(function(e){d.model=
e;pb(a,d)},function(e){d.status=4;a.abortableTasks.delete(d);e=Q(a,e);R(a,d.id,e);null!==a.destination&&S(a,a.destination)});return d.id}function z(a,b,d){d=O(d);var c=a.nextChunkId++;b="H"+b;b=c.toString(16)+":"+b;a.completedHintChunks.push(b+d+"\n");sb(a)}function tb(a){if("fulfilled"===a.status)return a.value;if("rejected"===a.status)throw a.reason;throw a;}
function ub(a){switch(a.status){case "fulfilled":case "rejected":break;default:"string"!==typeof a.status&&(a.status="pending",a.then(function(b){"pending"===a.status&&(a.status="fulfilled",a.value=b)},function(b){"pending"===a.status&&(a.status="rejected",a.reason=b)}))}return{$$typeof:C,_payload:a,_init:tb}}
function T(a,b,d,c,e,f){if(null!==c&&void 0!==c)throw Error("Refs cannot be used in Server Components, nor passed to Client Components.");if("function"===typeof b){if(b.$$typeof===v)return[B,b,d,e];K=0;L=f;e=b(e);return"object"===typeof e&&null!==e&&"function"===typeof e.then?"fulfilled"===e.status?e.value:ub(e):e}if("string"===typeof b)return[B,b,d,e];if("symbol"===typeof b)return b===xa?e.children:[B,b,d,e];if(null!=b&&"object"===typeof b){if(b.$$typeof===v)return[B,b,d,e];switch(b.$$typeof){case C:var g=
b._init;b=g(b._payload);return T(a,b,d,c,e,f);case Aa:return a=b.render,K=0,L=f,a(e,void 0);case Da:return T(a,b.type,d,c,e,f);case ya:return Ma(b._context,e.value),[B,b,d,{value:e.value,children:e.children,__pop:nb}]}}throw Error("Unsupported Server Component type: "+bb(b));}function pb(a,b){var d=a.pingedTasks;d.push(b);1===d.length&&(a.flushScheduled=null!==a.destination,setImmediate(function(){return vb(a)}))}
function mb(a,b,d,c){var e={id:a.nextChunkId++,status:0,model:b,context:d,ping:function(){return pb(a,e)},thenableState:null};c.add(e);return e}function U(a){return"$"+a.toString(16)}function wb(a,b,d){a=O(d);return b.toString(16)+":"+a+"\n"}
function xb(a,b,d,c){var e=c.$$async?c.$$id+"#async":c.$$id,f=a.writtenClientReferences,g=f.get(e);if(void 0!==g)return b[0]===B&&"1"===d?"$L"+g.toString(16):U(g);try{var h=a.bundlerConfig,k=c.$$id;g="";var n=h[k];if(n)g=n.name;else{var p=k.lastIndexOf("#");-1!==p&&(g=k.slice(p+1),n=h[k.slice(0,p)]);if(!n)throw Error('Could not find the module "'+k+'" in the React Client Manifest. This is probably a bug in the React Server Components bundler.');}var q=!0===c.$$async?[n.id,n.chunks,g,1]:[n.id,n.chunks,
g];a.pendingChunks++;var H=a.nextChunkId++,D=O(q),Vb=H.toString(16)+":I"+D+"\n";a.completedImportChunks.push(Vb);f.set(e,H);return b[0]===B&&"1"===d?"$L"+H.toString(16):U(H)}catch(Wb){return a.pendingChunks++,b=a.nextChunkId++,d=Q(a,Wb),R(a,b,d),U(b)}}function yb(a,b){a.pendingChunks++;var d=a.nextChunkId++;zb(a,d,b);return d}
function V(a,b,d){a.pendingChunks+=2;var c=a.nextChunkId++;d=new Uint8Array(d.buffer,d.byteOffset,d.byteLength);var e=d.byteLength;b=c.toString(16)+":"+b+e.toString(16)+",";a.completedRegularChunks.push(b,d);return U(c)}
function kb(a,b,d,c){switch(c){case B:return"$"}for(;"object"===typeof c&&null!==c&&(c.$$typeof===B||c.$$typeof===C);)try{switch(c.$$typeof){case B:var e=c;c=T(a,e.type,e.key,e.ref,e.props,null);break;case C:var f=c._init;c=f(c._payload)}}catch(g){d=g===Na?Qa():g;if("object"===typeof d&&null!==d){if("function"===typeof d.then)return a.pendingChunks++,a=mb(a,c,F,a.abortableTasks),c=a.ping,d.then(c,c),a.thenableState=Ra(),"$L"+a.id.toString(16);if(d.$$typeof===Fa)return c=d,a.pendingChunks++,d=a.nextChunkId++,
qb(a,c.message),rb(a,d),"$L"+d.toString(16)}a.pendingChunks++;c=a.nextChunkId++;d=Q(a,d);R(a,c,d);return"$L"+c.toString(16)}if(null===c)return null;if("object"===typeof c){if(c.$$typeof===v)return xb(a,b,d,c);if("function"===typeof c.then)return"$@"+ob(a,c).toString(16);if(c.$$typeof===ya)return c=c._context._globalName,b=a.writtenProviders,d=b.get(d),void 0===d&&(a.pendingChunks++,d=a.nextChunkId++,b.set(c,d),c=wb(a,d,"$P"+c),a.completedRegularChunks.push(c)),U(d);if(c===nb){a=F;if(null===a)throw Error("Tried to pop a Context at the root of the app. This is a bug in React.");
c=a.parentValue;a.context._currentValue=c===E?a.context._defaultValue:c;F=a.parent;return}return c instanceof Map?"$Q"+yb(a,Array.from(c)).toString(16):c instanceof Set?"$W"+yb(a,Array.from(c)).toString(16):c instanceof ArrayBuffer?V(a,"A",new Uint8Array(c)):c instanceof Int8Array?V(a,"C",c):c instanceof Uint8Array?V(a,"c",c):c instanceof Uint8ClampedArray?V(a,"U",c):c instanceof Int16Array?V(a,"S",c):c instanceof Uint16Array?V(a,"s",c):c instanceof Int32Array?V(a,"L",c):c instanceof Uint32Array?
V(a,"l",c):c instanceof Float32Array?V(a,"F",c):c instanceof Float64Array?V(a,"D",c):c instanceof BigInt64Array?V(a,"N",c):c instanceof BigUint64Array?V(a,"m",c):c instanceof DataView?V(a,"V",c):!$a(c)&&(null===c||"object"!==typeof c?a=null:(a=Ga&&c[Ga]||c["@@iterator"],a="function"===typeof a?a:null),a)?Array.from(c):c}if("string"===typeof c){if("Z"===c[c.length-1]&&b[d]instanceof Date)return"$D"+c;if(1024<=c.length)return a.pendingChunks+=2,d=a.nextChunkId++,b="string"===typeof c?Buffer.byteLength(c,
"utf8"):c.byteLength,b=d.toString(16)+":T"+b.toString(16)+",",a.completedRegularChunks.push(b,c),U(d);a="$"===c[0]?"$"+c:c;return a}if("boolean"===typeof c)return c;if("number"===typeof c)return a=c,Number.isFinite(a)?0===a&&-Infinity===1/a?"$-0":a:Infinity===a?"$Infinity":-Infinity===a?"$-Infinity":"$NaN";if("undefined"===typeof c)return"$undefined";if("function"===typeof c){if(c.$$typeof===v)return xb(a,b,d,c);if(c.$$typeof===w)return d=a.writtenServerReferences,b=d.get(c),void 0!==b?a="$F"+b.toString(16):
(b=c.$$bound,b={id:c.$$id,bound:b?Promise.resolve(b):null},a=yb(a,b),d.set(c,a),a="$F"+a.toString(16)),a;if(/^on[A-Z]/.test(d))throw Error("Event handlers cannot be passed to Client Component props."+N(b,d)+"\nIf you need interactivity, consider converting part of this to a Client Component.");throw Error('Functions cannot be passed directly to Client Components unless you explicitly expose it by marking it with "use server".'+N(b,d));}if("symbol"===typeof c){e=a.writtenSymbols;f=e.get(c);if(void 0!==
f)return U(f);f=c.description;if(Symbol.for(f)!==c)throw Error("Only global symbols received from Symbol.for(...) can be passed to Client Components. The symbol Symbol.for("+(c.description+") cannot be found among global symbols.")+N(b,d));a.pendingChunks++;d=a.nextChunkId++;b=wb(a,d,"$S"+f);a.completedImportChunks.push(b);e.set(c,d);return U(d)}if("bigint"===typeof c)return"$n"+c.toString(10);throw Error("Type "+typeof c+" is not supported in Client Component props."+N(b,d));}
function qb(a,b){a=a.onPostpone;a(b)}function Q(a,b){a=a.onError;b=a(b);if(null!=b&&"string"!==typeof b)throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof b+'" instead');return b||""}function Ab(a,b){null!==a.destination?(a.status=2,a.destination.destroy(b)):(a.status=1,a.fatalError=b)}
function rb(a,b){b=b.toString(16)+":P\n";a.completedErrorChunks.push(b)}function R(a,b,d){d={digest:d};b=b.toString(16)+":E"+O(d)+"\n";a.completedErrorChunks.push(b)}function zb(a,b,d){d=O(d,a.toJSON);b=b.toString(16)+":"+d+"\n";a.completedRegularChunks.push(b)}
function vb(a){var b=fb.current;fb.current=Wa;var d=P;J=P=a;try{var c=a.pingedTasks;a.pingedTasks=[];for(var e=0;e<c.length;e++){var f=c[e];a:{var g=a;if(0===f.status){La(f.context);try{var h=f.model;if("object"===typeof h&&null!==h&&h.$$typeof===B){var k=h,n=f.thenableState;f.model=h;h=T(g,k.type,k.key,k.ref,k.props,n);for(f.thenableState=null;"object"===typeof h&&null!==h&&h.$$typeof===B;)k=h,f.model=h,h=T(g,k.type,k.key,k.ref,k.props,null)}zb(g,f.id,h);g.abortableTasks.delete(f);f.status=1}catch(D){var p=
D===Na?Qa():D;if("object"===typeof p&&null!==p)if("function"===typeof p.then){var q=f.ping;p.then(q,q);f.thenableState=Ra();break a}else if(p.$$typeof===Fa){g.abortableTasks.delete(f);f.status=4;qb(g,p.message);rb(g,f.id);break a}g.abortableTasks.delete(f);f.status=4;var H=Q(g,p);R(g,f.id,H)}}}}null!==a.destination&&S(a,a.destination)}catch(D){Q(a,D),Ab(a,D)}finally{fb.current=b,J=null,P=d}}
function S(a,b){l=new Uint8Array(2048);m=0;r=!0;try{for(var d=a.completedImportChunks,c=0;c<d.length;c++)if(a.pendingChunks--,!u(b,d[c])){a.destination=null;c++;break}d.splice(0,c);var e=a.completedHintChunks;for(c=0;c<e.length;c++)if(!u(b,e[c])){a.destination=null;c++;break}e.splice(0,c);var f=a.completedRegularChunks;for(c=0;c<f.length;c++)if(a.pendingChunks--,!u(b,f[c])){a.destination=null;c++;break}f.splice(0,c);var g=a.completedErrorChunks;for(c=0;c<g.length;c++)if(a.pendingChunks--,!u(b,g[c])){a.destination=
null;c++;break}g.splice(0,c)}finally{a.flushScheduled=!1,l&&0<m&&b.write(l.subarray(0,m)),l=null,m=0,r=!0}"function"===typeof b.flush&&b.flush();0===a.pendingChunks&&b.end()}function Bb(a){a.flushScheduled=null!==a.destination;setImmediate(function(){return wa.run(a,vb,a)})}function sb(a){if(!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination){var b=a.destination;a.flushScheduled=!0;setImmediate(function(){return S(a,b)})}}
function Cb(a,b){if(1===a.status)a.status=2,b.destroy(a.fatalError);else if(2!==a.status&&null===a.destination){a.destination=b;try{S(a,b)}catch(d){Q(a,d),Ab(a,d)}}}
function Db(a,b){try{var d=a.abortableTasks;if(0<d.size){var c=void 0===b?Error("The render was aborted by the server without a reason."):b,e=Q(a,c);a.pendingChunks++;var f=a.nextChunkId++;R(a,f,e,c);d.forEach(function(g){g.status=3;var h=U(f);g=wb(a,g.id,h);a.completedErrorChunks.push(g)});d.clear()}null!==a.destination&&S(a,a.destination)}catch(g){Q(a,g),Ab(a,g)}}
function lb(a){if(a){var b=F;La(null);for(var d=0;d<a.length;d++){var c=a[d],e=c[0];c=c[1];if(!eb[e]){var f={$$typeof:za,_currentValue:E,_currentValue2:E,_defaultValue:E,_threadCount:0,Provider:null,Consumer:null,_globalName:e};f.Provider={$$typeof:ya,_context:f};eb[e]=f}Ma(eb[e],c)}a=F;La(b);return a}return null}
function Eb(a,b){var d="",c=a[b];if(c)d=c.name;else{var e=b.lastIndexOf("#");-1!==e&&(d=b.slice(e+1),c=a[b.slice(0,e)]);if(!c)throw Error('Could not find the module "'+b+'" in the React Server Manifest. This is probably a bug in the React Server Components bundler.');}return[c.id,c.chunks,d]}var Fb=new Map;
function Gb(a){var b=globalThis.__next_require__(a);if("function"!==typeof b.then||"fulfilled"===b.status)return null;b.then(function(d){b.status="fulfilled";b.value=d},function(d){b.status="rejected";b.reason=d});return b}function Hb(){}
function Ib(a){for(var b=a[1],d=[],c=0;c<b.length;){var e=b[c++];b[c++];var f=Fb.get(e);if(void 0===f){f=__webpack_chunk_load__(e);d.push(f);var g=Fb.set.bind(Fb,e,null);f.then(g,Hb);Fb.set(e,f)}else null!==f&&d.push(f)}return 4===a.length?0===d.length?Gb(a[0]):Promise.all(d).then(function(){return Gb(a[0])}):0<d.length?Promise.all(d):null}
function W(a){var b=globalThis.__next_require__(a[0]);if(4===a.length&&"function"===typeof b.then)if("fulfilled"===b.status)b=b.value;else throw b.reason;return"*"===a[2]?b:""===a[2]?b.__esModule?b.default:b:b[a[2]]}function Jb(a,b,d,c){this.status=a;this.value=b;this.reason=d;this._response=c}Jb.prototype=Object.create(Promise.prototype);
Jb.prototype.then=function(a,b){switch(this.status){case "resolved_model":Kb(this)}switch(this.status){case "fulfilled":a(this.value);break;case "pending":case "blocked":a&&(null===this.value&&(this.value=[]),this.value.push(a));b&&(null===this.reason&&(this.reason=[]),this.reason.push(b));break;default:b(this.reason)}};function Lb(a,b){for(var d=0;d<a.length;d++)(0,a[d])(b)}
function Mb(a,b){if("pending"===a.status||"blocked"===a.status){var d=a.reason;a.status="rejected";a.reason=b;null!==d&&Lb(d,b)}}function Nb(a,b,d,c,e,f){var g=Eb(a._bundlerConfig,b);a=Ib(g);if(d)d=Promise.all([d,a]).then(function(h){h=h[0];var k=W(g);return k.bind.apply(k,[null].concat(h))});else if(a)d=Promise.resolve(a).then(function(){return W(g)});else return W(g);d.then(Ob(c,e,f),Pb(c));return null}var X=null,Y=null;
function Kb(a){var b=X,d=Y;X=a;Y=null;try{var c=JSON.parse(a.value,a._response._fromJSON);null!==Y&&0<Y.deps?(Y.value=c,a.status="blocked",a.value=null,a.reason=null):(a.status="fulfilled",a.value=c)}catch(e){a.status="rejected",a.reason=e}finally{X=b,Y=d}}function Qb(a,b){a._chunks.forEach(function(d){"pending"===d.status&&Mb(d,b)})}
function Z(a,b){var d=a._chunks,c=d.get(b);c||(c=a._formData.get(a._prefix+b),c=null!=c?new Jb("resolved_model",c,null,a):new Jb("pending",null,null,a),d.set(b,c));return c}function Ob(a,b,d){if(Y){var c=Y;c.deps++}else c=Y={deps:1,value:null};return function(e){b[d]=e;c.deps--;0===c.deps&&"blocked"===a.status&&(e=a.value,a.status="fulfilled",a.value=c.value,null!==e&&Lb(e,c.value))}}function Pb(a){return function(b){return Mb(a,b)}}
function Rb(a,b){a=Z(a,b);"resolved_model"===a.status&&Kb(a);if("fulfilled"!==a.status)throw a.reason;return a.value}
function Sb(a,b,d,c){if("$"===c[0])switch(c[1]){case "$":return c.slice(1);case "@":return b=parseInt(c.slice(2),16),Z(a,b);case "S":return Symbol.for(c.slice(2));case "F":return c=parseInt(c.slice(2),16),c=Rb(a,c),Nb(a,c.id,c.bound,X,b,d);case "Q":return b=parseInt(c.slice(2),16),a=Rb(a,b),new Map(a);case "W":return b=parseInt(c.slice(2),16),a=Rb(a,b),new Set(a);case "K":b=c.slice(2);var e=a._prefix+b+"_",f=new FormData;a._formData.forEach(function(g,h){h.startsWith(e)&&f.append(h.slice(e.length),
g)});return f;case "I":return Infinity;case "-":return"$-0"===c?-0:-Infinity;case "N":return NaN;case "u":return;case "D":return new Date(Date.parse(c.slice(2)));case "n":return BigInt(c.slice(2));default:c=parseInt(c.slice(1),16);a=Z(a,c);switch(a.status){case "resolved_model":Kb(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":return c=X,a.then(Ob(c,b,d),Pb(c)),null;default:throw a.reason;}}return c}
function Tb(a,b){var d=2<arguments.length&&void 0!==arguments[2]?arguments[2]:new FormData,c=new Map,e={_bundlerConfig:a,_prefix:b,_formData:d,_chunks:c,_fromJSON:function(f,g){return"string"===typeof g?Sb(e,this,f,g):g}};return e}
function Ub(a,b,d){a._formData.append(b,d);var c=a._prefix;if(b.startsWith(c)&&(a=a._chunks,b=+b.slice(c.length),(b=a.get(b))&&"pending"===b.status&&(c=b.value,a=b.reason,b.status="resolved_model",b.value=d,null!==c)))switch(Kb(b),b.status){case "fulfilled":Lb(c,b.value);break;case "pending":case "blocked":b.value=c;b.reason=a;break;case "rejected":a&&Lb(a,b.reason)}}function Xb(a){Qb(a,Error("Connection closed."))}
function Yb(a,b,d){var c=Eb(a,b);a=Ib(c);return d?Promise.all([d,a]).then(function(e){e=e[0];var f=W(c);return f.bind.apply(f,[null].concat(e))}):a?Promise.resolve(a).then(function(){return W(c)}):Promise.resolve(W(c))}function Zb(a,b,d){a=Tb(b,d,a);Xb(a);a=Z(a,0);a.then(function(){});if("fulfilled"!==a.status)throw a.reason;return a.value}function $b(a,b){return function(){return Cb(b,a)}}function ac(a,b){return function(){a.destination=null;Db(a,Error(b))}}
exports.createClientModuleProxy=function(a){a=x({},a,!1);return new Proxy(a,ma)};exports.decodeAction=function(a,b){var d=new FormData,c=null;a.forEach(function(e,f){f.startsWith("$ACTION_")?f.startsWith("$ACTION_REF_")?(e="$ACTION_"+f.slice(12)+":",e=Zb(a,b,e),c=Yb(b,e.id,e.bound)):f.startsWith("$ACTION_ID_")&&(e=f.slice(11),c=Yb(b,e,null)):d.append(f,e)});return null===c?null:c.then(function(e){return e.bind(null,d)})};
exports.decodeFormState=function(a,b,d){var c=b.get("$ACTION_KEY");if("string"!==typeof c)return Promise.resolve(null);var e=null;b.forEach(function(g,h){h.startsWith("$ACTION_REF_")&&(g="$ACTION_"+h.slice(12)+":",e=Zb(b,d,g))});if(null===e)return Promise.resolve(null);var f=e.id;return Promise.resolve(e.bound).then(function(g){return null===g?null:[a,c,f,g.length-1]})};exports.decodeReply=function(a,b){if("string"===typeof a){var d=new FormData;d.append("0",a);a=d}a=Tb(b,"",a);Xb(a);return Z(a,0)};
exports.decodeReplyFromBusboy=function(a,b){var d=Tb(b,""),c=0,e=[];a.on("field",function(f,g){0<c?e.push(f,g):Ub(d,f,g)});a.on("file",function(f,g,h){var k=h.filename,n=h.mimeType;if("base64"===h.encoding.toLowerCase())throw Error("React doesn't accept base64 encoded file uploads because we don't expect form data passed from a browser to ever encode data that way. If that's the wrong assumption, we can easily fix it.");c++;var p=[];g.on("data",function(q){p.push(q)});g.on("end",function(){var q=
new Blob(p,{type:n});d._formData.append(f,q,k);c--;if(0===c){for(q=0;q<e.length;q+=2)Ub(d,e[q],e[q+1]);e.length=0}})});a.on("finish",function(){Xb(d)});a.on("error",function(f){Qb(d,f)});return Z(d,0)};exports.registerClientReference=function(a,b,d){return x(a,b+"#"+d,!1)};exports.registerServerReference=function(a,b,d){return Object.defineProperties(a,{$$typeof:{value:w},$$id:{value:null===d?b:b+"#"+d},$$bound:{value:null},bind:{value:ia}})};
exports.renderToPipeableStream=function(a,b,d){var c=jb(a,b,d?d.onError:void 0,d?d.context:void 0,d?d.identifierPrefix:void 0,d?d.onPostpone:void 0),e=!1;Bb(c);return{pipe:function(f){if(e)throw Error("React currently only supports piping to one writable stream.");e=!0;Cb(c,f);f.on("drain",$b(f,c));f.on("error",ac(c,"The destination stream errored while writing data."));f.on("close",ac(c,"The destination stream closed early."));return f},abort:function(f){Db(c,f)}}};
