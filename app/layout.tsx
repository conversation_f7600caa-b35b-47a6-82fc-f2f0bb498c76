import type { Metadata } from 'next'
import './globals.css'
import Navbar from '@/components/Navbar'
import Footer from '@/components/Footer'

export const metadata: Metadata = {
  title: 'Your Personal Website',
  description: 'A personal website with various topic categories',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className="bg-stone-100">
        <Navbar />
        <main>{children}</main>
        <Footer />
      </body>
    </html>
  )
}