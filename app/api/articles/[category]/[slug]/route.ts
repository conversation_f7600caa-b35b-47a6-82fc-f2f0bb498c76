import { NextRequest, NextResponse } from 'next/server';
import { articleStorage } from '@/lib/storage';
import { ArticleCategory } from '@/lib/types';
import { createApiResponse, handleApiError, isValidCategory } from '@/lib/utils';

// GET /api/articles/[category]/[slug] - Fetch individual article by category and slug
export async function GET(
  request: NextRequest,
  { params }: { params: { category: string; slug: string } }
) {
  try {
    // Validate category
    if (!isValidCategory(params.category)) {
      return NextResponse.json(
        createApiResponse(false, undefined, 'Invalid category'),
        { status: 400 }
      );
    }

    const article = await articleStorage.getBySlug(
      params.category as ArticleCategory,
      params.slug
    );

    if (!article) {
      return NextResponse.json(
        createApiResponse(false, undefined, 'Article not found'),
        { status: 404 }
      );
    }

    return NextResponse.json(createApiResponse(true, article));
  } catch (error) {
    return NextResponse.json(handleApiError(error), { status: 500 });
  }
}
