import { NextRequest, NextResponse } from 'next/server';
import { articleStorage } from '@/lib/storage';
import { ArticleCategory } from '@/lib/types';
import { parseQueryParams, createApiResponse, handleApiError, isValidCategory } from '@/lib/utils';

// GET /api/articles/[category] - Fetch articles by category
export async function GET(
  request: NextRequest,
  { params }: { params: { category: string } }
) {
  try {
    // Validate category
    if (!isValidCategory(params.category)) {
      return NextResponse.json(
        createApiResponse(false, undefined, 'Invalid category'),
        { status: 400 }
      );
    }

    const { searchParams } = new URL(request.url);
    const queryParams = parseQueryParams(Object.fromEntries(searchParams));

    const result = await articleStorage.getByCategory(
      params.category as ArticleCategory,
      {
        ...queryParams,
        status: 'published' // Only return published articles for public API
      }
    );

    return NextResponse.json(result);
  } catch (error) {
    return NextResponse.json(handleApiError(error), { status: 500 });
  }
}
