import { NextRequest, NextResponse } from 'next/server';
import { articleStorage } from '@/lib/storage';
import { CreateArticleInput } from '@/lib/types';
import { parseQueryParams, createApiResponse, handleApiError, isValidCategory } from '@/lib/utils';

// GET /api/articles - Fetch all published articles with pagination
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const queryParams = parseQueryParams(Object.fromEntries(searchParams));
    
    // Validate category if provided
    if (searchParams.get('category') && !isValidCategory(searchParams.get('category')!)) {
      return NextResponse.json(
        createApiResponse(false, undefined, 'Invalid category'),
        { status: 400 }
      );
    }

    const result = await articleStorage.getAll({
      ...queryParams,
      category: searchParams.get('category') as any || undefined,
      status: 'published' // Only return published articles for public API
    });

    return NextResponse.json(result);
  } catch (error) {
    return NextResponse.json(handleApiError(error), { status: 500 });
  }
}

// POST /api/articles - Create new article (admin only)
export async function POST(request: NextRequest) {
  try {
    // Basic auth check - in production, implement proper authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        createApiResponse(false, undefined, 'Unauthorized'),
        { status: 401 }
      );
    }

    const body = await request.json();
    
    // Validate required fields
    const requiredFields = ['title', 'content', 'excerpt', 'category', 'author', 'seo'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          createApiResponse(false, undefined, `Missing required field: ${field}`),
          { status: 400 }
        );
      }
    }

    // Validate category
    if (!isValidCategory(body.category)) {
      return NextResponse.json(
        createApiResponse(false, undefined, 'Invalid category'),
        { status: 400 }
      );
    }

    // Validate author object
    if (!body.author.name) {
      return NextResponse.json(
        createApiResponse(false, undefined, 'Author name is required'),
        { status: 400 }
      );
    }

    // Validate SEO object
    if (!body.seo.metaDescription || !body.seo.keywords) {
      return NextResponse.json(
        createApiResponse(false, undefined, 'SEO metaDescription and keywords are required'),
        { status: 400 }
      );
    }

    const articleInput: CreateArticleInput = {
      title: body.title,
      content: body.content,
      excerpt: body.excerpt,
      category: body.category,
      author: {
        name: body.author.name,
        email: body.author.email,
        bio: body.author.bio
      },
      status: body.status || 'draft',
      seo: {
        metaDescription: body.seo.metaDescription,
        keywords: Array.isArray(body.seo.keywords) ? body.seo.keywords : [],
        ogTitle: body.seo.ogTitle,
        ogDescription: body.seo.ogDescription,
        ogImage: body.seo.ogImage
      },
      featuredImage: body.featuredImage,
      tags: Array.isArray(body.tags) ? body.tags : []
    };

    const article = await articleStorage.create(articleInput);
    
    return NextResponse.json(
      createApiResponse(true, article, undefined, 'Article created successfully'),
      { status: 201 }
    );
  } catch (error) {
    return NextResponse.json(handleApiError(error), { status: 500 });
  }
}
