import { NextRequest, NextResponse } from 'next/server';
import { articleStorage } from '@/lib/storage';
import { UpdateArticleInput } from '@/lib/types';
import { createApiResponse, handleApiError, isValidCategory } from '@/lib/utils';

// GET /api/articles/[id] - Fetch article by ID (admin only)
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Basic auth check - in production, implement proper authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        createApiResponse(false, undefined, 'Unauthorized'),
        { status: 401 }
      );
    }

    const article = await articleStorage.getById(params.id);
    
    if (!article) {
      return NextResponse.json(
        createApiResponse(false, undefined, 'Article not found'),
        { status: 404 }
      );
    }

    return NextResponse.json(createApiResponse(true, article));
  } catch (error) {
    return NextResponse.json(handleApiError(error), { status: 500 });
  }
}

// PUT /api/articles/[id] - Update existing article (admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Basic auth check - in production, implement proper authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        createApiResponse(false, undefined, 'Unauthorized'),
        { status: 401 }
      );
    }

    const body = await request.json();
    
    // Validate category if provided
    if (body.category && !isValidCategory(body.category)) {
      return NextResponse.json(
        createApiResponse(false, undefined, 'Invalid category'),
        { status: 400 }
      );
    }

    // Validate author object if provided
    if (body.author && !body.author.name) {
      return NextResponse.json(
        createApiResponse(false, undefined, 'Author name is required'),
        { status: 400 }
      );
    }

    const updateInput: Partial<UpdateArticleInput> = {
      id: params.id,
      ...body
    };

    const article = await articleStorage.update(params.id, updateInput);
    
    return NextResponse.json(
      createApiResponse(true, article, undefined, 'Article updated successfully')
    );
  } catch (error) {
    if (error instanceof Error && error.message === 'Article not found') {
      return NextResponse.json(
        createApiResponse(false, undefined, 'Article not found'),
        { status: 404 }
      );
    }
    return NextResponse.json(handleApiError(error), { status: 500 });
  }
}

// DELETE /api/articles/[id] - Delete article (admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Basic auth check - in production, implement proper authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        createApiResponse(false, undefined, 'Unauthorized'),
        { status: 401 }
      );
    }

    const success = await articleStorage.delete(params.id);
    
    if (!success) {
      return NextResponse.json(
        createApiResponse(false, undefined, 'Article not found'),
        { status: 404 }
      );
    }

    return NextResponse.json(
      createApiResponse(true, undefined, undefined, 'Article deleted successfully')
    );
  } catch (error) {
    return NextResponse.json(handleApiError(error), { status: 500 });
  }
}
