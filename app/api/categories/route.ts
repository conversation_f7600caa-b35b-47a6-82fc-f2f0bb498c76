import { NextRequest, NextResponse } from 'next/server';
import { articleStorage } from '@/lib/storage';
import { createApiResponse, handleApiError } from '@/lib/utils';

// GET /api/categories - Fetch all categories with article counts
export async function GET(request: NextRequest) {
  try {
    const categories = await articleStorage.getCategoryStats();
    return NextResponse.json(createApiResponse(true, categories));
  } catch (error) {
    return NextResponse.json(handleApiError(error), { status: 500 });
  }
}
