import Link from 'next/link'
import Image from 'next/image'

export default function Home() {
  return (
    <div className="max-w-6xl mx-auto px-4 py-12">
      <div className="text-center mb-16">
        <div className="flex justify-center mb-4">
          <span className="text-amber-700">✦ I'M YOUR NAME!</span>
        </div>
        <h1 className="text-5xl font-serif mb-6">
          Welcome to My Journey of<br />Discovery and Growth
        </h1>
        <p className="max-w-2xl mx-auto text-gray-600">
          A writer passionate about exploring the world, sharing insights on various topics,
          enhancing personal growth, and nurturing intellectual curiosity.
        </p>
        <div className="mt-8">
          <Link href="/blog" className="bg-amber-700 text-white px-8 py-3 inline-block">
            GET INSPIRED
          </Link>
        </div>
      </div>
      
      <div className="grid md:grid-cols-3 gap-8 mt-16">
        {/* Featured articles grid */}
        <div className="overflow-hidden">
          <Image 
            src="/images/featured-1.jpg" 
            alt="Featured article" 
            width={400} 
            height={300}
            className="hover:scale-105 transition duration-500"
          />
          <h3 className="text-xl mt-4">Article Title</h3>
          <p className="text-gray-600">Brief description of the article...</p>
        </div>
        {/* Repeat for other featured articles */}
      </div>
    </div>
  )
}