import Image from 'next/image'
import { notFound } from 'next/navigation'
import { articleStorage } from '@/lib/storage'
import { isValidCategory, formatDate, getCategoryDisplayName } from '@/lib/utils'
import { ArticleCategory } from '@/lib/types'

async function getArticle(category: string, slug: string) {
  if (!isValidCategory(category)) {
    return null;
  }

  try {
    const article = await articleStorage.getBySlug(category as ArticleCategory, slug);
    return article;
  } catch (error) {
    console.error('Error fetching article:', error);
    return null;
  }
}

export default async function ArticlePage({ 
  params 
}: { 
  params: { category: string; slug: string } 
}) {
  const article = await getArticle(params.category, params.slug)
  
  if (!article) {
    notFound()
  }
  
  return (
    <div className="max-w-4xl mx-auto px-4 py-12">
      <div className="mb-6">
        <span className="text-amber-700 uppercase">{getCategoryDisplayName(article.category)}</span>
        <h1 className="text-4xl font-serif mt-2">{article.title}</h1>
        <p className="text-gray-500 mt-2">{formatDate(article.publishedAt)}</p>
        <div className="flex items-center gap-4 mt-4 text-sm text-gray-600">
          <span>By {article.author.name}</span>
          {article.readingTime && <span>{article.readingTime} min read</span>}
          {article.viewCount && <span>{article.viewCount} views</span>}
        </div>
      </div>

      {article.featuredImage && (
        <Image
          src={article.featuredImage}
          alt={article.title}
          width={1000}
          height={600}
          className="w-full h-auto rounded-lg mb-8"
        />
      )}

      <div className="prose max-w-none">
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <p className="text-lg text-gray-700 italic">{article.excerpt}</p>
        </div>

        <div className="whitespace-pre-wrap">
          {article.content}
        </div>

        {article.tags && article.tags.length > 0 && (
          <div className="mt-8 pt-8 border-t border-gray-200">
            <h3 className="text-lg font-semibold mb-4">Tags</h3>
            <div className="flex flex-wrap gap-2">
              {article.tags.map((tag, index) => (
                <span
                  key={index}
                  className="px-3 py-1 bg-amber-100 text-amber-800 rounded-full text-sm"
                >
                  {tag}
                </span>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}