import Image from 'next/image'
import { notFound } from 'next/navigation'

// In a real app, this would fetch from your database
async function getArticle(category: string, slug: string) {
  // Mock data for demonstration
  return {
    title: 'Article Title',
    category,
    slug,
    content: 'This is the full article content...',
    image: '/images/article-header.jpg',
    date: 'January 15, 2024'
  }
}

export default async function ArticlePage({ 
  params 
}: { 
  params: { category: string; slug: string } 
}) {
  const article = await getArticle(params.category, params.slug)
  
  if (!article) {
    notFound()
  }
  
  return (
    <div className="max-w-4xl mx-auto px-4 py-12">
      <div className="mb-6">
        <span className="text-amber-700 uppercase">{article.category}</span>
        <h1 className="text-4xl font-serif mt-2">{article.title}</h1>
        <p className="text-gray-500 mt-2">{article.date}</p>
      </div>
      
      <Image 
        src={article.image} 
        alt={article.title} 
        width={1000} 
        height={600}
        className="w-full h-auto rounded-lg mb-8"
      />
      
      <div className="prose max-w-none">
        {article.content}
      </div>
    </div>
  )
}