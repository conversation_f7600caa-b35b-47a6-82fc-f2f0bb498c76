import Link from 'next/link'
import Image from 'next/image'
import { notFound } from 'next/navigation'
import { articleStorage } from '@/lib/storage'
import { isValidCategory, formatDate, getCategoryDisplayName } from '@/lib/utils'
import { ArticleCategory } from '@/lib/types'

async function getCategoryArticles(category: string, page: number = 1) {
  if (!isValidCategory(category)) {
    return null;
  }
  
  try {
    const result = await articleStorage.getByCategory(category as ArticleCategory, {
      page,
      limit: 12,
      status: 'published'
    });
    return result;
  } catch (error) {
    console.error('Error fetching category articles:', error);
    return null;
  }
}

export default async function CategoryPage({ 
  params,
  searchParams 
}: { 
  params: { category: string };
  searchParams: { page?: string };
}) {
  const page = parseInt(searchParams.page || '1');
  const result = await getCategoryArticles(params.category, page);
  
  if (!result) {
    notFound();
  }

  const { data: articles, pagination } = result;
  const categoryDisplayName = getCategoryDisplayName(params.category as ArticleCategory);

  return (
    <div className="max-w-6xl mx-auto px-4 py-12">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-serif mb-4">{categoryDisplayName}</h1>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Explore our collection of {categoryDisplayName.toLowerCase()} articles and insights.
        </p>
      </div>
      
      {articles.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">No articles found in this category yet.</p>
          <Link href="/" className="text-amber-700 hover:text-amber-800 mt-4 inline-block">
            ← Back to Home
          </Link>
        </div>
      ) : (
        <>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {articles.map((article) => (
              <article key={article.id} className="border border-gray-200 rounded overflow-hidden hover:shadow-lg transition-shadow">
                {article.featuredImage && (
                  <Image 
                    src={article.featuredImage} 
                    alt={article.title} 
                    width={400} 
                    height={250}
                    className="w-full h-48 object-cover"
                  />
                )}
                <div className="p-6">
                  <div className="flex items-center gap-2 text-sm text-gray-500 mb-2">
                    <span>{formatDate(article.publishedAt)}</span>
                    {article.readingTime && (
                      <>
                        <span>•</span>
                        <span>{article.readingTime} min read</span>
                      </>
                    )}
                  </div>
                  
                  <h2 className="text-xl font-serif mb-3 line-clamp-2">
                    <Link 
                      href={`/${article.category}/${article.slug}`}
                      className="hover:text-amber-700 transition-colors"
                    >
                      {article.title}
                    </Link>
                  </h2>
                  
                  <p className="text-gray-600 mb-4 line-clamp-3">{article.excerpt}</p>
                  
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-500">
                      By {article.author.name}
                    </div>
                    <Link 
                      href={`/${article.category}/${article.slug}`} 
                      className="text-amber-700 hover:text-amber-800 font-medium"
                    >
                      Read More →
                    </Link>
                  </div>
                  
                  {article.tags && article.tags.length > 0 && (
                    <div className="mt-4 pt-4 border-t border-gray-100">
                      <div className="flex flex-wrap gap-1">
                        {article.tags.slice(0, 3).map((tag, index) => (
                          <span 
                            key={index}
                            className="px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs"
                          >
                            {tag}
                          </span>
                        ))}
                        {article.tags.length > 3 && (
                          <span className="px-2 py-1 text-gray-500 text-xs">
                            +{article.tags.length - 3} more
                          </span>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </article>
            ))}
          </div>

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="flex justify-center items-center gap-4">
              {pagination.hasPrev && (
                <Link 
                  href={`/${params.category}?page=${pagination.page - 1}`}
                  className="px-4 py-2 border border-gray-300 rounded hover:bg-gray-50"
                >
                  ← Previous
                </Link>
              )}
              
              <span className="text-gray-600">
                Page {pagination.page} of {pagination.totalPages}
              </span>
              
              {pagination.hasNext && (
                <Link 
                  href={`/${params.category}?page=${pagination.page + 1}`}
                  className="px-4 py-2 border border-gray-300 rounded hover:bg-gray-50"
                >
                  Next →
                </Link>
              )}
            </div>
          )}
        </>
      )}
    </div>
  )
}
