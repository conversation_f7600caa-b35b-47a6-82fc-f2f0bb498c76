# Website Architecture

## Frontend
- SSR (Server-Side Rendering) with Next.js for better SEO
- Page-based routing for the ~10 topic pages
- Reusable components for consistent UI elements
- Responsive design for mobile/desktop

## Backend
- RESTful API endpoints for CRUD operations
- Content management system for blog posts
- Authentication system (if needed)
- Image storage with CDN integration

## Deployment
- Vercel or Netlify for frontend hosting
- MongoDB Atlas or Supabase for database
- Optional: Cloudinary for image management