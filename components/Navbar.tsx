import Link from 'next/link'
import { FaInstagram, FaFacebook, FaLinkedin, FaPinterest } from 'react-icons/fa'

const categories = [
  { name: 'Home', path: '/' },
  { name: 'History', path: '/history' },
  { name: 'Business', path: '/business' },
  { name: 'Technology', path: '/technology' },
  { name: 'Politics', path: '/politics' },
  { name: 'Art', path: '/art' },
  { name: 'Social Life', path: '/social-life' },
]

export default function Navbar() {
  return (
    <nav className="py-4 px-6 flex justify-between items-center">
      <div className="text-2xl font-serif italic">
        <Link href="/">Your Name ©</Link>
      </div>
      
      <div className="hidden md:flex space-x-6">
        {categories.map((category) => (
          <Link 
            key={category.path} 
            href={category.path}
            className="text-gray-700 hover:text-gray-900"
          >
            {category.name}
          </Link>
        ))}
      </div>
      
      <div className="flex items-center space-x-4">
        <Link href="#" aria-label="Instagram"><FaInstagram /></Link>
        <Link href="#" aria-label="Facebook"><FaFacebook /></Link>
        <Link href="#" aria-label="LinkedIn"><FaLinkedin /></Link>
        <Link href="#" aria-label="Pinterest"><FaPinterest /></Link>
        <Link href="/subscribe" className="ml-4 px-4 py-2 border border-gray-700 text-gray-700 hover:bg-gray-700 hover:text-white transition">
          SUBSCRIBE
        </Link>
      </div>
    </nav>
  )
}