const fs = require('fs/promises');
const path = require('path');

const DATA_DIR = path.join(process.cwd(), 'data');
const ARTICLES_FILE = path.join(DATA_DIR, 'articles.json');

const sampleArticles = [
  {
    id: "tech-ai-evolution-001",
    title: "The Evolution of Artificial Intelligence",
    slug: "the-evolution-of-artificial-intelligence",
    content: `Artificial Intelligence has come a long way since its inception in the 1950s. From simple rule-based systems to today's sophisticated machine learning algorithms, AI has transformed how we interact with technology.

The journey began with <PERSON>'s groundbreaking work on machine intelligence. His famous "Turing Test" proposed a method for determining whether a machine could think like a human. This laid the foundation for decades of research and development.

In the 1960s and 70s, AI research focused on symbolic reasoning and expert systems. These early systems could solve specific problems but lacked the flexibility and learning capabilities we see today.

The 1980s brought the rise of neural networks, inspired by the structure of the human brain. However, computational limitations prevented these systems from reaching their full potential.

The real breakthrough came in the 2010s with the advent of deep learning. Powered by massive datasets and powerful GPUs, deep neural networks began achieving superhuman performance in tasks like image recognition and natural language processing.

Today, AI is everywhere - from the recommendation algorithms on social media to autonomous vehicles navigating our streets. Large language models like GPT have revolutionized how we interact with computers, making AI more accessible than ever before.

Looking ahead, the future of AI promises even more exciting developments. Quantum computing may unlock new possibilities for AI algorithms, while advances in robotics could bring AI into the physical world in unprecedented ways.

As we continue to push the boundaries of what's possible with AI, it's important to consider the ethical implications and ensure that these powerful technologies are developed and deployed responsibly.`,
    excerpt: "Explore the fascinating journey of artificial intelligence from its early beginnings to today's revolutionary breakthroughs in machine learning and deep learning.",
    category: "technology",
    author: {
      name: "Dr. Sarah Chen",
      email: "<EMAIL>",
      bio: "AI researcher and professor at Stanford University"
    },
    publishedAt: "2024-01-15T10:00:00.000Z",
    lastModified: "2024-01-15T10:00:00.000Z",
    createdAt: "2024-01-15T10:00:00.000Z",
    status: "published",
    seo: {
      metaDescription: "Discover the evolution of artificial intelligence from the 1950s to today's advanced AI systems. Learn about key milestones and future possibilities.",
      keywords: ["artificial intelligence", "machine learning", "deep learning", "AI history", "technology evolution"],
      ogTitle: "The Evolution of Artificial Intelligence: From Turing to Modern AI",
      ogDescription: "A comprehensive look at how AI has evolved over the decades and what the future holds."
    },
    featuredImage: "/images/ai-evolution.jpg",
    readingTime: 8,
    tags: ["AI", "Machine Learning", "Technology", "Innovation", "Future Tech"],
    viewCount: 0
  },
  {
    id: "hist-rome-rise-fall-001",
    title: "The Rise and Fall of Ancient Rome",
    slug: "the-rise-and-fall-of-ancient-rome",
    content: `The Roman Empire stands as one of history's most influential civilizations, spanning over a millennium and leaving an indelible mark on Western culture, law, and governance.

Founded in 753 BCE according to legend, Rome began as a small city-state on the Italian peninsula. The early Roman Kingdom gave way to the Roman Republic in 509 BCE, establishing a system of government that would influence democratic institutions for centuries to come.

The Republic's expansion was driven by military prowess and political acumen. Roman legions conquered vast territories across the Mediterranean, bringing diverse cultures under Roman rule. The conquest of Carthage in the Punic Wars established Rome as the dominant Mediterranean power.

However, the Republic's success contained the seeds of its own destruction. Internal conflicts, civil wars, and the rise of powerful generals like Julius Caesar led to the Republic's collapse and the emergence of the Roman Empire under Augustus in 27 BCE.

The Imperial period marked Rome's golden age. Under emperors like Trajan and Hadrian, the empire reached its greatest territorial extent, stretching from Britain to Mesopotamia. Roman engineering marvels like aqueducts, roads, and the Colosseum showcased the empire's technological achievements.

Roman law, codified in the Twelve Tables and later refined in Justinian's Code, became the foundation for legal systems worldwide. Latin, the language of Rome, evolved into the Romance languages and influenced countless others.

The empire's decline was gradual and multifaceted. Economic troubles, military pressures from barbarian tribes, political instability, and the rise of Christianity all contributed to Rome's eventual fall. The Western Roman Empire officially ended in 476 CE, though the Eastern Byzantine Empire continued for another thousand years.

Rome's legacy endures in our legal systems, languages, architecture, and political institutions. The Roman model of citizenship, republican government, and imperial administration continues to influence modern nations.`,
    excerpt: "Discover the epic story of Ancient Rome, from its legendary founding to its eventual fall, and explore the lasting impact of Roman civilization on the modern world.",
    category: "history",
    author: {
      name: "Prof. Marcus Thompson",
      email: "<EMAIL>",
      bio: "Professor of Ancient History specializing in Roman civilization"
    },
    publishedAt: "2024-01-10T14:30:00.000Z",
    lastModified: "2024-01-10T14:30:00.000Z",
    createdAt: "2024-01-10T14:30:00.000Z",
    status: "published",
    seo: {
      metaDescription: "Explore the rise and fall of Ancient Rome, from republic to empire. Learn about Roman achievements, culture, and lasting influence on modern civilization.",
      keywords: ["Ancient Rome", "Roman Empire", "Roman Republic", "Roman history", "classical civilization"],
      ogTitle: "The Rise and Fall of Ancient Rome: A Complete History",
      ogDescription: "The epic story of Rome from city-state to empire and its enduring legacy."
    },
    featuredImage: "/images/ancient-rome.jpg",
    readingTime: 12,
    tags: ["Ancient History", "Roman Empire", "Classical Civilization", "Mediterranean", "Historical Analysis"],
    viewCount: 0
  },
  {
    id: "biz-sustainable-practices-001",
    title: "Sustainable Business Practices in the Digital Age",
    slug: "sustainable-business-practices-in-the-digital-age",
    content: `As we navigate the 21st century, businesses face unprecedented challenges in balancing profitability with environmental responsibility. The digital age has created new opportunities for sustainable practices while also presenting unique environmental challenges.

The concept of sustainability in business has evolved from a nice-to-have initiative to a critical component of long-term success. Companies that fail to adapt to sustainable practices risk losing customers, investors, and top talent who increasingly prioritize environmental responsibility.

Digital transformation offers powerful tools for sustainability. Cloud computing reduces the need for physical infrastructure, while remote work capabilities decrease commuting-related emissions. Data analytics help optimize resource usage, and IoT sensors enable real-time monitoring of energy consumption.

However, the digital revolution also brings environmental costs. Data centers consume enormous amounts of energy, and the production of electronic devices requires rare earth minerals. The challenge lies in leveraging digital tools to create net positive environmental impact.

Leading companies are pioneering innovative approaches to sustainable business. Microsoft has committed to being carbon negative by 2030, while Patagonia has built its brand around environmental activism. These companies demonstrate that sustainability can be a competitive advantage rather than a burden.

The circular economy model is gaining traction, emphasizing reuse, recycling, and regeneration over the traditional linear "take-make-dispose" approach. Digital platforms facilitate sharing economies, extending product lifecycles and reducing waste.

Investors are increasingly considering Environmental, Social, and Governance (ESG) factors in their decisions. Companies with strong sustainability credentials often enjoy better access to capital and higher valuations.

The regulatory landscape is also evolving, with governments implementing stricter environmental standards and carbon pricing mechanisms. Businesses must adapt to these changes or face significant penalties.

Looking forward, artificial intelligence and machine learning will play crucial roles in optimizing sustainable practices. From smart grids that balance renewable energy supply and demand to AI-powered supply chain optimization, technology will be key to achieving sustainability goals.

The transition to sustainable business practices requires commitment from leadership, investment in new technologies, and a willingness to rethink traditional business models. Companies that embrace this challenge will be best positioned for long-term success in an increasingly environmentally conscious world.`,
    excerpt: "Learn how businesses are leveraging digital technologies to implement sustainable practices, reduce environmental impact, and create competitive advantages in the modern economy.",
    category: "business",
    author: {
      name: "Elena Rodriguez",
      email: "<EMAIL>",
      bio: "Sustainability consultant and former Fortune 500 executive"
    },
    publishedAt: "2024-01-20T09:15:00.000Z",
    lastModified: "2024-01-20T09:15:00.000Z",
    createdAt: "2024-01-20T09:15:00.000Z",
    status: "published",
    seo: {
      metaDescription: "Discover how businesses are using digital technologies for sustainable practices. Learn about ESG investing, circular economy, and green business strategies.",
      keywords: ["sustainable business", "digital transformation", "ESG investing", "circular economy", "green technology"],
      ogTitle: "Sustainable Business Practices in the Digital Age",
      ogDescription: "How companies are balancing profitability with environmental responsibility using digital tools."
    },
    featuredImage: "/images/sustainable-business.jpg",
    readingTime: 10,
    tags: ["Sustainability", "Digital Transformation", "ESG", "Green Business", "Corporate Responsibility"],
    viewCount: 0
  }
];

async function ensureDataDir() {
  try {
    await fs.access(DATA_DIR);
  } catch {
    await fs.mkdir(DATA_DIR, { recursive: true });
  }
}

async function seedDatabase() {
  console.log('Starting database seeding...');
  
  try {
    await ensureDataDir();
    
    // Write articles to JSON file
    await fs.writeFile(ARTICLES_FILE, JSON.stringify(sampleArticles, null, 2));
    
    console.log('\n✅ Database seeding completed successfully!');
    console.log(`Created ${sampleArticles.length} sample articles.`);
    
    // Display category stats
    const categories = ['history', 'business', 'technology', 'politics', 'art', 'social-life'];
    console.log('\n📊 Category Statistics:');
    categories.forEach(category => {
      const count = sampleArticles.filter(a => a.category === category).length;
      const displayName = category.charAt(0).toUpperCase() + category.slice(1).replace('-', ' ');
      console.log(`  ${displayName}: ${count} articles`);
    });
    
  } catch (error) {
    console.error('❌ Error seeding database:', error);
    process.exit(1);
  }
}

// Run the seeding script
if (require.main === module) {
  seedDatabase();
}

module.exports = { seedDatabase };
