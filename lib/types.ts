// Article and Blog Types
export type ArticleStatus = 'draft' | 'published' | 'archived';

export type ArticleCategory = 
  | 'history' 
  | 'business' 
  | 'technology' 
  | 'politics' 
  | 'art' 
  | 'social-life';

export interface Article {
  id: string;
  title: string;
  slug: string;
  content: string;
  excerpt: string;
  category: ArticleCategory;
  author: {
    name: string;
    email?: string;
    bio?: string;
  };
  publishedAt: string; // ISO date string
  lastModified: string; // ISO date string
  createdAt: string; // ISO date string
  status: ArticleStatus;
  seo: {
    metaDescription: string;
    keywords: string[];
    ogTitle?: string;
    ogDescription?: string;
    ogImage?: string;
  };
  featuredImage?: string;
  readingTime?: number; // in minutes
  tags?: string[];
  viewCount?: number;
}

export interface CreateArticleInput {
  title: string;
  content: string;
  excerpt: string;
  category: ArticleCategory;
  author: {
    name: string;
    email?: string;
    bio?: string;
  };
  status?: ArticleStatus;
  seo: {
    metaDescription: string;
    keywords: string[];
    ogTitle?: string;
    ogDescription?: string;
    ogImage?: string;
  };
  featuredImage?: string;
  tags?: string[];
}

export interface UpdateArticleInput extends Partial<CreateArticleInput> {
  id: string;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface ArticleListResponse extends PaginatedResponse<Article> {}

// Query Parameters
export interface ArticleQueryParams {
  page?: number;
  limit?: number;
  category?: ArticleCategory;
  status?: ArticleStatus;
  search?: string;
  sortBy?: 'publishedAt' | 'title' | 'viewCount';
  sortOrder?: 'asc' | 'desc';
}

// Category Information
export interface CategoryInfo {
  name: ArticleCategory;
  displayName: string;
  description: string;
  articleCount: number;
}

// Database/Storage Interface
export interface ArticleStorage {
  getAll(params?: ArticleQueryParams): Promise<PaginatedResponse<Article>>;
  getByCategory(category: ArticleCategory, params?: ArticleQueryParams): Promise<PaginatedResponse<Article>>;
  getBySlug(category: ArticleCategory, slug: string): Promise<Article | null>;
  getById(id: string): Promise<Article | null>;
  create(article: CreateArticleInput): Promise<Article>;
  update(id: string, updates: Partial<UpdateArticleInput>): Promise<Article>;
  delete(id: string): Promise<boolean>;
  getCategoryStats(): Promise<CategoryInfo[]>;
}

// Utility Types
export type ArticlePreview = Pick<Article, 'id' | 'title' | 'slug' | 'excerpt' | 'category' | 'author' | 'publishedAt' | 'featuredImage' | 'readingTime' | 'tags'>;

export type ArticleMetadata = Pick<Article, 'id' | 'title' | 'slug' | 'category' | 'publishedAt' | 'lastModified' | 'status' | 'viewCount'>;
