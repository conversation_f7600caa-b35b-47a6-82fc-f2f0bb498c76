import fs from 'fs/promises';
import path from 'path';
import { 
  Article, 
  CreateArticleInput, 
  UpdateArticleInput, 
  ArticleStorage, 
  ArticleQueryParams, 
  PaginatedResponse, 
  CategoryInfo, 
  ArticleCategory 
} from './types';
import { generateSlug, calculateReadingTime } from './utils';

const DATA_DIR = path.join(process.cwd(), 'data');
const ARTICLES_FILE = path.join(DATA_DIR, 'articles.json');

class JSONArticleStorage implements ArticleStorage {
  private async ensureDataDir(): Promise<void> {
    try {
      await fs.access(DATA_DIR);
    } catch {
      await fs.mkdir(DATA_DIR, { recursive: true });
    }
  }

  private async loadArticles(): Promise<Article[]> {
    await this.ensureDataDir();
    try {
      const data = await fs.readFile(ARTICLES_FILE, 'utf-8');
      return JSON.parse(data);
    } catch {
      return [];
    }
  }

  private async saveArticles(articles: Article[]): Promise<void> {
    await this.ensureDataDir();
    await fs.writeFile(ARTICLES_FILE, JSON.stringify(articles, null, 2));
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  private filterArticles(articles: Article[], params: ArticleQueryParams = {}): Article[] {
    let filtered = [...articles];

    // Filter by status (default to published for public queries)
    if (params.status) {
      filtered = filtered.filter(article => article.status === params.status);
    } else {
      filtered = filtered.filter(article => article.status === 'published');
    }

    // Filter by category
    if (params.category) {
      filtered = filtered.filter(article => article.category === params.category);
    }

    // Filter by search term
    if (params.search) {
      const searchTerm = params.search.toLowerCase();
      filtered = filtered.filter(article => 
        article.title.toLowerCase().includes(searchTerm) ||
        article.excerpt.toLowerCase().includes(searchTerm) ||
        article.content.toLowerCase().includes(searchTerm) ||
        article.tags?.some(tag => tag.toLowerCase().includes(searchTerm))
      );
    }

    // Sort articles
    const sortBy = params.sortBy || 'publishedAt';
    const sortOrder = params.sortOrder || 'desc';
    
    filtered.sort((a, b) => {
      let aValue: any = a[sortBy];
      let bValue: any = b[sortBy];
      
      if (sortBy === 'publishedAt') {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      }
      
      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return filtered;
  }

  private paginateResults<T>(items: T[], page: number = 1, limit: number = 10): PaginatedResponse<T> {
    const total = items.length;
    const totalPages = Math.ceil(total / limit);
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedItems = items.slice(startIndex, endIndex);

    return {
      success: true,
      data: paginatedItems,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
  }

  async getAll(params: ArticleQueryParams = {}): Promise<PaginatedResponse<Article>> {
    const articles = await this.loadArticles();
    const filtered = this.filterArticles(articles, params);
    return this.paginateResults(filtered, params.page, params.limit);
  }

  async getByCategory(category: ArticleCategory, params: ArticleQueryParams = {}): Promise<PaginatedResponse<Article>> {
    const articles = await this.loadArticles();
    const filtered = this.filterArticles(articles, { ...params, category });
    return this.paginateResults(filtered, params.page, params.limit);
  }

  async getBySlug(category: ArticleCategory, slug: string): Promise<Article | null> {
    const articles = await this.loadArticles();
    const article = articles.find(a => a.category === category && a.slug === slug && a.status === 'published');
    
    if (article) {
      // Increment view count
      article.viewCount = (article.viewCount || 0) + 1;
      await this.saveArticles(articles);
    }
    
    return article || null;
  }

  async getById(id: string): Promise<Article | null> {
    const articles = await this.loadArticles();
    return articles.find(a => a.id === id) || null;
  }

  async create(input: CreateArticleInput): Promise<Article> {
    const articles = await this.loadArticles();
    const now = new Date().toISOString();
    
    const article: Article = {
      id: this.generateId(),
      ...input,
      slug: generateSlug(input.title),
      status: input.status || 'draft',
      publishedAt: input.status === 'published' ? now : '',
      lastModified: now,
      createdAt: now,
      readingTime: calculateReadingTime(input.content),
      viewCount: 0
    };

    articles.push(article);
    await this.saveArticles(articles);
    return article;
  }

  async update(id: string, updates: Partial<UpdateArticleInput>): Promise<Article> {
    const articles = await this.loadArticles();
    const index = articles.findIndex(a => a.id === id);
    
    if (index === -1) {
      throw new Error('Article not found');
    }

    const article = articles[index];
    const now = new Date().toISOString();
    
    // Update fields
    Object.assign(article, updates);
    article.lastModified = now;
    
    // Update slug if title changed
    if (updates.title) {
      article.slug = generateSlug(updates.title);
    }
    
    // Update reading time if content changed
    if (updates.content) {
      article.readingTime = calculateReadingTime(updates.content);
    }
    
    // Set published date if status changed to published
    if (updates.status === 'published' && !article.publishedAt) {
      article.publishedAt = now;
    }

    await this.saveArticles(articles);
    return article;
  }

  async delete(id: string): Promise<boolean> {
    const articles = await this.loadArticles();
    const index = articles.findIndex(a => a.id === id);
    
    if (index === -1) {
      return false;
    }

    articles.splice(index, 1);
    await this.saveArticles(articles);
    return true;
  }

  async getCategoryStats(): Promise<CategoryInfo[]> {
    const articles = await this.loadArticles();
    const publishedArticles = articles.filter(a => a.status === 'published');
    
    const categories: ArticleCategory[] = ['history', 'business', 'technology', 'politics', 'art', 'social-life'];
    
    return categories.map(category => ({
      name: category,
      displayName: category.charAt(0).toUpperCase() + category.slice(1).replace('-', ' '),
      description: `Articles about ${category.replace('-', ' ')}`,
      articleCount: publishedArticles.filter(a => a.category === category).length
    }));
  }
}

export const articleStorage = new JSONArticleStorage();
