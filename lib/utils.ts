import { ArticleCategory } from './types';

/**
 * Generate a URL-friendly slug from a title
 */
export function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
}

/**
 * Calculate estimated reading time in minutes
 */
export function calculateReadingTime(content: string): number {
  const wordsPerMinute = 200; // Average reading speed
  const wordCount = content.trim().split(/\s+/).length;
  const readingTime = Math.ceil(wordCount / wordsPerMinute);
  return Math.max(1, readingTime); // Minimum 1 minute
}

/**
 * Validate if a string is a valid article category
 */
export function isValidCategory(category: string): category is ArticleCategory {
  const validCategories: ArticleCategory[] = [
    'history', 
    'business', 
    'technology', 
    'politics', 
    'art', 
    'social-life'
  ];
  return validCategories.includes(category as ArticleCategory);
}

/**
 * Get display name for a category
 */
export function getCategoryDisplayName(category: ArticleCategory): string {
  const displayNames: Record<ArticleCategory, string> = {
    'history': 'History',
    'business': 'Business',
    'technology': 'Technology',
    'politics': 'Politics',
    'art': 'Art',
    'social-life': 'Social Life'
  };
  return displayNames[category];
}

/**
 * Format date for display
 */
export function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

/**
 * Format date for SEO (ISO format)
 */
export function formatDateISO(dateString: string): string {
  return new Date(dateString).toISOString();
}

/**
 * Truncate text to a specified length
 */
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength).trim() + '...';
}

/**
 * Extract excerpt from content if not provided
 */
export function extractExcerpt(content: string, maxLength: number = 160): string {
  // Remove HTML tags and markdown formatting
  const plainText = content
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/[#*_`]/g, '') // Remove basic markdown formatting
    .replace(/\n+/g, ' ') // Replace newlines with spaces
    .trim();
  
  return truncateText(plainText, maxLength);
}

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Generate a random API key
 */
export function generateApiKey(): string {
  return 'ak_' + Math.random().toString(36).substring(2) + Date.now().toString(36);
}

/**
 * Sanitize HTML content (basic implementation)
 */
export function sanitizeHtml(html: string): string {
  // This is a basic implementation. In production, use a proper HTML sanitizer like DOMPurify
  return html
    .replace(/<script[^>]*>.*?<\/script>/gi, '')
    .replace(/<iframe[^>]*>.*?<\/iframe>/gi, '')
    .replace(/on\w+="[^"]*"/gi, ''); // Remove event handlers
}

/**
 * Create SEO-friendly meta description
 */
export function createMetaDescription(content: string, maxLength: number = 160): string {
  const excerpt = extractExcerpt(content, maxLength);
  return excerpt.endsWith('...') ? excerpt : excerpt + '.';
}

/**
 * Validate article slug uniqueness
 */
export function validateSlugFormat(slug: string): boolean {
  const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
  return slugRegex.test(slug) && slug.length >= 3 && slug.length <= 100;
}

/**
 * Parse query parameters for pagination
 */
export function parseQueryParams(query: any) {
  return {
    page: parseInt(query.page) || 1,
    limit: Math.min(parseInt(query.limit) || 10, 50), // Max 50 items per page
    search: query.search || undefined,
    sortBy: query.sortBy || 'publishedAt',
    sortOrder: query.sortOrder === 'asc' ? 'asc' : 'desc'
  };
}

/**
 * Create a standardized API response
 */
export function createApiResponse<T>(
  success: boolean, 
  data?: T, 
  error?: string, 
  message?: string
) {
  return {
    success,
    ...(data !== undefined && { data }),
    ...(error && { error }),
    ...(message && { message })
  };
}

/**
 * Handle API errors consistently
 */
export function handleApiError(error: any) {
  console.error('API Error:', error);
  
  if (error.message) {
    return createApiResponse(false, undefined, error.message);
  }
  
  return createApiResponse(false, undefined, 'An unexpected error occurred');
}
